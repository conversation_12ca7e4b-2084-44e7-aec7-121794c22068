# Test configuration for site-based replication feature
# Redis connection URL for the primary node
redis_url = "redis://settlenxt:ENC:IqAUNVA/KEnxebd8MtvNsMCCLQeYooyUejFT+XFNOQk=@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to (Rust nodes)
secondary_nodes = ["http://127.0.0.1:50052"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 1024

# Number of secondary nodes to which data should be replicated
# 0 means no replication, maximum is the number of secondary nodes
replication_factor = 1

# Number of secondary nodes that must have the data before considering it consistent
# Currently not being referred
read_consistency = 0

# IMPORTANT: Set to true to enable site replication (async replication required for site replication)
# If true, replication to secondary nodes happens asynchronously
# If false, replication happens synchronously without rust nodes. Priamary node connects directly redis
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts when connecting to secondary nodes
# Reduce for faster recovery from failures
retry_delay_ms = 100

# Maximum age in seconds for a replication batch before it's considered expired
# Expired batches are skipped to prevent stale data replication
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 512

# Maximum number of operations in a batch
max_batch_size = 50000

# Interval in milliseconds to flush batches
batch_flush_interval_ms = 200

# Authentication configuration
# Set to true to enable client authentication
auth_enabled = true
# Username for client authentication (required if auth_enabled = true)
auth_username = "testuser"
# Password for client authentication (required if auth_enabled = true)
auth_password = "ENC:5cRbJGkXfn+XNZqYlPTy+vpH0UOz6m6JBQeZCFz9deN3CSBq"
# Session duration in seconds (default: 3600 = 1 hour)
session_duration_secs = 3600
# Authentication mode: "connection_only" or "per_request"
# connection_only: authenticate once per connection, no token validation per request
# per_request: validate session token on each request (default)
auth_mode = "connection_only"

# Enable/disable session token expiry
# true: tokens expire after session_duration_secs (default)
# false: tokens are permanent until application restart
auth_token_expiry_enabled = false

# Write consistency configuration (not used when async_replication = true)
# These settings are only active when async_replication = false
# List of peer Redis nodes for write consistency (when async_replication = false)
# These are direct Redis connections, not Rust nodes
# peer_redis_nodes = ["redis://settlenxt:npci@127.0.0.1:6370", "redis://settlenxt:npci@127.0.0.1:6371"]
peer_redis_nodes = []

# Write consistency level: ALL, QUORUM, or ONE (only used when async_replication = false)
write_consistency = "QUORUM"
# Number of nodes expected for quorum (used when write_consistency = "QUORUM")
quorum_value = 2
# Number of retry attempts for write consistency operations
write_retry_count = 3
# Connection pool size for each peer Redis node (for write consistency)
# This controls how many concurrent connections each peer Redis node can handle
peer_redis_pool_size = 64

# Site replication configuration (disabled by default)
# Enable site-based replication for cross-site data replication
site_replication_enabled = true

# Maximum number of connections per site node in the gRPC client pool
# This creates logical clients sharing a single HTTP/2 connection for efficiency
site_replication_pool_size = 1024

# Primary node for other site (will receive replication with skip_replication=false)
# This is the gRPC endpoint of the primary node in the other site
site_primary_node = "http://127.0.0.1:50053"

# Failover node for other site (used when primary node is unavailable)
# This is the gRPC endpoint of the failover node in the other site
site_failover_node = "http://127.0.0.1:50054"

# Number of retry attempts for site replication operations
site_replication_retry_count = 3

# Timeout in milliseconds for site replication operations
site_replication_timeout_ms = 1000

# Connection pool mode: true = multiple physical TCP connections, false = HTTP/2 multiplexing
# true: Creates actual TCP connections (visible in netstat) - better for monitoring/load balancing
# false: Uses HTTP/2 multiplexing (1 TCP connection, multiple logical clients) - more efficient
use_physical_connections = false

# Performance tuning parameters
# Server configuration
# Sends periodic keepalive probes to maintain connection health
tcp_keepalive_secs = 30
# Disables Nagle's algorithm when set to true
# This reduces latency by sending packets immediately without waiting to fill buffers
tcp_nodelay = true
# Limits the number of concurrent requests per connection
concurrency_limit = 1024
# Controls maximum number of concurrent HTTP/2 streams per connection
max_concurrent_streams = 8192
# Replication chunk size for batch processing
chunk_size = 10000
# Number of shards for batch collectors
num_shards = 128
# Configure worker threads based on the CPU size
# Set to a power of 2 for better scheduling efficiency
worker_threads = 32

# HEALTH CHECK AND KEEP-ALIVE FEATURES:
# Enable Redis connection health checks to keep connections alive during idle periods
redis_keepalive_enabled = true

# Interval in seconds for Redis connection health checks
redis_keepalive_interval_secs = 30

# Enable secondary nodes health checks to keep connections alive during idle periods
secondary_nodes_keepalive_enabled = true

# Interval in seconds for secondary nodes health checks
secondary_nodes_keepalive_interval_secs = 30

# Enable site replication nodes health checks to keep connections alive during idle periods
site_nodes_keepalive_enabled = true

# Interval in seconds for site replication nodes health checks
site_nodes_keepalive_interval_secs = 30

# Enable peer Redis nodes health checks to keep connections alive during idle periods
peer_redis_keepalive_enabled = true

# Interval in seconds for peer Redis nodes health checks
peer_redis_keepalive_interval_secs = 30

# Rate limiting configuration
# Enable/disable rate limiting to prevent abuse and ensure fair resource usage
rate_limiting_enabled = false

# Rate limiting type: "global", "per_client", or "both"
# - "global": Limit total requests across all clients
# - "per_client": Limit requests per individual client
# - "both": Apply both global and per-client limits (both must pass)
rate_limit_type = "global"

# Per-client rate limiting settings (used when rate_limit_type = "per_client" or "both")
# Maximum requests per second per client (default: 1000 RPS per client)
rate_limit_requests_per_second = 10000

# Per-client maximum burst size (token bucket capacity)
# Higher values allow more bursty traffic but may impact performance under sustained load
rate_limit_burst_size = 300

# Global rate limiting settings (used when rate_limit_type = "global" or "both")
# Maximum requests per second across all clients (increased for load testing)
rate_limit_global_requests_per_second = 15000

# Global maximum burst size (token bucket capacity)
# Increased to handle 1000 concurrent connections + sustained load
rate_limit_global_burst_size = 5000

# Cleanup interval for expired client rate limiters (in seconds)
# Clients that haven't made requests for 2x this interval will be removed from memory
# Lower values use less memory but require more CPU for cleanup
rate_limit_cleanup_interval_secs = 300

# HOT CONFIGURATION RELOAD SETTINGS
# How often to check for configuration file changes (hot reloadable)
config_watch_interval_secs = 5  # Check every 5 seconds (can be 1-300 seconds)