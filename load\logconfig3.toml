# Tracing configuration file for RustyCluster

# Log file path
file = "rustycluster3.log"

# Maximum size of log file before rotation (in bytes)
max_size = 10485760  # 10MB

# Maximum number of rotated log files to keep
max_files = 5

# Log level (error, warn, info, debug, trace)
level = "info"

# Log pattern format
# Available variables:
# {d} - Date and time
# {l} - Log level
# {M} - Module name
# {m} - Log message
# {f} - File name
# {L} - Line number
pattern = "{d(%Y-%m-%d %H:%M:%S.%3f)} - {l} - {M} - {m}\n"

# Enable Tokio Console integration (requires console feature)
console_enabled = false