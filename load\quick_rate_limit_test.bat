@echo off
REM Quick Rate Limiting Test - Single scenario

echo 🚀 Quick Rate Limiting Test
echo ============================
echo.

if "%1"=="" (
    echo Usage: quick_rate_limit_test.bat [target_rps]
    echo.
    echo Examples:
    echo   quick_rate_limit_test.bat 800   ^(should NOT be rate limited^)
    echo   quick_rate_limit_test.bat 1200  ^(SHOULD be rate limited^)
    echo.
    echo Default: 1200 RPS ^(above 1000 RPS limit^)
    set TARGET_RPS=1200
) else (
    set TARGET_RPS=%1
)

echo Target RPS: %TARGET_RPS%
echo Expected Limit: 1000 RPS
echo Duration: 10 seconds
echo Clients: 5
echo.

python test_rate_limiting_validation.py --target-rps %TARGET_RPS% --expected-limit 1000 --duration 10 --clients 5

echo.
echo Test completed!
pause
