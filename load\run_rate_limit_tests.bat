@echo off
REM Rate Limiting Test Suite for RustyCluster
REM Tests various scenarios to validate rate limiting functionality

echo 🧪 RustyCluster Rate Limiting Test Suite
echo ==========================================
echo.

echo 📋 Test Scenarios:
echo 1. Below Limit Test (800 RPS) - Should NOT be rate limited
echo 2. At Limit Test (1000 RPS) - Should NOT be rate limited  
echo 3. Above Limit Test (1200 RPS) - SHOULD be rate limited
echo 4. High Load Test (1500 RPS) - SHOULD be heavily rate limited
echo.

echo ⚠️  Make sure RustyCluster is running with config_rate_limit_1000_rps.toml
echo    Command: cargo run --release -- --config config_rate_limit_1000_rps.toml
echo.
pause

REM Test 1: Below the limit (should succeed)
echo.
echo 🟢 Test 1: Below Limit (800 RPS) - Expected: NO rate limiting
echo ================================================================
python test_rate_limiting_validation.py --target-rps 800 --expected-limit 1000 --duration 10 --clients 3
echo.
echo Press any key to continue to next test...
pause

REM Test 2: At the limit (should succeed)
echo.
echo 🟡 Test 2: At Limit (1000 RPS) - Expected: NO rate limiting
echo ===========================================================
python test_rate_limiting_validation.py --target-rps 1000 --expected-limit 1000 --duration 10 --clients 4
echo.
echo Press any key to continue to next test...
pause

REM Test 3: Above the limit (should be rate limited)
echo.
echo 🔴 Test 3: Above Limit (1200 RPS) - Expected: RATE LIMITING
echo ============================================================
python test_rate_limiting_validation.py --target-rps 1200 --expected-limit 1000 --duration 10 --clients 5
echo.
echo Press any key to continue to next test...
pause

REM Test 4: High load (should be heavily rate limited)
echo.
echo 🔴 Test 4: High Load (1500 RPS) - Expected: HEAVY RATE LIMITING
echo ================================================================
python test_rate_limiting_validation.py --target-rps 1500 --expected-limit 1000 --duration 10 --clients 6
echo.

echo.
echo ✅ Rate Limiting Test Suite Completed!
echo.
echo 📊 Summary:
echo - Test 1 (800 RPS): Should show NO rate limiting
echo - Test 2 (1000 RPS): Should show NO rate limiting  
echo - Test 3 (1200 RPS): Should show SOME rate limiting
echo - Test 4 (1500 RPS): Should show HEAVY rate limiting
echo.
echo If rate limiting is working correctly:
echo ✅ Tests 1-2 should PASS with no RESOURCE_EXHAUSTED errors
echo ✅ Tests 3-4 should PASS with RESOURCE_EXHAUSTED errors
echo.
pause
