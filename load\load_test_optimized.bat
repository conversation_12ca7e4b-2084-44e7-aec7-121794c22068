@echo off
REM Optimized load test for RustyCluster
REM Reduced concurrent connections to avoid rate limiting burst issues

cd C:\Users\<USER>\Desktop\starters\ghz-windows-x86_64

echo Starting optimized load test...
echo Target: 10,000 RPS with optimized concurrent connections for global rate limiting
echo.

REM Optimized for global rate limiting: 10,000 RPS with 200 concurrent connections
REM This reduces the initial burst while maintaining high RPS
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 10000 -n 200000 -c 200 --format html --output set_result_optimized.html --metadata-file=../meta.json 127.0.0.1:50051

echo.
echo Load test completed!
echo Check set_result_optimized.html for results.
pause
