@echo off
REM Debug load test for RustyCluster
REM Start with very low load and gradually increase

cd C:\Users\<USER>\Desktop\starters\ghz-windows-x86_64

echo Starting debug load test...
echo.

REM Test 1: Very low load (10 RPS, 5 connections)
echo Test 1: Very low load - 10 RPS with 5 connections
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 10 -n 100 -c 5 --metadata-file=../meta.json 127.0.0.1:50051
echo Test 1 result: %ERRORLEVEL%
echo.

REM Test 2: Low load (100 RPS, 10 connections)
echo Test 2: Low load - 100 RPS with 10 connections
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 100 -n 1000 -c 10 --metadata-file=../meta.json 127.0.0.1:50051
echo Test 2 result: %ERRORLEVEL%
echo.

REM Test 3: Medium load (500 RPS, 25 connections)
echo Test 3: Medium load - 500 RPS with 25 connections
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 500 -n 5000 -c 25 --metadata-file=../meta.json 127.0.0.1:50051
echo Test 3 result: %ERRORLEVEL%
echo.

REM Test 4: Target load (1000 RPS, 50 connections)
echo Test 4: Target load - 1000 RPS with 50 connections
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 1000 -n 10000 -c 50 --format html --output debug_result.html --metadata-file=../meta.json 127.0.0.1:50051
echo Test 4 result: %ERRORLEVEL%
echo.

echo Debug load test completed!
echo Check debug_result.html for detailed results.
pause
