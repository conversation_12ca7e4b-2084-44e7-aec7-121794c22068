@echo off
REM Gradual ramp-up load test for RustyCluster
REM This script gradually increases load to avoid overwhelming rate limiting

cd C:\Users\<USER>\Desktop\starters\ghz-windows-x86_64

echo Starting gradual load test...
echo.

REM Phase 1: Warm-up (1000 RPS, 100 connections)
echo Phase 1: Warm-up - 1000 RPS with 100 connections
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 1000 -n 10000 -c 100 --metadata-file=../meta.json 127.0.0.1:50051
echo.

REM Phase 2: Medium load (5000 RPS, 300 connections)
echo Phase 2: Medium load - 5000 RPS with 300 connections
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 5000 -n 25000 -c 300 --metadata-file=../meta.json 127.0.0.1:50051
echo.

REM Phase 3: High load (10000 RPS, 200 connections) - Optimized for global rate limiting
echo Phase 3: High load - 10000 RPS with 200 connections
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 10000 -n 100000 -c 200 --format html --output set_result_10k.html --metadata-file=../meta.json 127.0.0.1:50051
echo.

REM Phase 4: Maximum load (12000 RPS, 250 connections) - Optimized for global rate limiting
echo Phase 4: Maximum load - 12000 RPS with 250 connections
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 12000 -n 120000 -c 250 --format html --output set_result_12k.html --metadata-file=../meta.json 127.0.0.1:50051

echo.
echo Load test completed!
echo Check set_result_10k.html and set_result_12k.html for results.
pause
