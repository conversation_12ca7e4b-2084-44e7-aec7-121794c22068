# RustyCluster User Manual

## Table of Contents
1. [Overview](#overview)
2. [Environment Setup](#environment-setup)
3. [Configuration Details](#configuration-details)
4. [Fine-Tuning & Optimization](#fine-tuning--optimization)
5. [Security Configuration](#security-configuration)
6. [Monitoring & Logging](#monitoring--logging)
7. [Deployment Guide](#deployment-guide)
8. [Troubleshooting](#troubleshooting)

## Overview

RustyCluster is a high-performance distributed key-value store built in Rust, designed for scalability, reliability, and performance. It provides:

- **Distributed Architecture**: Multi-node cluster with primary/secondary replication
- **High Performance**: 10,000+ RPS capability with optimized configurations
- **Redis Backend**: Uses Redis as the underlying storage engine
- **gRPC Communication**: Efficient inter-node communication protocol
- **Authentication & Security**: Built-in authentication with encrypted password support
- **Hot Configuration Reload**: Runtime configuration updates without restarts
- **Comprehensive Monitoring**: Structured logging with Tokio Console integration

### Key Features
- Multi-level replication (local nodes + site-level replication)
- Write consistency guarantees (ALL/QUORUM/ONE)
- Rate limiting with configurable policies
- Health checks and automatic failover
- Batch operations for improved performance
- Comprehensive API with 20+ Redis operations

## Environment Setup

### Prerequisites by Environment

#### System Requirements (All Environments)
- **Operating System**: Windows 10/11, Linux (Ubuntu 18.04+), macOS 10.15+
- **CPU**: Minimum 4 cores, Recommended 8+ cores for high performance
- **Memory**: Minimum 4GB RAM, Recommended 16GB+ for production
- **Storage**: SSD recommended for optimal performance
- **Network**: Gigabit Ethernet for multi-node deployments

#### Development Environment Prerequisites
Required for building and developing RustyCluster:

- **Rust**: Latest stable version (1.70+)
  ```bash
  curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
  source ~/.cargo/env
  ```

- **Protocol Buffers Compiler**: For gRPC code generation
  ```bash
  # Ubuntu/Debian
  sudo apt install protobuf-compiler

  # CentOS/RHEL
  sudo yum install protobuf-compiler

  # macOS
  brew install protobuf

  # Windows
  # Download from https://github.com/protocolbuffers/protobuf/releases
  ```

- **Redis Server**: Version 6.0+ with authentication support
  ```bash
  # Ubuntu/Debian
  sudo apt update && sudo apt install redis-server

  # CentOS/RHEL
  sudo yum install redis

  # macOS
  brew install redis

  # Windows
  # Download from https://github.com/microsoftarchive/redis/releases
  ```

#### Test/Production Environment Prerequisites
Required for running pre-built RustyCluster binaries:

- **Redis Server**: Version 6.0+ with authentication support (same as dev)
- **RustyCluster Binary**: Pre-compiled binary from releases or CI/CD pipeline
- **Configuration Files**: `config.toml` and `logconfig.toml`
- **Network Access**: Ports 50051 (gRPC), 6379+ (Redis) accessible

#### Optional Tools (All Environments)
- **Tokio Console**: For runtime profiling and debugging
  ```bash
  cargo install --locked tokio-console
  ```

- **Load Testing Tools**: For performance validation
  ```bash
  # Python dependencies for load testing scripts
  pip install grpcio grpcio-tools asyncio aiogrpc

  # ghz for gRPC load testing (optional)
  # Download from https://github.com/bojand/ghz/releases
  ```

- **Monitoring Tools**: For production monitoring
  ```bash
  # System monitoring
  sudo apt install htop iftop netstat-nat

  # Log analysis
  sudo apt install jq grep awk
  ```

### Installation Steps

1. **Clone the Repository**
   ```bash
   git clone https://github.com/yourusername/rustycluster.git
   cd rustycluster
   ```

2. **Build the Application**
   ```bash
   # Standard build
   cargo build --release
   
   # Build with Tokio Console support (optional)
   cargo build --release --features console
   ```

3. **Verify Installation**
   ```bash
   # Check if binary was created
   ls -la target/release/rustycluster
   
   # Run version check
   ./target/release/rustycluster --help
   ```

### Redis Setup

#### Single Node Setup
```bash
# Start Redis with authentication
redis-server --port 6379 --requirepass "your_secure_password"

# Test connection
redis-cli -p 6379 -a "your_secure_password" ping
```

#### Multi-Node Redis Setup
```bash
# Primary Redis instance
redis-server --port 6379 --requirepass "your_secure_password"

# Secondary Redis instances
redis-server --port 6370 --requirepass "your_secure_password"
redis-server --port 6371 --requirepass "your_secure_password"
```

#### Redis Configuration Recommendations
```bash
# /etc/redis/redis.conf or redis.conf
bind 0.0.0.0                    # Allow external connections
port 6379                       # Default port
requirepass your_secure_password # Enable authentication
maxmemory 2gb                   # Set memory limit
maxmemory-policy allkeys-lru    # Eviction policy
save 900 1                      # Persistence settings
save 300 10
save 60 10000
```

### Verification

#### Test Redis Connection
```bash
redis-cli -h 127.0.0.1 -p 6379 -a "your_password" ping
# Expected output: PONG
```

#### Test RustyCluster Build
```bash
# Create minimal config file
cat > test_config.toml << EOF
redis_url = "redis://username:password@127.0.0.1:6379"
secondary_nodes = []
port = 50051
redis_pool_size = 10
replication_factor = 0
read_consistency = 0
async_replication = true
max_retries = 3
retry_delay_ms = 100
auth_enabled = false
EOF

# Test run (should start without errors)
cargo run --release test_config.toml
```

#### Environment Validation Checklist
- [ ] Rust compiler installed and working
- [ ] Redis server running and accessible
- [ ] Protocol buffer compiler available
- [ ] RustyCluster builds successfully
- [ ] Basic configuration file loads without errors
- [ ] Network ports (50051, 6379) are available
- [ ] Sufficient system resources available

## Configuration Details

RustyCluster uses TOML configuration files for all settings. The application supports two configuration files:
- **Main Configuration** (`config.toml`): Application settings
- **Logging Configuration** (`logconfig.toml`): Logging and tracing settings

### Core Configuration Parameters

#### Redis Connection Settings

| Parameter | Type | Default | Range/Options | Description | Environment |
|-----------|------|---------|---------------|-------------|-------------|
| `redis_url` | String | Required | Redis URL format | Redis connection URL with authentication | All |
| `redis_pool_size` | Integer | 1024 | 1-4096 | Connection pool size (affects performance) | All |

**Example:**
```toml
redis_url = "redis://username:password@127.0.0.1:6379"
redis_pool_size = 1024
```

#### Network & Server Settings

| Parameter | Type | Default | Range/Options | Description | Environment |
|-----------|------|---------|---------------|-------------|-------------|
| `port` | Integer | 50051 | 1024-65535 | gRPC server listening port | All |
| `tcp_keepalive_secs` | Integer | 30 | 10-300 | TCP keepalive interval in seconds | All |
| `tcp_nodelay` | Boolean | true | true/false | Disable Nagle's algorithm for lower latency | All |
| `use_physical_connections` | Boolean | false | true/false | Use physical TCP connections vs HTTP/2 multiplexing | All |

**Example:**
```toml
port = 50051
tcp_keepalive_secs = 30
tcp_nodelay = true
use_physical_connections = true
```

#### Replication Configuration

| Parameter | Type | Default | Range/Options | Description | Environment |
|-----------|------|---------|---------------|-------------|-------------|
| `secondary_nodes` | Array | [] | List of URLs | List of secondary nodes for replication | All |
| `replication_factor` | Integer | 0 | 0-N | Number of secondary nodes to replicate to | All |
| `read_consistency` | Integer | 0 | 0-N | Required replicas for read consistency | All |
| `async_replication` | Boolean | true | true/false | Asynchronous vs synchronous replication | All |
| `secondary_pool_size` | Integer | 512 | 1-2048 | Connection pool size for secondary nodes | All |

**Example:**
```toml
secondary_nodes = ["http://127.0.0.1:50052", "http://127.0.0.1:50053"]
replication_factor = 2
read_consistency = 1
async_replication = true
secondary_pool_size = 512
```

#### Performance Tuning Parameters

| Parameter | Type | Default | Range/Options | Description | Environment |
|-----------|------|---------|---------------|-------------|-------------|
| `max_retries` | Integer | 3 | 1-10 | Maximum retry attempts for failed operations | All |
| `retry_delay_ms` | Integer | 100 | 10-5000 | Delay between retry attempts in milliseconds | All |
| `max_batch_size` | Integer | 1000 | 1-100000 | Maximum operations per batch | All |
| `batch_flush_interval_ms` | Integer | 100 | 1-10000 | Batch flush interval in milliseconds | All |
| `chunk_size` | Integer | 1000 | 100-100000 | Chunk size for parallel processing | All |
| `concurrency_limit` | Integer | 1000 | 100-8192 | Maximum concurrent requests per connection | All |
| `max_concurrent_streams` | Integer | 2048 | 100-16384 | Maximum HTTP/2 streams per connection | All |
| `worker_threads` | Integer | num_cpus | 1-64 | Number of worker threads for async runtime | All |

**Example:**
```toml
max_retries = 3
retry_delay_ms = 100
max_batch_size = 1000
batch_flush_interval_ms = 100
chunk_size = 1000
concurrency_limit = 1000
max_concurrent_streams = 2048
worker_threads = 8
```

#### Authentication Configuration

| Parameter | Type | Default | Range/Options | Description | Environment |
|-----------|------|---------|---------------|-------------|-------------|
| `auth_enabled` | Boolean | false | true/false | Enable/disable authentication | All |
| `auth_username` | String | "" | Any string | Authentication username | All |
| `auth_password` | String | "" | Plain or ENC:encrypted | Authentication password (can be encrypted) | All |
| `session_duration_secs` | Integer | 3600 | 60-86400 | Session duration in seconds (1 hour default) | All |
| `auth_token_expiry_enabled` | Boolean | true | true/false | Enable session token expiry | All |
| `auth_mode` | String | "connection_only" | "connection_only", "per_request" | Authentication mode | All |

**Example:**
```toml
auth_enabled = true
auth_username = "admin"
auth_password = "ENC:encrypted_password_here"
session_duration_secs = 3600
auth_token_expiry_enabled = true
auth_mode = "connection_only"
```

#### Rate Limiting Configuration

| Parameter | Type | Default | Range/Options | Description | Environment |
|-----------|------|---------|---------------|-------------|-------------|
| `rate_limiting_enabled` | Boolean | false | true/false | Enable/disable rate limiting | All |
| `rate_limit_type` | String | "per_client" | "global", "per_client", "both" | Type of rate limiting to apply | All |
| `rate_limit_rps` | Integer | 1000 | 1-100000 | Per-client requests per second limit | All |
| `rate_limit_burst` | Integer | 100 | 1-10000 | Per-client burst capacity | All |
| `rate_limit_window_secs` | Integer | 1 | 1-60 | Time window for rate limiting in seconds | All |
| `global_rate_limit_rps` | Integer | 10000 | 1-1000000 | Global requests per second limit | All |
| `global_rate_limit_burst` | Integer | 1000 | 1-100000 | Global burst capacity | All |

**Example:**
```toml
rate_limiting_enabled = true
rate_limit_type = "both"
rate_limit_rps = 1000
rate_limit_burst = 100
rate_limit_window_secs = 1
global_rate_limit_rps = 10000
global_rate_limit_burst = 1000
```

#### Site Replication Configuration

| Parameter | Type | Default | Range/Options | Description | Environment |
|-----------|------|---------|---------------|-------------|-------------|
| `site_replication_enabled` | Boolean | false | true/false | Enable site-level replication for disaster recovery | All |
| `site_replication_timeout_ms` | Integer | 1000 | 100-30000 | Timeout for site replication operations in milliseconds | All |
| `site_replication_retries` | Integer | 3 | 1-10 | Number of retry attempts for failed site replication | All |
| `site_replication_batch_size` | Integer | 100 | 1-10000 | Batch size for site replication operations | All |

**Site Replication Nodes Array:**
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `site_name` | String | Yes | Unique identifier for the site |
| `primary_node` | String | Yes | Primary node URL for the site |
| `failover_node` | String | Yes | Failover node URL for the site |

**Example:**
```toml
site_replication_enabled = true
site_replication_timeout_ms = 1000
site_replication_retries = 3
site_replication_batch_size = 100

site_replication_nodes = [
    {
        site_name = "site2",
        primary_node = "http://site2-primary:50051",
        failover_node = "http://site2-failover:50051"
    },
    {
        site_name = "site3",
        primary_node = "http://site3-primary:50051",
        failover_node = "http://site3-failover:50051"
    }
]
```

#### Write Consistency Configuration

| Parameter | Type | Default | Range/Options | Description | Environment |
|-----------|------|---------|---------------|-------------|-------------|
| `write_consistency` | String | "ONE" | "ALL", "QUORUM", "ONE" | Write consistency level (only when async_replication = false) | All |
| `quorum_value` | Integer | 2 | 1-N | Required replicas for QUORUM mode | All |
| `write_retry_count` | Integer | 3 | 1-10 | Number of retry attempts for write operations | All |
| `peer_redis_nodes` | Array | [] | List of Redis URLs | Direct Redis nodes for write consistency | All |
| `peer_redis_pool_size` | Integer | 128 | 1-2048 | Connection pool size for peer Redis nodes | All |

**Example:**
```toml
write_consistency = "QUORUM"
quorum_value = 2
write_retry_count = 3
peer_redis_nodes = [
    "redis://username:password@127.0.0.1:6370",
    "redis://username:password@127.0.0.1:6371"
]
peer_redis_pool_size = 128
```

#### Health Check Configuration

| Parameter | Type | Default | Range/Options | Description | Environment |
|-----------|------|---------|---------------|-------------|-------------|
| `health_check_enabled` | Boolean | true | true/false | Enable health monitoring | All |
| `health_check_interval_secs` | Integer | 30 | 5-300 | Health check interval in seconds | All |
| `health_check_timeout_ms` | Integer | 5000 | 1000-30000 | Health check timeout in milliseconds | All |
| `health_check_on_idle_only` | Boolean | true | true/false | Perform health checks only during idle periods | All |
| `enable_automatic_failover` | Boolean | false | true/false | Enable automatic failover on health check failures | All |
| `failover_detection_timeout_ms` | Integer | 1000 | 500-10000 | Timeout for failover detection in milliseconds | All |
| `recovery_retry_interval_ms` | Integer | 5000 | 1000-60000 | Retry interval for recovery attempts in milliseconds | All |

**Example:**
```toml
health_check_enabled = true
health_check_interval_secs = 30
health_check_timeout_ms = 5000
health_check_on_idle_only = true
enable_automatic_failover = true
failover_detection_timeout_ms = 1000
recovery_retry_interval_ms = 5000
```

#### Hot Configuration Reload

| Parameter | Type | Default | Range/Options | Description | Environment |
|-----------|------|---------|---------------|-------------|-------------|
| `hot_reload_enabled` | Boolean | true | true/false | Enable hot configuration reload | All |
| `config_watch_interval_secs` | Integer | 5 | 1-300 | Configuration file monitoring interval in seconds | All |

**Example:**
```toml
hot_reload_enabled = true
config_watch_interval_secs = 5
```

### Configuration File Examples

#### Development Configuration
```toml
# config_dev.toml - Development environment
redis_url = "redis://127.0.0.1:6379"
secondary_nodes = []
port = 50051
redis_pool_size = 10
replication_factor = 0
async_replication = true
auth_enabled = false
rate_limiting_enabled = false
worker_threads = 4
```

#### Production Configuration
```toml
# config_prod.toml - Production environment
redis_url = "redis://username:ENC:encrypted_password@redis-primary:6379"
secondary_nodes = [
    "http://node2:50051",
    "http://node3:50051"
]
port = 50051
redis_pool_size = 2048
secondary_pool_size = 1024
replication_factor = 2
read_consistency = 1
async_replication = true
max_retries = 3
retry_delay_ms = 50

# Authentication
auth_enabled = true
auth_username = "admin"
auth_password = "ENC:encrypted_admin_password"
session_duration_secs = 7200
auth_mode = "connection_only"

# Rate limiting
rate_limiting_enabled = true
rate_limit_rps = 5000
rate_limit_burst = 500
rate_limit_type = "per_client"

# Performance tuning
tcp_keepalive_secs = 15
tcp_nodelay = true
use_physical_connections = true
concurrency_limit = 4096
max_concurrent_streams = 8192
worker_threads = 16

# Health checks
health_check_enabled = true
health_check_interval_secs = 30
health_check_on_idle_only = true
enable_automatic_failover = true

# Site replication
site_replication_enabled = true
site_replication_nodes = [
    {
        site_name = "backup_site",
        primary_node = "http://backup-primary:50051",
        failover_node = "http://backup-failover:50051"
    }
]
```

#### High-Performance Configuration
```toml
# config_performance.toml - Maximum performance
redis_url = "redis://username:password@127.0.0.1:6379"
secondary_nodes = ["http://127.0.0.1:50052", "http://127.0.0.1:50053"]
port = 50051

# Maximize connection pools
redis_pool_size = 4096
secondary_pool_size = 2048

# Optimize for throughput
replication_factor = 0  # Disable for max performance
async_replication = true
max_retries = 2
retry_delay_ms = 10

# Batch optimization
max_batch_size = 100000
batch_flush_interval_ms = 10
chunk_size = 100000

# Network optimization
tcp_keepalive_secs = 10
tcp_nodelay = true
use_physical_connections = true
concurrency_limit = 8192
max_concurrent_streams = 16384

# CPU optimization
worker_threads = 32
num_shards = 256

# Authentication (optimized)
auth_enabled = true
auth_username = "testuser"
auth_password = "testpass"
session_duration_secs = 7200
auth_mode = "connection_only"
auth_token_expiry_enabled = false

# Disable features that impact performance
rate_limiting_enabled = false
health_check_on_idle_only = true
```

## Fine-Tuning & Optimization

### Performance Optimization Guidelines

#### CPU Optimization
```toml
# Worker thread configuration
worker_threads = 16        # Set to 2x CPU cores for I/O intensive workloads
                          # Set to 1x CPU cores for CPU intensive workloads

# Sharding for parallel processing
num_shards = 256          # Higher values improve parallelism
                          # Recommended: 16-512 based on workload
```

#### Memory Optimization
```toml
# Connection pool sizing
redis_pool_size = 2048    # Balance between memory usage and performance
                          # Formula: expected_concurrent_requests * 1.5

secondary_pool_size = 1024 # Typically 50% of redis_pool_size

# Batch processing to reduce memory allocation
max_batch_size = 10000    # Larger batches = better throughput, more memory
chunk_size = 10000        # Should match or be smaller than max_batch_size
```

#### Network Optimization
```toml
# TCP settings for low latency
tcp_nodelay = true        # Disable Nagle's algorithm for lower latency
tcp_keepalive_secs = 15   # Shorter intervals for faster failure detection

# Connection management
use_physical_connections = true  # Better for monitoring and load balancing
concurrency_limit = 4096         # Limit concurrent requests per connection
max_concurrent_streams = 8192    # HTTP/2 stream limit
```

#### I/O Optimization
```toml
# Reduce retry overhead
max_retries = 2           # Lower retries for faster failure detection
retry_delay_ms = 10       # Shorter delays for faster recovery

# Batch processing intervals
batch_flush_interval_ms = 10  # Shorter intervals for lower latency
replication_batch_max_age_secs = 30  # Longer for better batching
```

### Performance Tuning by Use Case

#### High Throughput (10K+ RPS)
```toml
# Maximize connection pools
redis_pool_size = 4096
secondary_pool_size = 2048

# Optimize batching
max_batch_size = 100000
batch_flush_interval_ms = 10

# Maximize concurrency
concurrency_limit = 8192
max_concurrent_streams = 16384
worker_threads = 32

# Minimize replication overhead
replication_factor = 0    # Disable for pure throughput testing
async_replication = true

# Optimize authentication
auth_mode = "connection_only"
auth_token_expiry_enabled = false
```

#### Low Latency
```toml
# Minimize connection pool overhead
redis_pool_size = 512
secondary_pool_size = 256

# Fast batch processing
max_batch_size = 100
batch_flush_interval_ms = 1

# Network optimization
tcp_nodelay = true
tcp_keepalive_secs = 10

# Fast retries
max_retries = 1
retry_delay_ms = 5

# Immediate replication
async_replication = false
write_consistency = "ONE"
```

#### High Availability
```toml
# Robust replication
replication_factor = 2
read_consistency = 2
async_replication = true

# Comprehensive health checks
health_check_enabled = true
health_check_interval_secs = 15
enable_automatic_failover = true

# Site replication for disaster recovery
site_replication_enabled = true
site_replication_timeout_ms = 500
site_replication_retries = 5

# Conservative retry settings
max_retries = 5
retry_delay_ms = 200
```

#### Memory Constrained Environments
```toml
# Smaller connection pools
redis_pool_size = 128
secondary_pool_size = 64

# Smaller batch sizes
max_batch_size = 1000
chunk_size = 1000

# Fewer worker threads
worker_threads = 4
num_shards = 16

# Shorter session durations
session_duration_secs = 1800
```

### Resource Tuning Guidelines

#### Memory Usage Estimation
```
Total Memory = (redis_pool_size + secondary_pool_size * num_secondary_nodes) * connection_overhead
             + (worker_threads * thread_stack_size)
             + (max_batch_size * average_operation_size)
             + application_overhead

Where:
- connection_overhead ≈ 64KB per connection
- thread_stack_size ≈ 2MB per thread
- average_operation_size ≈ 1KB per operation
- application_overhead ≈ 100MB base
```

#### CPU Usage Guidelines
```
CPU Cores Needed = worker_threads + (replication_factor * 0.5) + 2 (for system)

Recommended configurations:
- 4 cores: worker_threads = 4, replication_factor ≤ 2
- 8 cores: worker_threads = 8, replication_factor ≤ 4
- 16 cores: worker_threads = 16, replication_factor ≤ 8
- 32+ cores: worker_threads = 32, replication_factor unlimited
```

#### Network Bandwidth Planning
```
Bandwidth = (RPS * average_request_size * (1 + replication_factor)) * 8 bits/byte

Example for 10K RPS with 1KB requests and replication_factor=2:
Bandwidth = 10,000 * 1,024 * (1 + 2) * 8 = 245 Mbps
```

### Monitoring Performance

#### Key Performance Metrics
- **Throughput**: Requests per second (RPS)
- **Latency**: P50, P95, P99 response times
- **Error Rate**: Failed requests percentage
- **Resource Usage**: CPU, memory, network utilization
- **Connection Pool**: Active/idle connections

#### Performance Benchmarking
```bash
# Basic throughput test
cd load
python loadtest_async.py --operations 100000 --concurrency 1000 --rps 5000

# Latency test
python loadtest_async.py --operations 10000 --concurrency 100 --rps 1000

# Stress test
python loadtest_async.py --operations 1000000 --concurrency 2000 --rps 10000
```

#### Performance Troubleshooting

**Low Throughput Issues:**
1. Check connection pool sizes
2. Verify worker thread count
3. Monitor CPU and memory usage
4. Check network bandwidth
5. Review batch processing settings

**High Latency Issues:**
1. Enable tcp_nodelay
2. Reduce batch_flush_interval_ms
3. Check Redis response times
4. Monitor network latency
5. Review retry settings

**Memory Issues:**
1. Reduce connection pool sizes
2. Lower max_batch_size
3. Decrease worker_threads
4. Enable connection recycling
5. Monitor for memory leaks

## Security Configuration

### Password Encryption

RustyCluster supports encrypted passwords in configuration files to enhance security.

#### Setting Up Encryption

1. **Set Master Key Environment Variable**
   ```bash
   # Windows (PowerShell)
   $env:RUSTYCLUSTER_MASTER_KEY="your_secure_master_key_here"

   # Windows (Command Prompt)
   set RUSTYCLUSTER_MASTER_KEY=your_secure_master_key_here

   # Linux/macOS
   export RUSTYCLUSTER_MASTER_KEY="your_secure_master_key_here"
   ```

2. **Encrypt Passwords**
   ```bash
   # Use the built-in encryption utility
   cargo run --bin encrypt_password
   ```

   Example session:
   ```
   RustyCluster Password Encryption Utility
   ========================================

   ✓ Using RUSTYCLUSTER_MASTER_KEY environment variable

   Enter password to encrypt (or 'quit' to exit): mypassword123

   ✓ Password encrypted successfully!
   Original:  mypassword123
   Encrypted: ENC:SGVsbG9Xb3JsZA==

   Copy the encrypted value to your configuration file.
   ```

3. **Update Configuration Files**
   ```toml
   # Before (plain text)
   auth_password = "mypassword123"
   redis_url = "redis://user:mypassword123@localhost:6379"

   # After (encrypted)
   auth_password = "ENC:SGVsbG9Xb3JsZA=="
   redis_url = "redis://user:ENC:SGVsbG9Xb3JsZA==@localhost:6379"
   ```

#### Supported Encrypted Fields
- `auth_password` - Client authentication password
- `redis_url` - Redis connection password (within URL)
- `peer_redis_nodes` - Peer Redis connection passwords
- Any password field in site replication node configurations

#### Decryption Utility
```bash
# Decrypt passwords for verification
cargo run --bin decrypt_password

# Enter encrypted password when prompted
Enter encrypted password: ENC:SGVsbG9Xb3JsZA==
Decrypted: mypassword123
```

### Authentication Configuration

#### Authentication Modes

**Connection-Only Authentication** (Recommended for performance)
```toml
auth_enabled = true
auth_mode = "connection_only"
auth_username = "admin"
auth_password = "ENC:encrypted_password"
session_duration_secs = 3600
auth_token_expiry_enabled = true
```

**Per-Request Authentication** (Higher security)
```toml
auth_enabled = true
auth_mode = "per_request"
auth_username = "admin"
auth_password = "ENC:encrypted_password"
session_duration_secs = 1800  # Shorter sessions for per-request mode
auth_token_expiry_enabled = true
```

#### Session Management
```toml
# Session duration (seconds)
session_duration_secs = 3600    # 1 hour default
                                # Range: 60-86400 (1 minute to 24 hours)

# Token expiry
auth_token_expiry_enabled = true  # Enable automatic token expiration
                                 # false = tokens valid until restart

# Session cleanup
# Automatic cleanup runs every session_duration_secs/4 interval
```

#### Client Authentication Flow

1. **Authenticate to get session token**
   ```bash
   # Using grpcurl
   grpcurl -plaintext -d '{"username":"admin","password":"secure_password"}' \
     127.0.0.1:50051 rustycluster.KeyValueService.Authenticate
   ```

2. **Use session token in subsequent requests**
   ```bash
   # Add authorization header with Bearer token
   grpcurl -plaintext -H "authorization: Bearer your-session-token" \
     -d '{"key":"test","value":"data"}' \
     127.0.0.1:50051 rustycluster.KeyValueService.Set
   ```

### Network Security

#### TLS Configuration (Future Enhancement)
```toml
# TLS settings (planned feature)
tls_enabled = false
tls_cert_file = "/path/to/cert.pem"
tls_key_file = "/path/to/key.pem"
tls_ca_file = "/path/to/ca.pem"
```

#### Network Access Control
```toml
# Bind to specific interface (security)
bind_address = "127.0.0.1"  # Localhost only
# bind_address = "0.0.0.0"  # All interfaces (less secure)

# Port configuration
port = 50051  # Change default port for security through obscurity
```

#### Firewall Configuration
```bash
# Linux iptables example
sudo iptables -A INPUT -p tcp --dport 50051 -s trusted_ip_range -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 50051 -j DROP

# Allow Redis access only from RustyCluster nodes
sudo iptables -A INPUT -p tcp --dport 6379 -s 127.0.0.1 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 6379 -j DROP
```

### Redis Security

#### Redis Authentication
```bash
# Redis configuration
requirepass your_secure_redis_password
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG ""
```

#### Redis Network Security
```bash
# Bind Redis to specific interface
bind 127.0.0.1 ********  # Internal network only

# Disable dangerous commands
rename-command SHUTDOWN SHUTDOWN_SECRET_KEY
rename-command EVAL ""
```

### Security Best Practices

#### Production Security Checklist
- [ ] Use encrypted passwords in all configuration files
- [ ] Set strong master key via environment variable
- [ ] Enable authentication with strong credentials
- [ ] Use connection-only auth mode for performance
- [ ] Configure appropriate session durations
- [ ] Bind services to specific network interfaces
- [ ] Configure firewall rules for network access
- [ ] Secure Redis with authentication and command restrictions
- [ ] Use TLS for inter-node communication (when available)
- [ ] Regularly rotate passwords and session keys
- [ ] Monitor authentication logs for suspicious activity
- [ ] Implement network segmentation for multi-tier deployments

#### Security Monitoring
```toml
# Enable security-related logging
level = "info"  # Captures authentication events
pattern = "{d(%Y-%m-%d %H:%M:%S.%3f)} - {l} - {M} - {m}\n"

# Monitor these log patterns:
# - Authentication failures
# - Session token usage
# - Connection attempts
# - Configuration changes
```

## Monitoring & Logging

### Logging Configuration

RustyCluster uses the `tracing` crate for structured, high-performance logging.

#### Log Configuration File (`logconfig.toml`)
```toml
# Log file settings
file = "rustycluster.log"           # Log file path
max_size = 10485760                 # 10MB file size limit
max_files = 5                       # Keep 5 rotated files

# Log level (error, warn, info, debug, trace)
level = "info"                      # Default: info

# Log format pattern
pattern = "{d(%Y-%m-%d %H:%M:%S.%3f)} - {l} - {M} - {m}\n"

# Tokio Console integration
console_enabled = false             # Enable for profiling
```

#### Log Levels and Usage

**Error Level** - Critical issues requiring immediate attention
```toml
level = "error"
# Logs: System failures, connection errors, authentication failures
```

**Warn Level** - Important issues that don't stop operation
```toml
level = "warn"
# Logs: Performance degradation, retry attempts, configuration warnings
```

**Info Level** - General operational information (recommended for production)
```toml
level = "info"
# Logs: Startup/shutdown, configuration changes, authentication events
```

**Debug Level** - Detailed diagnostic information
```toml
level = "debug"
# Logs: Request/response details, connection pool status, replication events
```

**Trace Level** - Very detailed debugging (performance impact)
```toml
level = "trace"
# Logs: Function entry/exit, detailed timing, internal state changes
```

#### Environment Variable Override
```bash
# Override log level at runtime
RUST_LOG=debug cargo run --release config.toml

# Module-specific logging
RUST_LOG=rustycluster::grpc=debug,rustycluster::redis_lib=info cargo run --release config.toml
```

### Tokio Console Integration

Tokio Console provides real-time profiling and debugging capabilities.

#### Setup Tokio Console

1. **Build with console feature**
   ```bash
   cargo build --release --features console
   ```

2. **Enable in log configuration**
   ```toml
   # logconfig.toml
   console_enabled = true
   ```

3. **Run with tokio_unstable flag**
   ```bash
   RUSTFLAGS="--cfg tokio_unstable" cargo run --release --features console config.toml
   ```

4. **Start Tokio Console in another terminal**
   ```bash
   # Install tokio-console if not already installed
   cargo install --locked tokio-console

   # Connect to running application
   tokio-console
   ```

### Performance Monitoring

#### Key Metrics to Monitor

**Throughput Metrics:**
- Requests per second (RPS)
- Operations per second by type (Set, Get, Delete, etc.)
- Batch operation efficiency
- Replication throughput

**Latency Metrics:**
- Request processing time (P50, P95, P99)
- Redis operation latency
- Replication latency
- Authentication latency

**Resource Metrics:**
- Connection pool utilization
- Memory usage
- CPU utilization
- Network bandwidth

**Error Metrics:**
- Failed request rate
- Connection failures
- Authentication failures
- Replication failures

### Health Monitoring

#### Health Check Configuration
```toml
# Health check settings
health_check_enabled = true
health_check_interval_secs = 30
health_check_timeout_ms = 5000
health_check_on_idle_only = true
```

#### Health Check Endpoints
```bash
# Check application health
grpcurl -plaintext 127.0.0.1:50051 rustycluster.KeyValueService.HealthCheck

# Check specific node health
grpcurl -plaintext 127.0.0.1:50052 rustycluster.KeyValueService.HealthCheck
```

## Deployment Guide

### Single Node Deployment

#### Development Environment
```bash
# 1. Start Redis
redis-server --port 6379

# 2. Create basic configuration
cat > config_dev.toml << EOF
redis_url = "redis://127.0.0.1:6379"
secondary_nodes = []
port = 50051
redis_pool_size = 10
replication_factor = 0
auth_enabled = false
EOF

# 3. Start RustyCluster
cargo run --release config_dev.toml
```

#### Production Single Node
```bash
# 1. Start Redis with authentication
redis-server --port 6379 --requirepass "secure_redis_password"

# 2. Create production configuration
cat > config_prod.toml << EOF
redis_url = "redis://username:ENC:encrypted_password@127.0.0.1:6379"
secondary_nodes = []
port = 50051
redis_pool_size = 1024
replication_factor = 0
auth_enabled = true
auth_username = "admin"
auth_password = "ENC:encrypted_admin_password"
rate_limiting_enabled = true
rate_limit_rps = 5000
health_check_enabled = true
EOF

# 3. Set encryption key and start
export RUSTYCLUSTER_MASTER_KEY="your_secure_master_key"
cargo run --release config_prod.toml
```

### Multi-Node Cluster Deployment

#### Three-Node Cluster Setup

**Node 1 (Primary) - config_node1.toml**
```toml
redis_url = "redis://username:ENC:encrypted_password@127.0.0.1:6379"
secondary_nodes = ["http://127.0.0.1:50052", "http://127.0.0.1:50053"]
port = 50051
redis_pool_size = 2048
replication_factor = 2
read_consistency = 1
async_replication = true
auth_enabled = true
auth_username = "admin"
auth_password = "ENC:encrypted_password"
```

**Node 2 (Secondary) - config_node2.toml**
```toml
redis_url = "redis://username:ENC:encrypted_password@127.0.0.1:6370"
secondary_nodes = ["http://127.0.0.1:50051", "http://127.0.0.1:50053"]
port = 50052
redis_pool_size = 2048
replication_factor = 2
read_consistency = 1
async_replication = true
auth_enabled = true
auth_username = "admin"
auth_password = "ENC:encrypted_password"
```

**Node 3 (Secondary) - config_node3.toml**
```toml
redis_url = "redis://username:ENC:encrypted_password@127.0.0.1:6371"
secondary_nodes = ["http://127.0.0.1:50051", "http://127.0.0.1:50052"]
port = 50053
redis_pool_size = 2048
replication_factor = 2
read_consistency = 1
async_replication = true
auth_enabled = true
auth_username = "admin"
auth_password = "ENC:encrypted_password"
```

#### Deployment Steps
```bash
# 1. Start Redis instances
redis-server --port 6379 --requirepass "secure_password" &
redis-server --port 6370 --requirepass "secure_password" &
redis-server --port 6371 --requirepass "secure_password" &

# 2. Set encryption key on all nodes
export RUSTYCLUSTER_MASTER_KEY="your_secure_master_key"

# 3. Start RustyCluster nodes
cargo run --release config_node1.toml &
cargo run --release config_node2.toml &
cargo run --release config_node3.toml &

# 4. Verify cluster health
grpcurl -plaintext 127.0.0.1:50051 rustycluster.KeyValueService.HealthCheck
grpcurl -plaintext 127.0.0.1:50052 rustycluster.KeyValueService.HealthCheck
grpcurl -plaintext 127.0.0.1:50053 rustycluster.KeyValueService.HealthCheck
```

### Site Replication Deployment

#### Multi-Site Configuration

**Site 1 Primary - config_site1.toml**
```toml
redis_url = "redis://username:ENC:encrypted_password@site1-redis:6379"
secondary_nodes = ["http://site1-node2:50051"]
port = 50051

# Site replication to other sites
site_replication_enabled = true
site_replication_nodes = [
    {
        site_name = "site2",
        primary_node = "http://site2-primary:50051",
        failover_node = "http://site2-failover:50051"
    },
    {
        site_name = "site3",
        primary_node = "http://site3-primary:50051",
        failover_node = "http://site3-failover:50051"
    }
]

# Performance optimization for site replication
site_replication_timeout_ms = 1000
site_replication_retries = 3
site_replication_batch_size = 100
```

### Docker Deployment

#### Dockerfile
```dockerfile
# Dockerfile
FROM rust:1.70 as builder

WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim

RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/target/release/rustycluster /usr/local/bin/
COPY --from=builder /app/target/release/encrypt_password /usr/local/bin/
COPY --from=builder /app/target/release/decrypt_password /usr/local/bin/

EXPOSE 50051

CMD ["rustycluster", "/config/config.toml", "/config/logconfig.toml"]
```

#### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  redis-primary:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis-primary-data:/data

  redis-secondary1:
    image: redis:7-alpine
    command: redis-server --port 6370 --requirepass ${REDIS_PASSWORD}
    ports:
      - "6370:6370"
    volumes:
      - redis-secondary1-data:/data

  redis-secondary2:
    image: redis:7-alpine
    command: redis-server --port 6371 --requirepass ${REDIS_PASSWORD}
    ports:
      - "6371:6371"
    volumes:
      - redis-secondary2-data:/data

  rustycluster-node1:
    build: .
    ports:
      - "50051:50051"
    volumes:
      - ./config:/config
      - ./logs:/logs
    environment:
      - RUSTYCLUSTER_MASTER_KEY=${MASTER_KEY}
    depends_on:
      - redis-primary
      - redis-secondary1
      - redis-secondary2
    command: ["rustycluster", "/config/config_node1.toml", "/config/logconfig.toml"]

  rustycluster-node2:
    build: .
    ports:
      - "50052:50052"
    volumes:
      - ./config:/config
      - ./logs:/logs
    environment:
      - RUSTYCLUSTER_MASTER_KEY=${MASTER_KEY}
    depends_on:
      - redis-primary
      - redis-secondary1
      - redis-secondary2
    command: ["rustycluster", "/config/config_node2.toml", "/config/logconfig.toml"]

  rustycluster-node3:
    build: .
    ports:
      - "50053:50053"
    volumes:
      - ./config:/config
      - ./logs:/logs
    environment:
      - RUSTYCLUSTER_MASTER_KEY=${MASTER_KEY}
    depends_on:
      - redis-primary
      - redis-secondary1
      - redis-secondary2
    command: ["rustycluster", "/config/config_node3.toml", "/config/logconfig.toml"]

volumes:
  redis-primary-data:
  redis-secondary1-data:
  redis-secondary2-data:
```

#### Environment File (.env)
```bash
# .env
REDIS_PASSWORD=secure_redis_password
MASTER_KEY=your_secure_master_key_here
```

#### Docker Deployment Commands
```bash
# Build and start the cluster
docker-compose up -d

# Check container status
docker-compose ps

# View logs
docker-compose logs rustycluster-node1

# Scale specific services
docker-compose up -d --scale rustycluster-node2=2

# Stop the cluster
docker-compose down
```

### Kubernetes Deployment

#### Kubernetes Manifests

**ConfigMap for Configuration**
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: rustycluster-config
data:
  config.toml: |
    redis_url = "redis://username:ENC:encrypted_password@redis-service:6379"
    secondary_nodes = ["http://rustycluster-node2:50051", "http://rustycluster-node3:50051"]
    port = 50051
    redis_pool_size = 2048
    replication_factor = 2
    auth_enabled = true
    auth_username = "admin"
    auth_password = "ENC:encrypted_password"
    rate_limiting_enabled = true
    rate_limit_rps = 5000
  logconfig.toml: |
    file = "/logs/rustycluster.log"
    max_size = 10485760
    max_files = 5
    level = "info"
    pattern = "{d(%Y-%m-%d %H:%M:%S.%3f)} - {l} - {M} - {m}\n"
    console_enabled = false
```

**Deployment**
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rustycluster
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rustycluster
  template:
    metadata:
      labels:
        app: rustycluster
    spec:
      containers:
      - name: rustycluster
        image: rustycluster:latest
        ports:
        - containerPort: 50051
        env:
        - name: RUSTYCLUSTER_MASTER_KEY
          valueFrom:
            secretKeyRef:
              name: rustycluster-secrets
              key: master-key
        volumeMounts:
        - name: config-volume
          mountPath: /config
        - name: logs-volume
          mountPath: /logs
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          exec:
            command:
            - grpcurl
            - -plaintext
            - localhost:50051
            - rustycluster.KeyValueService.HealthCheck
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - grpcurl
            - -plaintext
            - localhost:50051
            - rustycluster.KeyValueService.Ping
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: rustycluster-config
      - name: logs-volume
        emptyDir: {}
```

**Service**
```yaml
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: rustycluster-service
spec:
  selector:
    app: rustycluster
  ports:
  - protocol: TCP
    port: 50051
    targetPort: 50051
  type: LoadBalancer
```

**Secret for Encryption Key**
```yaml
# k8s/secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: rustycluster-secrets
type: Opaque
data:
  master-key: <base64-encoded-master-key>
```

#### Kubernetes Deployment Commands
```bash
# Create namespace
kubectl create namespace rustycluster

# Apply configurations
kubectl apply -f k8s/secret.yaml -n rustycluster
kubectl apply -f k8s/configmap.yaml -n rustycluster
kubectl apply -f k8s/deployment.yaml -n rustycluster
kubectl apply -f k8s/service.yaml -n rustycluster

# Check deployment status
kubectl get pods -n rustycluster
kubectl get services -n rustycluster

# View logs
kubectl logs -f deployment/rustycluster -n rustycluster

# Scale deployment
kubectl scale deployment rustycluster --replicas=5 -n rustycluster
```

## Troubleshooting

### Common Issues and Solutions

#### Startup Issues

**Issue: Configuration file not found**
```
Configuration file 'config.toml' not found in current directory
```
**Solution:**
```bash
# Ensure config file exists in current directory
ls -la config.toml

# Or specify full path
cargo run --release /path/to/config.toml

# Check file permissions
chmod 644 config.toml
```

**Issue: Redis connection failed**
```
ERROR - redis_lib - Failed to connect to Redis: Connection refused
```
**Solution:**
```bash
# Check if Redis is running
redis-cli ping

# Check Redis configuration
redis-cli info server

# Verify connection string
redis-cli -h 127.0.0.1 -p 6379 -a "password" ping

# Check firewall/network
telnet 127.0.0.1 6379
```

**Issue: Port already in use**
```
ERROR - main - Failed to bind to address: Address already in use
```
**Solution:**
```bash
# Find process using the port
netstat -tulpn | grep :50051
lsof -i :50051

# Kill the process or change port in config
kill -9 <process_id>

# Or change port in config.toml
port = 50052
```

#### Authentication Issues

**Issue: Authentication failed**
```
WARN - auth - Authentication failed for user: invalid_user
```
**Solution:**
```bash
# Verify credentials in config
auth_username = "correct_username"
auth_password = "correct_password"

# Check if password is encrypted
auth_password = "ENC:encrypted_value"

# Test authentication
grpcurl -plaintext -d '{"username":"testuser","password":"testpass"}' \
  127.0.0.1:50051 rustycluster.KeyValueService.Authenticate
```

**Issue: Session token expired**
```
ERROR - auth - Session token expired: abc123-def456
```
**Solution:**
```bash
# Increase session duration
session_duration_secs = 7200  # 2 hours

# Disable token expiry for testing
auth_token_expiry_enabled = false

# Re-authenticate to get new token
```

#### Performance Issues

**Issue: Low throughput**
```
INFO - grpc - Current RPS: 500 (target: 10000)
```
**Solution:**
```toml
# Increase connection pools
redis_pool_size = 4096
secondary_pool_size = 2048

# Optimize worker threads
worker_threads = 32

# Optimize batching
max_batch_size = 100000
batch_flush_interval_ms = 10

# Disable replication for testing
replication_factor = 0
```

**Issue: High latency**
```
WARN - grpc - High latency detected: 1500ms for Set operation
```
**Solution:**
```toml
# Enable TCP optimizations
tcp_nodelay = true
tcp_keepalive_secs = 15

# Reduce retry delays
retry_delay_ms = 10
max_retries = 2

# Optimize batch processing
batch_flush_interval_ms = 1
```

**Issue: Memory usage high**
```
WARN - main - High memory usage detected: 8GB
```
**Solution:**
```toml
# Reduce connection pools
redis_pool_size = 512
secondary_pool_size = 256

# Reduce batch sizes
max_batch_size = 1000
chunk_size = 1000

# Reduce worker threads
worker_threads = 8
```

#### Replication Issues

**Issue: Replication failures**
```
ERROR - write_consistency - Failed to replicate to quorum: only 1 of 2 nodes responded
```
**Solution:**
```bash
# Check secondary node health
grpcurl -plaintext 127.0.0.1:50052 rustycluster.KeyValueService.HealthCheck

# Verify network connectivity
telnet 127.0.0.1 50052

# Check secondary node logs
tail -f rustycluster_node2.log

# Adjust replication settings
replication_factor = 1  # Reduce temporarily
write_consistency = "ONE"  # Less strict consistency
```

**Issue: Site replication timeout**
```
WARN - site_replication - Site replication timeout: site2 did not respond within 1000ms
```
**Solution:**
```toml
# Increase timeout
site_replication_timeout_ms = 5000

# Increase retries
site_replication_retries = 5

# Check network latency between sites
ping site2-primary
```

### Diagnostic Commands

#### Health Checks
```bash
# Application health
grpcurl -plaintext 127.0.0.1:50051 rustycluster.KeyValueService.HealthCheck

# Redis health
redis-cli -h 127.0.0.1 -p 6379 -a "password" ping

# Network connectivity
telnet 127.0.0.1 50051
nc -zv 127.0.0.1 50051
```

#### Performance Monitoring
```bash
# Monitor connections
netstat -an | grep :50051 | wc -l
netstat -an | grep :6379 | wc -l

# Monitor resource usage
top -p $(pgrep rustycluster)
htop -p $(pgrep rustycluster)

# Monitor network usage
iftop -i eth0
nethogs
```

#### Log Analysis
```bash
# Monitor errors in real-time
tail -f rustycluster.log | grep -i "error\|warn"

# Count error types
grep "ERROR" rustycluster.log | cut -d'-' -f3 | sort | uniq -c

# Monitor authentication events
grep "auth" rustycluster.log | tail -20

# Monitor performance metrics
grep "latency\|RPS\|throughput" rustycluster.log | tail -10
```

### Performance Troubleshooting Guide

#### Achieving 10K+ RPS

1. **Use Performance Configuration**
   ```bash
   cargo run --release load/config_performance.toml
   ```

2. **Optimize System Settings**
   ```bash
   # Increase file descriptor limits
   ulimit -n 65536

   # Optimize TCP settings
   echo 'net.core.somaxconn = 65536' >> /etc/sysctl.conf
   echo 'net.ipv4.tcp_max_syn_backlog = 65536' >> /etc/sysctl.conf
   sysctl -p
   ```

3. **Monitor Key Metrics**
   ```bash
   # Run load test
   cd load
   python loadtest_async.py --operations 200000 --concurrency 2000 --rps 10000

   # Monitor during test
   watch -n 1 'netstat -an | grep :50051 | wc -l'
   ```

4. **Tune Configuration Iteratively**
   ```toml
   # Start with these settings and adjust based on results
   redis_pool_size = 4096
   worker_threads = 32
   max_batch_size = 100000
   concurrency_limit = 8192
   ```

### Getting Help

#### Log Collection for Support
```bash
# Collect system information
uname -a > system_info.txt
free -h >> system_info.txt
df -h >> system_info.txt

# Collect application logs
cp rustycluster.log rustycluster_support.log
cp config.toml config_support.toml

# Collect performance data
top -b -n 1 > performance_snapshot.txt
netstat -an | grep :50051 > network_connections.txt
```

#### Community Resources
- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Check README.md and SCRIPTS.md for latest updates
- **Performance Guides**: See SCRIPTS.md for detailed performance optimization
- **Configuration Examples**: Multiple example configurations in load/ directory

This completes the comprehensive RustyCluster User Manual covering environment setup, configuration, fine-tuning, security, monitoring, deployment, and troubleshooting.

## Monitoring & Logging

### Logging Configuration

RustyCluster uses the `tracing` crate for structured, high-performance logging.

#### Log Configuration File (`logconfig.toml`)
```toml
# Log file settings
file = "rustycluster.log"           # Log file path
max_size = 10485760                 # 10MB file size limit
max_files = 5                       # Keep 5 rotated files

# Log level (error, warn, info, debug, trace)
level = "info"                      # Default: info

# Log format pattern
pattern = "{d(%Y-%m-%d %H:%M:%S.%3f)} - {l} - {M} - {m}\n"

# Tokio Console integration
console_enabled = false             # Enable for profiling
```

#### Log Levels and Usage

**Error Level** - Critical issues requiring immediate attention
```toml
level = "error"
# Logs: System failures, connection errors, authentication failures
```

**Warn Level** - Important issues that don't stop operation
```toml
level = "warn"
# Logs: Performance degradation, retry attempts, configuration warnings
```

**Info Level** - General operational information (recommended for production)
```toml
level = "info"
# Logs: Startup/shutdown, configuration changes, authentication events
```

**Debug Level** - Detailed diagnostic information
```toml
level = "debug"
# Logs: Request/response details, connection pool status, replication events
```

**Trace Level** - Very detailed debugging (performance impact)
```toml
level = "trace"
# Logs: Function entry/exit, detailed timing, internal state changes
```

#### Environment Variable Override
```bash
# Override log level at runtime
RUST_LOG=debug cargo run --release config.toml

# Module-specific logging
RUST_LOG=rustycluster::grpc=debug,rustycluster::redis_lib=info cargo run --release config.toml
```

#### Log Format Customization
```toml
# Available format variables:
# {d} - Date and time with custom format
# {l} - Log level
# {M} - Module name
# {m} - Log message
# {f} - File name
# {L} - Line number
# {t} - Thread name/ID

# Examples:
pattern = "{d(%Y-%m-%d %H:%M:%S.%3f)} [{l}] {M}: {m}\n"
pattern = "{d} - {l:5} - {t} - {M} - {m}\n"
pattern = "[{d(%H:%M:%S)}] {l} {f}:{L} - {m}\n"
```

### Tokio Console Integration

Tokio Console provides real-time profiling and debugging capabilities.

#### Setup Tokio Console

1. **Build with console feature**
   ```bash
   cargo build --release --features console
   ```

2. **Enable in log configuration**
   ```toml
   # logconfig.toml
   console_enabled = true
   ```

3. **Run with tokio_unstable flag**
   ```bash
   RUSTFLAGS="--cfg tokio_unstable" cargo run --release --features console config.toml
   ```

4. **Start Tokio Console in another terminal**
   ```bash
   # Install tokio-console if not already installed
   cargo install --locked tokio-console

   # Connect to running application
   tokio-console
   ```

#### Tokio Console Features
- **Task Monitoring**: View all async tasks and their states
- **Resource Tracking**: Monitor locks, semaphores, and other async resources
- **Performance Metrics**: CPU usage, task scheduling, and blocking operations
- **Real-time Updates**: Live view of application performance

### Performance Monitoring

#### Built-in Metrics

RustyCluster provides built-in performance tracking:

```toml
# Enable performance monitoring
enable_latency_tracking = true
enable_throughput_tracking = true
performance_log_interval_ms = 10000  # Log metrics every 10 seconds
```

#### Key Metrics to Monitor

**Throughput Metrics:**
- Requests per second (RPS)
- Operations per second by type (Set, Get, Delete, etc.)
- Batch operation efficiency
- Replication throughput

**Latency Metrics:**
- Request processing time (P50, P95, P99)
- Redis operation latency
- Replication latency
- Authentication latency

**Resource Metrics:**
- Connection pool utilization
- Memory usage
- CPU utilization
- Network bandwidth

**Error Metrics:**
- Failed request rate
- Connection failures
- Authentication failures
- Replication failures

#### External Monitoring Integration

**Prometheus Integration** (Future Enhancement)
```toml
# Prometheus metrics endpoint
prometheus_enabled = true
prometheus_port = 9090
prometheus_path = "/metrics"
```

**Log Analysis with ELK Stack**
```bash
# Logstash configuration for RustyCluster logs
input {
  file {
    path => "/path/to/rustycluster.log"
    start_position => "beginning"
  }
}

filter {
  grok {
    match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} - %{LOGLEVEL:level} - %{DATA:module} - %{GREEDYDATA:message}" }
  }
}

output {
  elasticsearch {
    hosts => ["localhost:9200"]
    index => "rustycluster-%{+YYYY.MM.dd}"
  }
}
```

### Health Monitoring

#### Health Check Configuration
```toml
# Health check settings
health_check_enabled = true
health_check_interval_secs = 30
health_check_timeout_ms = 5000
health_check_on_idle_only = true

# What to monitor
monitor_redis_health = true
monitor_secondary_nodes = true
monitor_site_replication = true
```

#### Health Check Endpoints
```bash
# Check application health
grpcurl -plaintext 127.0.0.1:50051 rustycluster.KeyValueService.HealthCheck

# Check specific node health
grpcurl -plaintext 127.0.0.1:50052 rustycluster.KeyValueService.HealthCheck
```

#### Automated Health Monitoring
```bash
#!/bin/bash
# health_monitor.sh - Simple health monitoring script

NODES=("127.0.0.1:50051" "127.0.0.1:50052" "127.0.0.1:50053")
LOG_FILE="/var/log/rustycluster_health.log"

for node in "${NODES[@]}"; do
    if grpcurl -plaintext "$node" rustycluster.KeyValueService.HealthCheck > /dev/null 2>&1; then
        echo "$(date): $node - HEALTHY" >> "$LOG_FILE"
    else
        echo "$(date): $node - UNHEALTHY" >> "$LOG_FILE"
        # Send alert (email, Slack, etc.)
    fi
done
```

### Alerting and Notifications

#### Log-based Alerting
```bash
# Monitor for critical errors
tail -f rustycluster.log | grep -i "error\|fatal\|panic" | while read line; do
    echo "ALERT: $line" | mail -s "RustyCluster Error" <EMAIL>
done
```

#### Performance Alerting
```bash
# Monitor for high latency
tail -f rustycluster.log | grep "latency" | awk '{if($NF > 1000) print "High latency detected: " $0}' | while read line; do
    echo "$line" | mail -s "RustyCluster Performance Alert" <EMAIL>
done
```

### Troubleshooting with Logs

#### Common Log Patterns

**Connection Issues:**
```
ERROR - redis_lib - Failed to connect to Redis: Connection refused
WARN - grpc - Secondary node unreachable: http://127.0.0.1:50052
```

**Authentication Issues:**
```
WARN - auth - Authentication failed for user: invalid_user
ERROR - auth - Session token expired: abc123-def456
```

**Performance Issues:**
```
WARN - grpc - High latency detected: 1500ms for Set operation
INFO - redis_lib - Connection pool exhausted, waiting for available connection
```

**Replication Issues:**
```
ERROR - write_consistency - Failed to replicate to quorum: only 1 of 2 nodes responded
WARN - site_replication - Site replication timeout: site2 did not respond within 1000ms
```

#### Log Analysis Commands
```bash
# Find authentication failures
grep "Authentication failed" rustycluster.log

# Monitor error rates
grep -c "ERROR" rustycluster.log

# Check performance issues
grep "High latency\|timeout\|exhausted" rustycluster.log

# Monitor replication health
grep "replication" rustycluster.log | tail -20
```
