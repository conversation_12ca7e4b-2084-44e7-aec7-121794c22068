#!/usr/bin/env python3
"""
Test script to verify the health check endpoint works without authentication.
This script tests the new HealthCheck gRPC endpoint that bypasses authentication.
"""

import grpc
import rustycluster_pb2
import rustycluster_pb2_grpc
import sys
import time

def test_health_check_without_auth():
    """Test health check endpoint without authentication"""
    
    channel = grpc.insecure_channel('localhost:50051')
    stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
    
    try:
        print("Testing health check without authentication...")
        health_check_request = rustycluster_pb2.PingRequest()
        health_check_response = stub.HealthCheck(health_check_request)
        
        if health_check_response.success:
            print(f"✓ Health check without auth successful: {health_check_response.message}")
            print(f"  Latency: {health_check_response.latency_ms}ms")
            return True
        else:
            print("✗ Health check without auth failed")
            return False
            
    except grpc.RpcError as e:
        print(f"✗ gRPC error during health check: {e}")
        return False
    finally:
        channel.close()

def test_ping_with_auth_enabled():
    """Test regular ping endpoint (should fail if auth is enabled)"""
    
    channel = grpc.insecure_channel('localhost:50051')
    stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
    
    try:
        print("Testing regular ping without authentication...")
        ping_request = rustycluster_pb2.PingRequest()
        ping_response = stub.Ping(ping_request)
        
        if ping_response.success:
            print(f"✓ Regular ping without auth successful: {ping_response.message}")
            print("  (This means auth is disabled)")
            return True
        else:
            print("✗ Regular ping without auth failed")
            return False
            
    except grpc.RpcError as e:
        if e.code() == grpc.StatusCode.UNAUTHENTICATED:
            print("✓ Regular ping correctly requires authentication")
            return True
        else:
            print(f"✗ gRPC error during ping: {e}")
            return False
    finally:
        channel.close()

def main():
    """Main test function"""
    print("=" * 60)
    print("RustyCluster Health Check Endpoint Test")
    print("=" * 60)
    
    # Test health check endpoint
    health_check_success = test_health_check_without_auth()
    print()
    
    # Test regular ping endpoint
    ping_result = test_ping_with_auth_enabled()
    print()
    
    # Summary
    print("=" * 60)
    print("Test Summary:")
    print(f"Health Check Endpoint: {'PASS' if health_check_success else 'FAIL'}")
    print(f"Regular Ping Endpoint: {'PASS' if ping_result else 'FAIL'}")
    
    if health_check_success:
        print("\n✓ Health check endpoint is working correctly!")
        print("  Internal communication should no longer require authentication.")
    else:
        print("\n✗ Health check endpoint is not working properly.")
        print("  Please check the server configuration.")
    
    print("=" * 60)
    
    return 0 if health_check_success else 1

if __name__ == "__main__":
    sys.exit(main())
