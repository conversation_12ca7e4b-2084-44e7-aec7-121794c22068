redis_url = "redis://settlenxt:npci@127.0.0.1:6370"
secondary_nodes = ["http://127.0.0.1:50051", "http://127.0.0.1:50053"]
port = 50052
# Maximum number of connections in the Redis connection pool
redis_pool_size = 2048

# Number of secondary nodes to which data should be replicated
# 0 means no replication, maximum is the number of secondary nodes
replication_factor = 2

# Number of secondary nodes that must have the data before considering it consistent
# 0 means no consistency check, maximum is the number of secondary nodes
read_consistency = 0

# If true, replication to secondary nodes happens asynchronously
# If false, replication happens synchronously and blocks until complete
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts when connecting to secondary nodes
retry_delay_ms = 50

# Maximum age in seconds for a replication batch before it's considered expired
# Expired batches are skipped to prevent stale data replication
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 512

# Maximum number of operations in a batch before it's flushed
max_batch_size = 100000

# Interval in milliseconds to flush batches
batch_flush_interval_ms = 25

# Server configuration
# Sends periodic keepalive probes to maintain connection health
tcp_keepalive_secs = 15
# Disables <PERSON><PERSON>'s algorithm when set to true
tcp_nodelay = true
# Limits the number of concurrent requests per connection
concurrency_limit = 512
# Controls maximum number of concurrent HTTP/2 streams per connection
max_concurrent_streams = 4096

# Replication chunk size for batch processing
chunk_size = 50000

# Number of shards for batch collectors
num_shards = 128

# Configure worker threads based on the CPU size
worker_threads = 32  # Maximized for better parallelism
# Authentication configuration
# Set to true to enable client authentication
auth_enabled = true

# Username for client authentication (required if auth_enabled = true)
auth_username = "admin"

# Password for client authentication (required if auth_enabled = true)
auth_password = "npci"

# Session duration in seconds (default: 3600 = 1 hour)
session_duration_secs = 3600