use rustycluster::crypto::create_crypto_manager;
use std::env;
use std::io::{self, Write};

/// Utility tool to encrypt passwords for RustyCluster configuration files
/// 
/// Usage:
/// 1. Set RUSTYCLUSTER_MASTER_KEY environment variable (optional)
/// 2. Run: cargo run --bin encrypt_password
/// 3. Enter the password to encrypt when prompted
/// 4. Copy the encrypted output to your configuration file
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("RustyCluster Password Encryption Utility");
    println!("========================================");
    println!();

    // Check if master key is set
    let master_key_set = env::var("RUSTYCLUSTER_MASTER_KEY").is_ok();
    if master_key_set {
        println!("✓ Using RUSTYCLUSTER_MASTER_KEY environment variable");
    } else {
        println!("⚠ RUSTYCLUSTER_MASTER_KEY not set, using default key");
        println!("  Set this environment variable in production for security!");
    }
    println!();

    // Create crypto manager
    let crypto_manager = create_crypto_manager()?;

    loop {
        // Prompt for password
        print!("Enter password to encrypt (or 'quit' to exit): ");
        io::stdout().flush()?;

        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        let password = input.trim();

        if password.is_empty() {
            println!("Empty password entered, skipping...");
            println!();
            continue;
        }

        if password.eq_ignore_ascii_case("quit") || password.eq_ignore_ascii_case("exit") {
            println!("Goodbye!");
            break;
        }

        // Encrypt the password
        match crypto_manager.encrypt(password) {
            Ok(encrypted) => {
                println!();
                println!("✓ Password encrypted successfully!");
                println!("Original:  {}", password);
                println!("Encrypted: {}", encrypted);
                println!();
                println!("Copy the encrypted value to your configuration file.");
                println!("The application will automatically detect and decrypt it.");
                println!();
                
                // Show example usage
                println!("Example configuration:");
                println!("auth_password = \"{}\"", encrypted);
                println!("redis_url = \"redis://username:{}@localhost:6379\"", encrypted);
                println!();
            }
            Err(e) => {
                eprintln!("✗ Failed to encrypt password: {}", e);
                println!();
            }
        }
    }

    Ok(())
}
