# RustyCluster Configuration with Rate Limiting Enabled
# This configuration demonstrates rate limiting features

# Redis connection URL for the primary node
redis_url = "redis://127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to
secondary_nodes = []

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 512

# Number of secondary nodes to which data should be replicated
replication_factor = 0

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# If true, replication to secondary nodes happens asynchronously
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 100

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 512

# Maximum number of operations in a batch
max_batch_size = 1000

# Interval in milliseconds to flush batches
batch_flush_interval_ms = 100

# TCP keepalive timeout in seconds
tcp_keepalive_secs = 30

# Enable TCP_NODELAY for better latency
tcp_nodelay = true

# Limits the number of concurrent requests per connection
concurrency_limit = 1024

# Controls maximum number of concurrent HTTP/2 streams per connection
max_concurrent_streams = 2048

# Replication chunk size for batch processing
chunk_size = 1000

# Number of shards for batch processing
num_shards = 16

# Number of worker threads for the Tokio runtime
worker_threads = 8

# Authentication configuration
auth_enabled = true
auth_username = "testuser"
auth_password = "testpass"
session_duration_secs = 3600
auth_mode = "per_request"
auth_token_expiry_enabled = true

# Write consistency configuration (disabled for this test)
peer_redis_nodes = []
write_consistency = "QUORUM"
quorum_value = 2
write_retry_count = 3
peer_redis_pool_size = 64

# Site replication configuration (disabled for this test)
site_replication_enabled = false
site_primary_node = ""
site_failover_node = ""
site_replication_retry_count = 3
site_replication_timeout_ms = 5000
site_replication_pool_size = 256

# Connection pool mode
use_physical_connections = true

# Health check configuration (disabled for this test)
redis_keepalive_enabled = false
redis_keepalive_interval_secs = 30
secondary_nodes_keepalive_enabled = false
secondary_nodes_keepalive_interval_secs = 30
site_nodes_keepalive_enabled = false
site_nodes_keepalive_interval_secs = 30
peer_redis_keepalive_enabled = false
peer_redis_keepalive_interval_secs = 30

# Rate limiting configuration - ENABLED FOR TESTING
# Enable rate limiting to demonstrate the feature
rate_limiting_enabled = true

# Rate limiting type for testing: "both" to demonstrate all features
# This tests both global and per-client rate limiting
rate_limit_type = "both"

# Conservative per-client rate limit for testing: 10 requests per second per client
# This makes it easy to test per-client rate limiting behavior
rate_limit_requests_per_second = 10

# Small per-client burst size for testing: allow 5 request bursts
# This allows testing per-client burst behavior
rate_limit_burst_size = 5

# Conservative global rate limit for testing: 50 requests per second globally
# This makes it easy to test global rate limiting behavior with multiple clients
rate_limit_global_requests_per_second = 50

# Small global burst size for testing: allow 20 request bursts globally
# This allows testing global burst behavior
rate_limit_global_burst_size = 20

# Short cleanup interval for testing: 60 seconds
# This allows testing cleanup behavior
rate_limit_cleanup_interval_secs = 60
