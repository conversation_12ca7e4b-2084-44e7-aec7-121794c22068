@echo off
REM Quick Rate Limiting Test - Much faster than the original

echo ⚡ Quick Rate Limiting Test
echo ==========================
echo.

echo This test is much faster - uses burst testing and shorter durations
echo.

echo 🚀 Running fast rate limiting tests...
echo.

REM Run the fast test
python test_rate_limiting_fast.py --expected-limit 1000 --test both

echo.
echo ✅ Quick test completed!
echo.
echo The fast test uses:
echo - Burst tests (100-500 requests sent quickly)
echo - Short sustained tests (5 seconds instead of 10)
echo - Concurrent clients for faster execution
echo.
pause
