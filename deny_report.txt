error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#addr2line@0.24.2:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ addr2line v0.24.2
    └── backtrace v0.3.75
        └── tokio v1.45.1
            ├── combine v4.6.7
            │   ├── redis v0.29.5
            │   │   └── deadpool-redis v0.20.0
            │   │       └── rustycluster v2.2.7
            │   └── redis v0.31.0
            │       └── rustycluster v2.2.7 (*)
            ├── deadpool v0.12.2
            │   └── deadpool-redis v0.20.0 (*)
            ├── deadpool-runtime v0.1.4
            │   └── deadpool v0.12.2 (*)
            ├── h2 v0.4.10
            │   ├── hyper v1.6.0
            │   │   ├── hyper-timeout v0.5.2
            │   │   │   └── tonic v0.13.1
            │   │   │       └── rustycluster v2.2.7 (*)
            │   │   ├── hyper-util v0.1.14
            │   │   │   ├── hyper-timeout v0.5.2 (*)
            │   │   │   └── tonic v0.13.1 (*)
            │   │   └── tonic v0.13.1 (*)
            │   └── tonic v0.13.1 (*)
            ├── hyper v1.6.0 (*)
            ├── hyper-timeout v0.5.2 (*)
            ├── hyper-util v0.1.14 (*)
            ├── redis v0.29.5 (*)
            ├── redis v0.31.0 (*)
            ├── rustycluster v2.2.7 (*)
            ├── tokio-stream v0.1.17
            │   └── tonic v0.13.1 (*)
            ├── tokio-util v0.7.15
            │   ├── combine v4.6.7 (*)
            │   ├── h2 v0.4.10 (*)
            │   ├── redis v0.29.5 (*)
            │   ├── redis v0.31.0 (*)
            │   └── tower v0.5.2
            │       ├── axum v0.8.4
            │       │   └── tonic v0.13.1 (*)
            │       └── tonic v0.13.1 (*)
            ├── tonic v0.13.1 (*)
            └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#adler2@2.0.1:4:12
  │
4 │ license = "0BSD OR MIT OR Apache-2.0"
  │            ━━━━────━━━────━━━━━━━━━━
  │            │       │      │
  │            │       │      rejected: license is not explicitly allowed
  │            │       rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ 0BSD - BSD Zero Clause License:
  ├   - OSI approved
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ adler2 v2.0.1
    └── miniz_oxide v0.8.9
        └── backtrace v0.3.75
            └── tokio v1.45.1
                ├── combine v4.6.7
                │   ├── redis v0.29.5
                │   │   └── deadpool-redis v0.20.0
                │   │       └── rustycluster v2.2.7
                │   └── redis v0.31.0
                │       └── rustycluster v2.2.7 (*)
                ├── deadpool v0.12.2
                │   └── deadpool-redis v0.20.0 (*)
                ├── deadpool-runtime v0.1.4
                │   └── deadpool v0.12.2 (*)
                ├── h2 v0.4.10
                │   ├── hyper v1.6.0
                │   │   ├── hyper-timeout v0.5.2
                │   │   │   └── tonic v0.13.1
                │   │   │       └── rustycluster v2.2.7 (*)
                │   │   ├── hyper-util v0.1.14
                │   │   │   ├── hyper-timeout v0.5.2 (*)
                │   │   │   └── tonic v0.13.1 (*)
                │   │   └── tonic v0.13.1 (*)
                │   └── tonic v0.13.1 (*)
                ├── hyper v1.6.0 (*)
                ├── hyper-timeout v0.5.2 (*)
                ├── hyper-util v0.1.14 (*)
                ├── redis v0.29.5 (*)
                ├── redis v0.31.0 (*)
                ├── rustycluster v2.2.7 (*)
                ├── tokio-stream v0.1.17
                │   └── tonic v0.13.1 (*)
                ├── tokio-util v0.7.15
                │   ├── combine v4.6.7 (*)
                │   ├── h2 v0.4.10 (*)
                │   ├── redis v0.29.5 (*)
                │   ├── redis v0.31.0 (*)
                │   └── tower v0.5.2
                │       ├── axum v0.8.4
                │       │   └── tonic v0.13.1 (*)
                │       └── tonic v0.13.1 (*)
                ├── tonic v0.13.1 (*)
                └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#aead@0.5.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ aead v0.5.2
    └── aes-gcm v0.10.3
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#aes@0.8.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ aes v0.8.4
    └── aes-gcm v0.10.3
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#aes-gcm@0.10.3:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ aes-gcm v0.10.3
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#android-tzdata@0.1.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ android-tzdata v0.1.1
    └── chrono v0.4.41
        └── tracing-subscriber v0.3.19
            ├── rustycluster v2.2.7
            └── tracing-appender v0.2.3
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#android_system_properties@0.1.5:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ android_system_properties v0.1.5
    └── iana-time-zone v0.1.63
        └── chrono v0.4.41
            └── tracing-subscriber v0.3.19
                ├── rustycluster v2.2.7
                └── tracing-appender v0.2.3
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#anyhow@1.0.98:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ anyhow v1.0.98
    └── prost-derive v0.13.5
        └── prost v0.13.5
            ├── prost-build v0.13.5
            │   └── tonic-build v0.13.1
            │       └── (build) rustycluster v2.2.7
            ├── prost-types v0.13.5
            │   ├── prost-build v0.13.5 (*)
            │   ├── rustycluster v2.2.7 (*)
            │   └── tonic-build v0.13.1 (*)
            ├── rustycluster v2.2.7 (*)
            └── tonic v0.13.1
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#arc-swap@1.7.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ arc-swap v1.7.1
    └── redis v0.29.5
        └── deadpool-redis v0.20.0
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#async-trait@0.1.88:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ async-trait v0.1.88
    ├── config v0.13.4
    │   └── rustycluster v2.2.7
    └── tonic v0.13.1
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#atomic-waker@1.1.2:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ atomic-waker v1.1.2
    └── h2 v0.4.10
        ├── hyper v1.6.0
        │   ├── hyper-timeout v0.5.2
        │   │   └── tonic v0.13.1
        │   │       └── rustycluster v2.2.7
        │   ├── hyper-util v0.1.14
        │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#autocfg@1.5.0:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ autocfg v1.5.0
    ├── (build) lock_api v0.4.13
    │   ├── dashmap v6.1.0
    │   │   └── rustycluster v2.2.7
    │   └── parking_lot v0.12.4
    │       ├── prometheus v0.14.0
    │       │   └── rustycluster v2.2.7 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       └── tokio v1.45.1
    │           ├── combine v4.6.7
    │           │   ├── redis v0.29.5
    │           │   │   └── deadpool-redis v0.20.0
    │           │   │       └── rustycluster v2.2.7 (*)
    │           │   └── redis v0.31.0
    │           │       └── rustycluster v2.2.7 (*)
    │           ├── deadpool v0.12.2
    │           │   └── deadpool-redis v0.20.0 (*)
    │           ├── deadpool-runtime v0.1.4
    │           │   └── deadpool v0.12.2 (*)
    │           ├── h2 v0.4.10
    │           │   ├── hyper v1.6.0
    │           │   │   ├── hyper-timeout v0.5.2
    │           │   │   │   └── tonic v0.13.1
    │           │   │   │       └── rustycluster v2.2.7 (*)
    │           │   │   ├── hyper-util v0.1.14
    │           │   │   │   ├── hyper-timeout v0.5.2 (*)
    │           │   │   │   └── tonic v0.13.1 (*)
    │           │   │   └── tonic v0.13.1 (*)
    │           │   └── tonic v0.13.1 (*)
    │           ├── hyper v1.6.0 (*)
    │           ├── hyper-timeout v0.5.2 (*)
    │           ├── hyper-util v0.1.14 (*)
    │           ├── redis v0.29.5 (*)
    │           ├── redis v0.31.0 (*)
    │           ├── rustycluster v2.2.7 (*)
    │           ├── tokio-stream v0.1.17
    │           │   └── tonic v0.13.1 (*)
    │           ├── tokio-util v0.7.15
    │           │   ├── combine v4.6.7 (*)
    │           │   ├── h2 v0.4.10 (*)
    │           │   ├── redis v0.29.5 (*)
    │           │   ├── redis v0.31.0 (*)
    │           │   └── tower v0.5.2
    │           │       ├── axum v0.8.4
    │           │       │   └── tonic v0.13.1 (*)
    │           │       └── tonic v0.13.1 (*)
    │           ├── tonic v0.13.1 (*)
    │           └── tower v0.5.2 (*)
    └── (build) num-traits v0.2.19
        ├── chrono v0.4.41
        │   └── tracing-subscriber v0.3.19
        │       ├── rustycluster v2.2.7 (*)
        │       └── tracing-appender v0.2.3
        │           └── rustycluster v2.2.7 (*)
        ├── num-bigint v0.4.6
        │   ├── redis v0.29.5 (*)
        │   └── redis v0.31.0 (*)
        └── num-integer v0.1.46
            └── num-bigint v0.4.6 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#axum@0.8.4:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ axum v0.8.4
    └── tonic v0.13.1
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#axum-core@0.5.2:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ axum-core v0.5.2
    └── axum v0.8.4
        └── tonic v0.13.1
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#backtrace@0.3.75:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ backtrace v0.3.75
    └── tokio v1.45.1
        ├── combine v4.6.7
        │   ├── redis v0.29.5
        │   │   └── deadpool-redis v0.20.0
        │   │       └── rustycluster v2.2.7
        │   └── redis v0.31.0
        │       └── rustycluster v2.2.7 (*)
        ├── deadpool v0.12.2
        │   └── deadpool-redis v0.20.0 (*)
        ├── deadpool-runtime v0.1.4
        │   └── deadpool v0.12.2 (*)
        ├── h2 v0.4.10
        │   ├── hyper v1.6.0
        │   │   ├── hyper-timeout v0.5.2
        │   │   │   └── tonic v0.13.1
        │   │   │       └── rustycluster v2.2.7 (*)
        │   │   ├── hyper-util v0.1.14
        │   │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   │   └── tonic v0.13.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── hyper v1.6.0 (*)
        ├── hyper-timeout v0.5.2 (*)
        ├── hyper-util v0.1.14 (*)
        ├── redis v0.29.5 (*)
        ├── redis v0.31.0 (*)
        ├── rustycluster v2.2.7 (*)
        ├── tokio-stream v0.1.17
        │   └── tonic v0.13.1 (*)
        ├── tokio-util v0.7.15
        │   ├── combine v4.6.7 (*)
        │   ├── h2 v0.4.10 (*)
        │   ├── redis v0.29.5 (*)
        │   ├── redis v0.31.0 (*)
        │   └── tower v0.5.2
        │       ├── axum v0.8.4
        │       │   └── tonic v0.13.1 (*)
        │       └── tonic v0.13.1 (*)
        ├── tonic v0.13.1 (*)
        └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#base64@0.13.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ base64 v0.13.1
    └── ron v0.7.1
        └── config v0.13.4
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#base64@0.21.7:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ base64 v0.21.7
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#base64@0.22.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ base64 v0.22.1
    └── tonic v0.13.1
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#bitflags@1.3.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ bitflags v1.3.2
    └── ron v0.7.1
        └── config v0.13.4
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#bitflags@2.9.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ bitflags v2.9.1
    ├── redox_syscall v0.5.13
    │   └── parking_lot_core v0.9.11
    │       ├── dashmap v6.1.0
    │       │   └── rustycluster v2.2.7
    │       └── parking_lot v0.12.4
    │           ├── prometheus v0.14.0
    │           │   └── rustycluster v2.2.7 (*)
    │           ├── rustycluster v2.2.7 (*)
    │           └── tokio v1.45.1
    │               ├── combine v4.6.7
    │               │   ├── redis v0.29.5
    │               │   │   └── deadpool-redis v0.20.0
    │               │   │       └── rustycluster v2.2.7 (*)
    │               │   └── redis v0.31.0
    │               │       └── rustycluster v2.2.7 (*)
    │               ├── deadpool v0.12.2
    │               │   └── deadpool-redis v0.20.0 (*)
    │               ├── deadpool-runtime v0.1.4
    │               │   └── deadpool v0.12.2 (*)
    │               ├── h2 v0.4.10
    │               │   ├── hyper v1.6.0
    │               │   │   ├── hyper-timeout v0.5.2
    │               │   │   │   └── tonic v0.13.1
    │               │   │   │       └── rustycluster v2.2.7 (*)
    │               │   │   ├── hyper-util v0.1.14
    │               │   │   │   ├── hyper-timeout v0.5.2 (*)
    │               │   │   │   └── tonic v0.13.1 (*)
    │               │   │   └── tonic v0.13.1 (*)
    │               │   └── tonic v0.13.1 (*)
    │               ├── hyper v1.6.0 (*)
    │               ├── hyper-timeout v0.5.2 (*)
    │               ├── hyper-util v0.1.14 (*)
    │               ├── redis v0.29.5 (*)
    │               ├── redis v0.31.0 (*)
    │               ├── rustycluster v2.2.7 (*)
    │               ├── tokio-stream v0.1.17
    │               │   └── tonic v0.13.1 (*)
    │               ├── tokio-util v0.7.15
    │               │   ├── combine v4.6.7 (*)
    │               │   ├── h2 v0.4.10 (*)
    │               │   ├── redis v0.29.5 (*)
    │               │   ├── redis v0.31.0 (*)
    │               │   └── tower v0.5.2
    │               │       ├── axum v0.8.4
    │               │       │   └── tonic v0.13.1 (*)
    │               │       └── tonic v0.13.1 (*)
    │               ├── tonic v0.13.1 (*)
    │               └── tower v0.5.2 (*)
    ├── rustix v1.0.7
    │   └── tempfile v3.20.0
    │       └── prost-build v0.13.5
    │           └── tonic-build v0.13.1
    │               └── (build) rustycluster v2.2.7 (*)
    └── wit-bindgen-rt v0.39.0
        └── wasi v0.14.2+wasi-0.2.4
            └── getrandom v0.3.3
                ├── tempfile v3.20.0 (*)
                └── uuid v1.17.0
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#block-buffer@0.10.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ block-buffer v0.10.4
    └── digest v0.10.7
        ├── hmac v0.12.1
        │   └── pbkdf2 v0.12.2
        │       └── rustycluster v2.2.7
        ├── pbkdf2 v0.12.2 (*)
        └── sha2 v0.10.9
            ├── (build) pest_meta v2.8.1
            │   └── pest_generator v2.8.1
            │       └── pest_derive v2.8.1
            │           └── json5 v0.4.1
            │               └── config v0.13.4
            │                   └── rustycluster v2.2.7 (*)
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#bumpalo@3.18.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ bumpalo v3.18.1
    └── wasm-bindgen-backend v0.2.100
        └── wasm-bindgen-macro-support v0.2.100
            └── wasm-bindgen-macro v0.2.100
                └── wasm-bindgen v0.2.100
                    ├── iana-time-zone v0.1.63
                    │   └── chrono v0.4.41
                    │       └── tracing-subscriber v0.3.19
                    │           ├── rustycluster v2.2.7
                    │           └── tracing-appender v0.2.3
                    │               └── rustycluster v2.2.7 (*)
                    └── js-sys v0.3.77
                        └── iana-time-zone v0.1.63 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#bytes@1.10.1:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ bytes v1.10.1
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── axum-core v0.5.2
    │   └── axum v0.8.4 (*)
    ├── combine v4.6.7
    │   ├── redis v0.29.5
    │   │   └── deadpool-redis v0.20.0
    │   │       └── rustycluster v2.2.7 (*)
    │   └── redis v0.31.0
    │       └── rustycluster v2.2.7 (*)
    ├── h2 v0.4.10
    │   ├── hyper v1.6.0
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper-util v0.1.14
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tonic v0.13.1 (*)
    ├── http v1.3.1
    │   ├── axum v0.8.4 (*)
    │   ├── axum-core v0.5.2 (*)
    │   ├── h2 v0.4.10 (*)
    │   ├── http-body v1.0.1
    │   │   ├── axum v0.8.4 (*)
    │   │   ├── axum-core v0.5.2 (*)
    │   │   ├── http-body-util v0.1.3
    │   │   │   ├── axum v0.8.4 (*)
    │   │   │   ├── axum-core v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper v1.6.0 (*)
    │   │   ├── hyper-util v0.1.14 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── http-body-util v0.1.3 (*)
    │   ├── hyper v1.6.0 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   └── tonic v0.13.1 (*)
    ├── http-body v1.0.1 (*)
    ├── http-body-util v0.1.3 (*)
    ├── hyper v1.6.0 (*)
    ├── hyper-util v0.1.14 (*)
    ├── prost v0.13.5
    │   ├── prost-build v0.13.5
    │   │   └── tonic-build v0.13.1
    │   │       └── (build) rustycluster v2.2.7 (*)
    │   ├── prost-types v0.13.5
    │   │   ├── prost-build v0.13.5 (*)
    │   │   ├── rustycluster v2.2.7 (*)
    │   │   └── tonic-build v0.13.1 (*)
    │   ├── rustycluster v2.2.7 (*)
    │   └── tonic v0.13.1 (*)
    ├── redis v0.29.5 (*)
    ├── redis v0.31.0 (*)
    ├── tokio v1.45.1
    │   ├── combine v4.6.7 (*)
    │   ├── deadpool v0.12.2
    │   │   └── deadpool-redis v0.20.0 (*)
    │   ├── deadpool-runtime v0.1.4
    │   │   └── deadpool v0.12.2 (*)
    │   ├── h2 v0.4.10 (*)
    │   ├── hyper v1.6.0 (*)
    │   ├── hyper-timeout v0.5.2 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   ├── redis v0.29.5 (*)
    │   ├── redis v0.31.0 (*)
    │   ├── rustycluster v2.2.7 (*)
    │   ├── tokio-stream v0.1.17
    │   │   └── tonic v0.13.1 (*)
    │   ├── tokio-util v0.7.15
    │   │   ├── combine v4.6.7 (*)
    │   │   ├── h2 v0.4.10 (*)
    │   │   ├── redis v0.29.5 (*)
    │   │   ├── redis v0.31.0 (*)
    │   │   └── tower v0.5.2
    │   │       ├── axum v0.8.4 (*)
    │   │       └── tonic v0.13.1 (*)
    │   ├── tonic v0.13.1 (*)
    │   └── tower v0.5.2 (*)
    ├── tokio-util v0.7.15 (*)
    └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#cc@1.2.27:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ cc v1.2.27
    └── (build) iana-time-zone-haiku v0.1.2
        └── iana-time-zone v0.1.63
            └── chrono v0.4.41
                └── tracing-subscriber v0.3.19
                    ├── rustycluster v2.2.7
                    └── tracing-appender v0.2.3
                        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#cfg-if@1.0.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ cfg-if v1.0.1
    ├── aes v0.8.4
    │   └── aes-gcm v0.10.3
    │       └── rustycluster v2.2.7
    ├── backtrace v0.3.75
    │   └── tokio v1.45.1
    │       ├── combine v4.6.7
    │       │   ├── redis v0.29.5
    │       │   │   └── deadpool-redis v0.20.0
    │       │   │       └── rustycluster v2.2.7 (*)
    │       │   └── redis v0.31.0
    │       │       └── rustycluster v2.2.7 (*)
    │       ├── deadpool v0.12.2
    │       │   └── deadpool-redis v0.20.0 (*)
    │       ├── deadpool-runtime v0.1.4
    │       │   └── deadpool v0.12.2 (*)
    │       ├── h2 v0.4.10
    │       │   ├── hyper v1.6.0
    │       │   │   ├── hyper-timeout v0.5.2
    │       │   │   │   └── tonic v0.13.1
    │       │   │   │       └── rustycluster v2.2.7 (*)
    │       │   │   ├── hyper-util v0.1.14
    │       │   │   │   ├── hyper-timeout v0.5.2 (*)
    │       │   │   │   └── tonic v0.13.1 (*)
    │       │   │   └── tonic v0.13.1 (*)
    │       │   └── tonic v0.13.1 (*)
    │       ├── hyper v1.6.0 (*)
    │       ├── hyper-timeout v0.5.2 (*)
    │       ├── hyper-util v0.1.14 (*)
    │       ├── redis v0.29.5 (*)
    │       ├── redis v0.31.0 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       ├── tokio-stream v0.1.17
    │       │   └── tonic v0.13.1 (*)
    │       ├── tokio-util v0.7.15
    │       │   ├── combine v4.6.7 (*)
    │       │   ├── h2 v0.4.10 (*)
    │       │   ├── redis v0.29.5 (*)
    │       │   ├── redis v0.31.0 (*)
    │       │   └── tower v0.5.2
    │       │       ├── axum v0.8.4
    │       │       │   └── tonic v0.13.1 (*)
    │       │       └── tonic v0.13.1 (*)
    │       ├── tonic v0.13.1 (*)
    │       └── tower v0.5.2 (*)
    ├── dashmap v6.1.0
    │   └── rustycluster v2.2.7 (*)
    ├── getrandom v0.2.16
    │   └── rand_core v0.6.4
    │       ├── crypto-common v0.1.6
    │       │   ├── aead v0.5.2
    │       │   │   └── aes-gcm v0.10.3 (*)
    │       │   ├── cipher v0.4.4
    │       │   │   ├── aes v0.8.4 (*)
    │       │   │   ├── aes-gcm v0.10.3 (*)
    │       │   │   └── ctr v0.9.2
    │       │   │       └── aes-gcm v0.10.3 (*)
    │       │   ├── digest v0.10.7
    │       │   │   ├── hmac v0.12.1
    │       │   │   │   └── pbkdf2 v0.12.2
    │       │   │   │       └── rustycluster v2.2.7 (*)
    │       │   │   ├── pbkdf2 v0.12.2 (*)
    │       │   │   └── sha2 v0.10.9
    │       │   │       ├── (build) pest_meta v2.8.1
    │       │   │       │   └── pest_generator v2.8.1
    │       │   │       │       └── pest_derive v2.8.1
    │       │   │       │           └── json5 v0.4.1
    │       │   │       │               └── config v0.13.4
    │       │   │       │                   └── rustycluster v2.2.7 (*)
    │       │   │       └── rustycluster v2.2.7 (*)
    │       │   └── universal-hash v0.5.1
    │       │       └── polyval v0.6.2
    │       │           └── ghash v0.5.1
    │       │               └── aes-gcm v0.10.3 (*)
    │       ├── rand v0.8.5
    │       │   └── rustycluster v2.2.7 (*)
    │       └── rand_chacha v0.3.1
    │           └── rand v0.8.5 (*)
    ├── getrandom v0.3.3
    │   ├── tempfile v3.20.0
    │   │   └── prost-build v0.13.5
    │   │       └── tonic-build v0.13.1
    │   │           └── (build) rustycluster v2.2.7 (*)
    │   └── uuid v1.17.0
    │       └── rustycluster v2.2.7 (*)
    ├── parking_lot_core v0.9.11
    │   ├── dashmap v6.1.0 (*)
    │   └── parking_lot v0.12.4
    │       ├── prometheus v0.14.0
    │       │   └── rustycluster v2.2.7 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       └── tokio v1.45.1 (*)
    ├── polyval v0.6.2 (*)
    ├── prometheus v0.14.0 (*)
    ├── redis v0.31.0 (*)
    ├── sha2 v0.10.9 (*)
    ├── thread_local v1.1.9
    │   └── tracing-subscriber v0.3.19
    │       ├── rustycluster v2.2.7 (*)
    │       └── tracing-appender v0.2.3
    │           └── rustycluster v2.2.7 (*)
    └── wasm-bindgen v0.2.100
        ├── iana-time-zone v0.1.63
        │   └── chrono v0.4.41
        │       └── tracing-subscriber v0.3.19 (*)
        └── js-sys v0.3.77
            └── iana-time-zone v0.1.63 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#chrono@0.4.41:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ chrono v0.4.41
    └── tracing-subscriber v0.3.19
        ├── rustycluster v2.2.7
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#cipher@0.4.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ cipher v0.4.4
    ├── aes v0.8.4
    │   └── aes-gcm v0.10.3
    │       └── rustycluster v2.2.7
    ├── aes-gcm v0.10.3 (*)
    └── ctr v0.9.2
        └── aes-gcm v0.10.3 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#combine@4.6.7:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ combine v4.6.7
    ├── redis v0.29.5
    │   └── deadpool-redis v0.20.0
    │       └── rustycluster v2.2.7
    └── redis v0.31.0
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#config@0.13.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ config v0.13.4
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#core-foundation-sys@0.8.7:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ core-foundation-sys v0.8.7
    └── iana-time-zone v0.1.63
        └── chrono v0.4.41
            └── tracing-subscriber v0.3.19
                ├── rustycluster v2.2.7
                └── tracing-appender v0.2.3
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#cpufeatures@0.2.17:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ cpufeatures v0.2.17
    ├── aes v0.8.4
    │   └── aes-gcm v0.10.3
    │       └── rustycluster v2.2.7
    ├── polyval v0.6.2
    │   └── ghash v0.5.1
    │       └── aes-gcm v0.10.3 (*)
    └── sha2 v0.10.9
        ├── (build) pest_meta v2.8.1
        │   └── pest_generator v2.8.1
        │       └── pest_derive v2.8.1
        │           └── json5 v0.4.1
        │               └── config v0.13.4
        │                   └── rustycluster v2.2.7 (*)
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#crossbeam-channel@0.5.15:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ crossbeam-channel v0.5.15
    └── tracing-appender v0.2.3
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#crossbeam-utils@0.8.21:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ crossbeam-utils v0.8.21
    ├── crossbeam-channel v0.5.15
    │   └── tracing-appender v0.2.3
    │       └── rustycluster v2.2.7
    └── dashmap v6.1.0
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#crypto-common@0.1.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ crypto-common v0.1.6
    ├── aead v0.5.2
    │   └── aes-gcm v0.10.3
    │       └── rustycluster v2.2.7
    ├── cipher v0.4.4
    │   ├── aes v0.8.4
    │   │   └── aes-gcm v0.10.3 (*)
    │   ├── aes-gcm v0.10.3 (*)
    │   └── ctr v0.9.2
    │       └── aes-gcm v0.10.3 (*)
    ├── digest v0.10.7
    │   ├── hmac v0.12.1
    │   │   └── pbkdf2 v0.12.2
    │   │       └── rustycluster v2.2.7 (*)
    │   ├── pbkdf2 v0.12.2 (*)
    │   └── sha2 v0.10.9
    │       ├── (build) pest_meta v2.8.1
    │       │   └── pest_generator v2.8.1
    │       │       └── pest_derive v2.8.1
    │       │           └── json5 v0.4.1
    │       │               └── config v0.13.4
    │       │                   └── rustycluster v2.2.7 (*)
    │       └── rustycluster v2.2.7 (*)
    └── universal-hash v0.5.1
        └── polyval v0.6.2
            └── ghash v0.5.1
                └── aes-gcm v0.10.3 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#ctr@0.9.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ ctr v0.9.2
    └── aes-gcm v0.10.3
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#dashmap@6.1.0:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ dashmap v6.1.0
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#deadpool@0.12.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ deadpool v0.12.2
    └── deadpool-redis v0.20.0
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#deadpool-redis@0.20.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ deadpool-redis v0.20.0
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#deadpool-runtime@0.1.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ deadpool-runtime v0.1.4
    └── deadpool v0.12.2
        └── deadpool-redis v0.20.0
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#deranged@0.4.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ deranged v0.4.0
    └── time v0.3.41
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#digest@0.10.7:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ digest v0.10.7
    ├── hmac v0.12.1
    │   └── pbkdf2 v0.12.2
    │       └── rustycluster v2.2.7
    ├── pbkdf2 v0.12.2 (*)
    └── sha2 v0.10.9
        ├── (build) pest_meta v2.8.1
        │   └── pest_generator v2.8.1
        │       └── pest_derive v2.8.1
        │           └── json5 v0.4.1
        │               └── config v0.13.4
        │                   └── rustycluster v2.2.7 (*)
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#displaydoc@0.2.5:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ displaydoc v0.2.5
    ├── icu_collections v2.0.0
    │   ├── icu_normalizer v2.0.0
    │   │   └── idna_adapter v1.2.1
    │   │       └── idna v1.0.3
    │   │           └── url v2.5.4
    │   │               ├── redis v0.29.5
    │   │               │   └── deadpool-redis v0.20.0
    │   │               │       └── rustycluster v2.2.7
    │   │               └── redis v0.31.0
    │   │                   └── rustycluster v2.2.7 (*)
    │   └── icu_properties v2.0.1
    │       └── idna_adapter v1.2.1 (*)
    ├── icu_locale_core v2.0.0
    │   ├── icu_properties v2.0.1 (*)
    │   └── icu_provider v2.0.0
    │       ├── icu_normalizer v2.0.0 (*)
    │       └── icu_properties v2.0.1 (*)
    ├── icu_normalizer v2.0.0 (*)
    ├── icu_properties v2.0.1 (*)
    ├── icu_provider v2.0.0 (*)
    ├── tinystr v0.8.1
    │   ├── icu_locale_core v2.0.0 (*)
    │   └── icu_provider v2.0.0 (*)
    └── zerotrie v0.2.2
        ├── icu_properties v2.0.1 (*)
        └── icu_provider v2.0.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#either@1.15.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ either v1.15.0
    └── itertools v0.14.0
        ├── prost-build v0.13.5
        │   └── tonic-build v0.13.1
        │       └── (build) rustycluster v2.2.7
        └── prost-derive v0.13.5
            └── prost v0.13.5
                ├── prost-build v0.13.5 (*)
                ├── prost-types v0.13.5
                │   ├── prost-build v0.13.5 (*)
                │   ├── rustycluster v2.2.7 (*)
                │   └── tonic-build v0.13.1 (*)
                ├── rustycluster v2.2.7 (*)
                └── tonic v0.13.1
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#equivalent@1.0.2:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ equivalent v1.0.2
    └── indexmap v2.9.0
        ├── h2 v0.4.10
        │   ├── hyper v1.6.0
        │   │   ├── hyper-timeout v0.5.2
        │   │   │   └── tonic v0.13.1
        │   │   │       └── rustycluster v2.2.7
        │   │   ├── hyper-util v0.1.14
        │   │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   │   └── tonic v0.13.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── petgraph v0.7.1
        │   └── prost-build v0.13.5
        │       └── tonic-build v0.13.1
        │           └── (build) rustycluster v2.2.7 (*)
        └── tower v0.5.2
            ├── axum v0.8.4
            │   └── tonic v0.13.1 (*)
            └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#errno@0.3.13:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ errno v0.3.13
    └── rustix v1.0.7
        └── tempfile v3.20.0
            └── prost-build v0.13.5
                └── tonic-build v0.13.1
                    └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#fastrand@2.3.0:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ fastrand v2.3.0
    └── tempfile v3.20.0
        └── prost-build v0.13.5
            └── tonic-build v0.13.1
                └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#fixedbitset@0.5.7:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ fixedbitset v0.5.7
    └── petgraph v0.7.1
        └── prost-build v0.13.5
            └── tonic-build v0.13.1
                └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#fnv@1.0.7:4:12
  │
4 │ license = "Apache-2.0  OR  MIT"
  │            ━━━━━━━━━━──────━━━
  │            │               │
  │            │               rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ fnv v1.0.7
    ├── h2 v0.4.10
    │   ├── hyper v1.6.0
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1
    │   │   │       └── rustycluster v2.2.7
    │   │   ├── hyper-util v0.1.14
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tonic v0.13.1 (*)
    ├── http v1.3.1
    │   ├── axum v0.8.4
    │   │   └── tonic v0.13.1 (*)
    │   ├── axum-core v0.5.2
    │   │   └── axum v0.8.4 (*)
    │   ├── h2 v0.4.10 (*)
    │   ├── http-body v1.0.1
    │   │   ├── axum v0.8.4 (*)
    │   │   ├── axum-core v0.5.2 (*)
    │   │   ├── http-body-util v0.1.3
    │   │   │   ├── axum v0.8.4 (*)
    │   │   │   ├── axum-core v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper v1.6.0 (*)
    │   │   ├── hyper-util v0.1.14 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── http-body-util v0.1.3 (*)
    │   ├── hyper v1.6.0 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   └── tonic v0.13.1 (*)
    └── prometheus v0.14.0
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#form_urlencoded@1.2.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ form_urlencoded v1.2.1
    └── url v2.5.4
        ├── redis v0.29.5
        │   └── deadpool-redis v0.20.0
        │       └── rustycluster v2.2.7
        └── redis v0.31.0
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#futures@0.3.31:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ futures v0.3.31
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#futures-channel@0.3.31:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ futures-channel v0.3.31
    ├── futures v0.3.31
    │   └── rustycluster v2.2.7
    ├── futures-util v0.3.31
    │   ├── axum v0.8.4
    │   │   └── tonic v0.13.1
    │   │       └── rustycluster v2.2.7 (*)
    │   ├── futures v0.3.31 (*)
    │   ├── futures-executor v0.3.31
    │   │   └── futures v0.3.31 (*)
    │   ├── hyper v1.6.0
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper-util v0.1.14
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   ├── redis v0.29.5
    │   │   └── deadpool-redis v0.20.0
    │   │       └── rustycluster v2.2.7 (*)
    │   ├── redis v0.31.0
    │   │   └── rustycluster v2.2.7 (*)
    │   └── tower v0.5.2
    │       ├── axum v0.8.4 (*)
    │       └── tonic v0.13.1 (*)
    ├── hyper v1.6.0 (*)
    └── hyper-util v0.1.14 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#futures-core@0.3.31:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ futures-core v0.3.31
    ├── axum-core v0.5.2
    │   └── axum v0.8.4
    │       └── tonic v0.13.1
    │           └── rustycluster v2.2.7
    ├── combine v4.6.7
    │   ├── redis v0.29.5
    │   │   └── deadpool-redis v0.20.0
    │   │       └── rustycluster v2.2.7 (*)
    │   └── redis v0.31.0
    │       └── rustycluster v2.2.7 (*)
    ├── futures v0.3.31
    │   └── rustycluster v2.2.7 (*)
    ├── futures-channel v0.3.31
    │   ├── futures v0.3.31 (*)
    │   ├── futures-util v0.3.31
    │   │   ├── axum v0.8.4 (*)
    │   │   ├── futures v0.3.31 (*)
    │   │   ├── futures-executor v0.3.31
    │   │   │   └── futures v0.3.31 (*)
    │   │   ├── hyper v1.6.0
    │   │   │   ├── hyper-timeout v0.5.2
    │   │   │   │   └── tonic v0.13.1 (*)
    │   │   │   ├── hyper-util v0.1.14
    │   │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   │   └── tonic v0.13.1 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper-util v0.1.14 (*)
    │   │   ├── redis v0.29.5 (*)
    │   │   ├── redis v0.31.0 (*)
    │   │   └── tower v0.5.2
    │   │       ├── axum v0.8.4 (*)
    │   │       └── tonic v0.13.1 (*)
    │   ├── hyper v1.6.0 (*)
    │   └── hyper-util v0.1.14 (*)
    ├── futures-executor v0.3.31 (*)
    ├── futures-util v0.3.31 (*)
    ├── h2 v0.4.10
    │   ├── hyper v1.6.0 (*)
    │   └── tonic v0.13.1 (*)
    ├── http-body-util v0.1.3
    │   ├── axum v0.8.4 (*)
    │   ├── axum-core v0.5.2 (*)
    │   └── tonic v0.13.1 (*)
    ├── hyper-util v0.1.14 (*)
    ├── tokio-stream v0.1.17
    │   └── tonic v0.13.1 (*)
    ├── tokio-util v0.7.15
    │   ├── combine v4.6.7 (*)
    │   ├── h2 v0.4.10 (*)
    │   ├── redis v0.29.5 (*)
    │   ├── redis v0.31.0 (*)
    │   └── tower v0.5.2 (*)
    └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#futures-executor@0.3.31:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ futures-executor v0.3.31
    └── futures v0.3.31
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#futures-io@0.3.31:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ futures-io v0.3.31
    ├── futures v0.3.31
    │   └── rustycluster v2.2.7
    └── futures-util v0.3.31
        ├── axum v0.8.4
        │   └── tonic v0.13.1
        │       └── rustycluster v2.2.7 (*)
        ├── futures v0.3.31 (*)
        ├── futures-executor v0.3.31
        │   └── futures v0.3.31 (*)
        ├── hyper v1.6.0
        │   ├── hyper-timeout v0.5.2
        │   │   └── tonic v0.13.1 (*)
        │   ├── hyper-util v0.1.14
        │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── hyper-util v0.1.14 (*)
        ├── redis v0.29.5
        │   └── deadpool-redis v0.20.0
        │       └── rustycluster v2.2.7 (*)
        ├── redis v0.31.0
        │   └── rustycluster v2.2.7 (*)
        └── tower v0.5.2
            ├── axum v0.8.4 (*)
            └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#futures-macro@0.3.31:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ futures-macro v0.3.31
    └── futures-util v0.3.31
        ├── axum v0.8.4
        │   └── tonic v0.13.1
        │       └── rustycluster v2.2.7
        ├── futures v0.3.31
        │   └── rustycluster v2.2.7 (*)
        ├── futures-executor v0.3.31
        │   └── futures v0.3.31 (*)
        ├── hyper v1.6.0
        │   ├── hyper-timeout v0.5.2
        │   │   └── tonic v0.13.1 (*)
        │   ├── hyper-util v0.1.14
        │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── hyper-util v0.1.14 (*)
        ├── redis v0.29.5
        │   └── deadpool-redis v0.20.0
        │       └── rustycluster v2.2.7 (*)
        ├── redis v0.31.0
        │   └── rustycluster v2.2.7 (*)
        └── tower v0.5.2
            ├── axum v0.8.4 (*)
            └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#futures-sink@0.3.31:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ futures-sink v0.3.31
    ├── futures v0.3.31
    │   └── rustycluster v2.2.7
    ├── futures-channel v0.3.31
    │   ├── futures v0.3.31 (*)
    │   ├── futures-util v0.3.31
    │   │   ├── axum v0.8.4
    │   │   │   └── tonic v0.13.1
    │   │   │       └── rustycluster v2.2.7 (*)
    │   │   ├── futures v0.3.31 (*)
    │   │   ├── futures-executor v0.3.31
    │   │   │   └── futures v0.3.31 (*)
    │   │   ├── hyper v1.6.0
    │   │   │   ├── hyper-timeout v0.5.2
    │   │   │   │   └── tonic v0.13.1 (*)
    │   │   │   ├── hyper-util v0.1.14
    │   │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   │   └── tonic v0.13.1 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper-util v0.1.14 (*)
    │   │   ├── redis v0.29.5
    │   │   │   └── deadpool-redis v0.20.0
    │   │   │       └── rustycluster v2.2.7 (*)
    │   │   ├── redis v0.31.0
    │   │   │   └── rustycluster v2.2.7 (*)
    │   │   └── tower v0.5.2
    │   │       ├── axum v0.8.4 (*)
    │   │       └── tonic v0.13.1 (*)
    │   ├── hyper v1.6.0 (*)
    │   └── hyper-util v0.1.14 (*)
    ├── futures-util v0.3.31 (*)
    ├── h2 v0.4.10
    │   ├── hyper v1.6.0 (*)
    │   └── tonic v0.13.1 (*)
    └── tokio-util v0.7.15
        ├── combine v4.6.7
        │   ├── redis v0.29.5 (*)
        │   └── redis v0.31.0 (*)
        ├── h2 v0.4.10 (*)
        ├── redis v0.29.5 (*)
        ├── redis v0.31.0 (*)
        └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#futures-task@0.3.31:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ futures-task v0.3.31
    ├── futures v0.3.31
    │   └── rustycluster v2.2.7
    ├── futures-executor v0.3.31
    │   └── futures v0.3.31 (*)
    └── futures-util v0.3.31
        ├── axum v0.8.4
        │   └── tonic v0.13.1
        │       └── rustycluster v2.2.7 (*)
        ├── futures v0.3.31 (*)
        ├── futures-executor v0.3.31 (*)
        ├── hyper v1.6.0
        │   ├── hyper-timeout v0.5.2
        │   │   └── tonic v0.13.1 (*)
        │   ├── hyper-util v0.1.14
        │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── hyper-util v0.1.14 (*)
        ├── redis v0.29.5
        │   └── deadpool-redis v0.20.0
        │       └── rustycluster v2.2.7 (*)
        ├── redis v0.31.0
        │   └── rustycluster v2.2.7 (*)
        └── tower v0.5.2
            ├── axum v0.8.4 (*)
            └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#futures-util@0.3.31:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ futures-util v0.3.31
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── futures v0.3.31
    │   └── rustycluster v2.2.7 (*)
    ├── futures-executor v0.3.31
    │   └── futures v0.3.31 (*)
    ├── hyper v1.6.0
    │   ├── hyper-timeout v0.5.2
    │   │   └── tonic v0.13.1 (*)
    │   ├── hyper-util v0.1.14
    │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tonic v0.13.1 (*)
    ├── hyper-util v0.1.14 (*)
    ├── redis v0.29.5
    │   └── deadpool-redis v0.20.0
    │       └── rustycluster v2.2.7 (*)
    ├── redis v0.31.0
    │   └── rustycluster v2.2.7 (*)
    └── tower v0.5.2
        ├── axum v0.8.4 (*)
        └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#generic-array@0.14.7:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ generic-array v0.14.7
    ├── aead v0.5.2
    │   └── aes-gcm v0.10.3
    │       └── rustycluster v2.2.7
    ├── block-buffer v0.10.4
    │   └── digest v0.10.7
    │       ├── hmac v0.12.1
    │       │   └── pbkdf2 v0.12.2
    │       │       └── rustycluster v2.2.7 (*)
    │       ├── pbkdf2 v0.12.2 (*)
    │       └── sha2 v0.10.9
    │           ├── (build) pest_meta v2.8.1
    │           │   └── pest_generator v2.8.1
    │           │       └── pest_derive v2.8.1
    │           │           └── json5 v0.4.1
    │           │               └── config v0.13.4
    │           │                   └── rustycluster v2.2.7 (*)
    │           └── rustycluster v2.2.7 (*)
    ├── crypto-common v0.1.6
    │   ├── aead v0.5.2 (*)
    │   ├── cipher v0.4.4
    │   │   ├── aes v0.8.4
    │   │   │   └── aes-gcm v0.10.3 (*)
    │   │   ├── aes-gcm v0.10.3 (*)
    │   │   └── ctr v0.9.2
    │   │       └── aes-gcm v0.10.3 (*)
    │   ├── digest v0.10.7 (*)
    │   └── universal-hash v0.5.1
    │       └── polyval v0.6.2
    │           └── ghash v0.5.1
    │               └── aes-gcm v0.10.3 (*)
    └── inout v0.1.4
        └── cipher v0.4.4 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#getrandom@0.2.16:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ getrandom v0.2.16
    └── rand_core v0.6.4
        ├── crypto-common v0.1.6
        │   ├── aead v0.5.2
        │   │   └── aes-gcm v0.10.3
        │   │       └── rustycluster v2.2.7
        │   ├── cipher v0.4.4
        │   │   ├── aes v0.8.4
        │   │   │   └── aes-gcm v0.10.3 (*)
        │   │   ├── aes-gcm v0.10.3 (*)
        │   │   └── ctr v0.9.2
        │   │       └── aes-gcm v0.10.3 (*)
        │   ├── digest v0.10.7
        │   │   ├── hmac v0.12.1
        │   │   │   └── pbkdf2 v0.12.2
        │   │   │       └── rustycluster v2.2.7 (*)
        │   │   ├── pbkdf2 v0.12.2 (*)
        │   │   └── sha2 v0.10.9
        │   │       ├── (build) pest_meta v2.8.1
        │   │       │   └── pest_generator v2.8.1
        │   │       │       └── pest_derive v2.8.1
        │   │       │           └── json5 v0.4.1
        │   │       │               └── config v0.13.4
        │   │       │                   └── rustycluster v2.2.7 (*)
        │   │       └── rustycluster v2.2.7 (*)
        │   └── universal-hash v0.5.1
        │       └── polyval v0.6.2
        │           └── ghash v0.5.1
        │               └── aes-gcm v0.10.3 (*)
        ├── rand v0.8.5
        │   └── rustycluster v2.2.7 (*)
        └── rand_chacha v0.3.1
            └── rand v0.8.5 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#getrandom@0.3.3:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ getrandom v0.3.3
    ├── tempfile v3.20.0
    │   └── prost-build v0.13.5
    │       └── tonic-build v0.13.1
    │           └── (build) rustycluster v2.2.7
    └── uuid v1.17.0
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#ghash@0.5.1:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ ghash v0.5.1
    └── aes-gcm v0.10.3
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#gimli@0.31.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ gimli v0.31.1
    └── addr2line v0.24.2
        └── backtrace v0.3.75
            └── tokio v1.45.1
                ├── combine v4.6.7
                │   ├── redis v0.29.5
                │   │   └── deadpool-redis v0.20.0
                │   │       └── rustycluster v2.2.7
                │   └── redis v0.31.0
                │       └── rustycluster v2.2.7 (*)
                ├── deadpool v0.12.2
                │   └── deadpool-redis v0.20.0 (*)
                ├── deadpool-runtime v0.1.4
                │   └── deadpool v0.12.2 (*)
                ├── h2 v0.4.10
                │   ├── hyper v1.6.0
                │   │   ├── hyper-timeout v0.5.2
                │   │   │   └── tonic v0.13.1
                │   │   │       └── rustycluster v2.2.7 (*)
                │   │   ├── hyper-util v0.1.14
                │   │   │   ├── hyper-timeout v0.5.2 (*)
                │   │   │   └── tonic v0.13.1 (*)
                │   │   └── tonic v0.13.1 (*)
                │   └── tonic v0.13.1 (*)
                ├── hyper v1.6.0 (*)
                ├── hyper-timeout v0.5.2 (*)
                ├── hyper-util v0.1.14 (*)
                ├── redis v0.29.5 (*)
                ├── redis v0.31.0 (*)
                ├── rustycluster v2.2.7 (*)
                ├── tokio-stream v0.1.17
                │   └── tonic v0.13.1 (*)
                ├── tokio-util v0.7.15
                │   ├── combine v4.6.7 (*)
                │   ├── h2 v0.4.10 (*)
                │   ├── redis v0.29.5 (*)
                │   ├── redis v0.31.0 (*)
                │   └── tower v0.5.2
                │       ├── axum v0.8.4
                │       │   └── tonic v0.13.1 (*)
                │       └── tonic v0.13.1 (*)
                ├── tonic v0.13.1 (*)
                └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#h2@0.4.10:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ h2 v0.4.10
    ├── hyper v1.6.0
    │   ├── hyper-timeout v0.5.2
    │   │   └── tonic v0.13.1
    │   │       └── rustycluster v2.2.7
    │   ├── hyper-util v0.1.14
    │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tonic v0.13.1 (*)
    └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#hashbrown@0.14.5:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ hashbrown v0.14.5
    └── dashmap v6.1.0
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#hashbrown@0.15.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ hashbrown v0.15.4
    └── indexmap v2.9.0
        ├── h2 v0.4.10
        │   ├── hyper v1.6.0
        │   │   ├── hyper-timeout v0.5.2
        │   │   │   └── tonic v0.13.1
        │   │   │       └── rustycluster v2.2.7
        │   │   ├── hyper-util v0.1.14
        │   │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   │   └── tonic v0.13.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── petgraph v0.7.1
        │   └── prost-build v0.13.5
        │       └── tonic-build v0.13.1
        │           └── (build) rustycluster v2.2.7 (*)
        └── tower v0.5.2
            ├── axum v0.8.4
            │   └── tonic v0.13.1 (*)
            └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#heck@0.5.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ heck v0.5.0
    └── prost-build v0.13.5
        └── tonic-build v0.13.1
            └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#hermit-abi@0.5.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ hermit-abi v0.5.2
    └── num_cpus v1.17.0
        ├── deadpool v0.12.2
        │   └── deadpool-redis v0.20.0
        │       └── rustycluster v2.2.7
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#hmac@0.12.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ hmac v0.12.1
    └── pbkdf2 v0.12.2
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#http@1.3.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ http v1.3.1
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── axum-core v0.5.2
    │   └── axum v0.8.4 (*)
    ├── h2 v0.4.10
    │   ├── hyper v1.6.0
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper-util v0.1.14
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tonic v0.13.1 (*)
    ├── http-body v1.0.1
    │   ├── axum v0.8.4 (*)
    │   ├── axum-core v0.5.2 (*)
    │   ├── http-body-util v0.1.3
    │   │   ├── axum v0.8.4 (*)
    │   │   ├── axum-core v0.5.2 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── hyper v1.6.0 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   └── tonic v0.13.1 (*)
    ├── http-body-util v0.1.3 (*)
    ├── hyper v1.6.0 (*)
    ├── hyper-util v0.1.14 (*)
    └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#http-body@1.0.1:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ http-body v1.0.1
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── axum-core v0.5.2
    │   └── axum v0.8.4 (*)
    ├── http-body-util v0.1.3
    │   ├── axum v0.8.4 (*)
    │   ├── axum-core v0.5.2 (*)
    │   └── tonic v0.13.1 (*)
    ├── hyper v1.6.0
    │   ├── hyper-timeout v0.5.2
    │   │   └── tonic v0.13.1 (*)
    │   ├── hyper-util v0.1.14
    │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tonic v0.13.1 (*)
    ├── hyper-util v0.1.14 (*)
    └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#http-body-util@0.1.3:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ http-body-util v0.1.3
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── axum-core v0.5.2
    │   └── axum v0.8.4 (*)
    └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#httparse@1.10.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ httparse v1.10.1
    └── hyper v1.6.0
        ├── hyper-timeout v0.5.2
        │   └── tonic v0.13.1
        │       └── rustycluster v2.2.7
        ├── hyper-util v0.1.14
        │   ├── hyper-timeout v0.5.2 (*)
        │   └── tonic v0.13.1 (*)
        └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#httpdate@1.0.3:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ httpdate v1.0.3
    └── hyper v1.6.0
        ├── hyper-timeout v0.5.2
        │   └── tonic v0.13.1
        │       └── rustycluster v2.2.7
        ├── hyper-util v0.1.14
        │   ├── hyper-timeout v0.5.2 (*)
        │   └── tonic v0.13.1 (*)
        └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#hyper@1.6.0:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ hyper v1.6.0
    ├── hyper-timeout v0.5.2
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── hyper-util v0.1.14
    │   ├── hyper-timeout v0.5.2 (*)
    │   └── tonic v0.13.1 (*)
    └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#hyper-timeout@0.5.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ hyper-timeout v0.5.2
    └── tonic v0.13.1
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#hyper-util@0.1.14:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ hyper-util v0.1.14
    ├── hyper-timeout v0.5.2
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#iana-time-zone@0.1.63:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ iana-time-zone v0.1.63
    └── chrono v0.4.41
        └── tracing-subscriber v0.3.19
            ├── rustycluster v2.2.7
            └── tracing-appender v0.2.3
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#iana-time-zone-haiku@0.1.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ iana-time-zone-haiku v0.1.2
    └── iana-time-zone v0.1.63
        └── chrono v0.4.41
            └── tracing-subscriber v0.3.19
                ├── rustycluster v2.2.7
                └── tracing-appender v0.2.3
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#icu_collections@2.0.0:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ icu_collections v2.0.0
    ├── icu_normalizer v2.0.0
    │   └── idna_adapter v1.2.1
    │       └── idna v1.0.3
    │           └── url v2.5.4
    │               ├── redis v0.29.5
    │               │   └── deadpool-redis v0.20.0
    │               │       └── rustycluster v2.2.7
    │               └── redis v0.31.0
    │                   └── rustycluster v2.2.7 (*)
    └── icu_properties v2.0.1
        └── idna_adapter v1.2.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#icu_locale_core@2.0.0:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ icu_locale_core v2.0.0
    ├── icu_properties v2.0.1
    │   └── idna_adapter v1.2.1
    │       └── idna v1.0.3
    │           └── url v2.5.4
    │               ├── redis v0.29.5
    │               │   └── deadpool-redis v0.20.0
    │               │       └── rustycluster v2.2.7
    │               └── redis v0.31.0
    │                   └── rustycluster v2.2.7 (*)
    └── icu_provider v2.0.0
        ├── icu_normalizer v2.0.0
        │   └── idna_adapter v1.2.1 (*)
        └── icu_properties v2.0.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#icu_normalizer@2.0.0:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ icu_normalizer v2.0.0
    └── idna_adapter v1.2.1
        └── idna v1.0.3
            └── url v2.5.4
                ├── redis v0.29.5
                │   └── deadpool-redis v0.20.0
                │       └── rustycluster v2.2.7
                └── redis v0.31.0
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#icu_normalizer_data@2.0.0:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ icu_normalizer_data v2.0.0
    └── icu_normalizer v2.0.0
        └── idna_adapter v1.2.1
            └── idna v1.0.3
                └── url v2.5.4
                    ├── redis v0.29.5
                    │   └── deadpool-redis v0.20.0
                    │       └── rustycluster v2.2.7
                    └── redis v0.31.0
                        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#icu_properties@2.0.1:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ icu_properties v2.0.1
    └── idna_adapter v1.2.1
        └── idna v1.0.3
            └── url v2.5.4
                ├── redis v0.29.5
                │   └── deadpool-redis v0.20.0
                │       └── rustycluster v2.2.7
                └── redis v0.31.0
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#icu_properties_data@2.0.1:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ icu_properties_data v2.0.1
    └── icu_properties v2.0.1
        └── idna_adapter v1.2.1
            └── idna v1.0.3
                └── url v2.5.4
                    ├── redis v0.29.5
                    │   └── deadpool-redis v0.20.0
                    │       └── rustycluster v2.2.7
                    └── redis v0.31.0
                        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#icu_provider@2.0.0:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ icu_provider v2.0.0
    ├── icu_normalizer v2.0.0
    │   └── idna_adapter v1.2.1
    │       └── idna v1.0.3
    │           └── url v2.5.4
    │               ├── redis v0.29.5
    │               │   └── deadpool-redis v0.20.0
    │               │       └── rustycluster v2.2.7
    │               └── redis v0.31.0
    │                   └── rustycluster v2.2.7 (*)
    └── icu_properties v2.0.1
        └── idna_adapter v1.2.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#idna@1.0.3:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ idna v1.0.3
    └── url v2.5.4
        ├── redis v0.29.5
        │   └── deadpool-redis v0.20.0
        │       └── rustycluster v2.2.7
        └── redis v0.31.0
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#idna_adapter@1.2.1:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ idna_adapter v1.2.1
    └── idna v1.0.3
        └── url v2.5.4
            ├── redis v0.29.5
            │   └── deadpool-redis v0.20.0
            │       └── rustycluster v2.2.7
            └── redis v0.31.0
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#indexmap@2.9.0:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ indexmap v2.9.0
    ├── h2 v0.4.10
    │   ├── hyper v1.6.0
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1
    │   │   │       └── rustycluster v2.2.7
    │   │   ├── hyper-util v0.1.14
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tonic v0.13.1 (*)
    ├── petgraph v0.7.1
    │   └── prost-build v0.13.5
    │       └── tonic-build v0.13.1
    │           └── (build) rustycluster v2.2.7 (*)
    └── tower v0.5.2
        ├── axum v0.8.4
        │   └── tonic v0.13.1 (*)
        └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#inout@0.1.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ inout v0.1.4
    └── cipher v0.4.4
        ├── aes v0.8.4
        │   └── aes-gcm v0.10.3
        │       └── rustycluster v2.2.7
        ├── aes-gcm v0.10.3 (*)
        └── ctr v0.9.2
            └── aes-gcm v0.10.3 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#itertools@0.14.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ itertools v0.14.0
    ├── prost-build v0.13.5
    │   └── tonic-build v0.13.1
    │       └── (build) rustycluster v2.2.7
    └── prost-derive v0.13.5
        └── prost v0.13.5
            ├── prost-build v0.13.5 (*)
            ├── prost-types v0.13.5
            │   ├── prost-build v0.13.5 (*)
            │   ├── rustycluster v2.2.7 (*)
            │   └── tonic-build v0.13.1 (*)
            ├── rustycluster v2.2.7 (*)
            └── tonic v0.13.1
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#itoa@1.0.15:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ itoa v1.0.15
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── http v1.3.1
    │   ├── axum v0.8.4 (*)
    │   ├── axum-core v0.5.2
    │   │   └── axum v0.8.4 (*)
    │   ├── h2 v0.4.10
    │   │   ├── hyper v1.6.0
    │   │   │   ├── hyper-timeout v0.5.2
    │   │   │   │   └── tonic v0.13.1 (*)
    │   │   │   ├── hyper-util v0.1.14
    │   │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   │   └── tonic v0.13.1 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── http-body v1.0.1
    │   │   ├── axum v0.8.4 (*)
    │   │   ├── axum-core v0.5.2 (*)
    │   │   ├── http-body-util v0.1.3
    │   │   │   ├── axum v0.8.4 (*)
    │   │   │   ├── axum-core v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper v1.6.0 (*)
    │   │   ├── hyper-util v0.1.14 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── http-body-util v0.1.3 (*)
    │   ├── hyper v1.6.0 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   └── tonic v0.13.1 (*)
    ├── hyper v1.6.0 (*)
    ├── redis v0.29.5
    │   └── deadpool-redis v0.20.0
    │       └── rustycluster v2.2.7 (*)
    ├── redis v0.31.0
    │   └── rustycluster v2.2.7 (*)
    ├── serde_json v1.0.140
    │   ├── config v0.13.4
    │   │   └── rustycluster v2.2.7 (*)
    │   └── tracing-subscriber v0.3.19
    │       ├── rustycluster v2.2.7 (*)
    │       └── tracing-appender v0.2.3
    │           └── rustycluster v2.2.7 (*)
    └── time v0.3.41
        └── tracing-appender v0.2.3 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#js-sys@0.3.77:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ js-sys v0.3.77
    └── iana-time-zone v0.1.63
        └── chrono v0.4.41
            └── tracing-subscriber v0.3.19
                ├── rustycluster v2.2.7
                └── tracing-appender v0.2.3
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#json5@0.4.1:4:12
  │
4 │ license = "ISC"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ ISC - ISC License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ json5 v0.4.1
    └── config v0.13.4
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#lazy_static@1.5.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ lazy_static v1.5.0
    ├── config v0.13.4
    │   └── rustycluster v2.2.7
    ├── prometheus v0.14.0
    │   └── rustycluster v2.2.7 (*)
    ├── rustycluster v2.2.7 (*)
    └── sharded-slab v0.1.7
        └── tracing-subscriber v0.3.19
            ├── rustycluster v2.2.7 (*)
            └── tracing-appender v0.2.3
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#libc@0.2.174:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ libc v0.2.174
    ├── android_system_properties v0.1.5
    │   └── iana-time-zone v0.1.63
    │       └── chrono v0.4.41
    │           └── tracing-subscriber v0.3.19
    │               ├── rustycluster v2.2.7
    │               └── tracing-appender v0.2.3
    │                   └── rustycluster v2.2.7 (*)
    ├── backtrace v0.3.75
    │   └── tokio v1.45.1
    │       ├── combine v4.6.7
    │       │   ├── redis v0.29.5
    │       │   │   └── deadpool-redis v0.20.0
    │       │   │       └── rustycluster v2.2.7 (*)
    │       │   └── redis v0.31.0
    │       │       └── rustycluster v2.2.7 (*)
    │       ├── deadpool v0.12.2
    │       │   └── deadpool-redis v0.20.0 (*)
    │       ├── deadpool-runtime v0.1.4
    │       │   └── deadpool v0.12.2 (*)
    │       ├── h2 v0.4.10
    │       │   ├── hyper v1.6.0
    │       │   │   ├── hyper-timeout v0.5.2
    │       │   │   │   └── tonic v0.13.1
    │       │   │   │       └── rustycluster v2.2.7 (*)
    │       │   │   ├── hyper-util v0.1.14
    │       │   │   │   ├── hyper-timeout v0.5.2 (*)
    │       │   │   │   └── tonic v0.13.1 (*)
    │       │   │   └── tonic v0.13.1 (*)
    │       │   └── tonic v0.13.1 (*)
    │       ├── hyper v1.6.0 (*)
    │       ├── hyper-timeout v0.5.2 (*)
    │       ├── hyper-util v0.1.14 (*)
    │       ├── redis v0.29.5 (*)
    │       ├── redis v0.31.0 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       ├── tokio-stream v0.1.17
    │       │   └── tonic v0.13.1 (*)
    │       ├── tokio-util v0.7.15
    │       │   ├── combine v4.6.7 (*)
    │       │   ├── h2 v0.4.10 (*)
    │       │   ├── redis v0.29.5 (*)
    │       │   ├── redis v0.31.0 (*)
    │       │   └── tower v0.5.2
    │       │       ├── axum v0.8.4
    │       │       │   └── tonic v0.13.1 (*)
    │       │       └── tonic v0.13.1 (*)
    │       ├── tonic v0.13.1 (*)
    │       └── tower v0.5.2 (*)
    ├── cpufeatures v0.2.17
    │   ├── aes v0.8.4
    │   │   └── aes-gcm v0.10.3
    │   │       └── rustycluster v2.2.7 (*)
    │   ├── polyval v0.6.2
    │   │   └── ghash v0.5.1
    │   │       └── aes-gcm v0.10.3 (*)
    │   └── sha2 v0.10.9
    │       ├── (build) pest_meta v2.8.1
    │       │   └── pest_generator v2.8.1
    │       │       └── pest_derive v2.8.1
    │       │           └── json5 v0.4.1
    │       │               └── config v0.13.4
    │       │                   └── rustycluster v2.2.7 (*)
    │       └── rustycluster v2.2.7 (*)
    ├── errno v0.3.13
    │   └── rustix v1.0.7
    │       └── tempfile v3.20.0
    │           └── prost-build v0.13.5
    │               └── tonic-build v0.13.1
    │                   └── (build) rustycluster v2.2.7 (*)
    ├── getrandom v0.2.16
    │   └── rand_core v0.6.4
    │       ├── crypto-common v0.1.6
    │       │   ├── aead v0.5.2
    │       │   │   └── aes-gcm v0.10.3 (*)
    │       │   ├── cipher v0.4.4
    │       │   │   ├── aes v0.8.4 (*)
    │       │   │   ├── aes-gcm v0.10.3 (*)
    │       │   │   └── ctr v0.9.2
    │       │   │       └── aes-gcm v0.10.3 (*)
    │       │   ├── digest v0.10.7
    │       │   │   ├── hmac v0.12.1
    │       │   │   │   └── pbkdf2 v0.12.2
    │       │   │   │       └── rustycluster v2.2.7 (*)
    │       │   │   ├── pbkdf2 v0.12.2 (*)
    │       │   │   └── sha2 v0.10.9 (*)
    │       │   └── universal-hash v0.5.1
    │       │       └── polyval v0.6.2 (*)
    │       ├── rand v0.8.5
    │       │   └── rustycluster v2.2.7 (*)
    │       └── rand_chacha v0.3.1
    │           └── rand v0.8.5 (*)
    ├── getrandom v0.3.3
    │   ├── tempfile v3.20.0 (*)
    │   └── uuid v1.17.0
    │       └── rustycluster v2.2.7 (*)
    ├── hyper-util v0.1.14 (*)
    ├── mio v1.0.4
    │   └── tokio v1.45.1 (*)
    ├── num_cpus v1.17.0
    │   ├── deadpool v0.12.2 (*)
    │   └── rustycluster v2.2.7 (*)
    ├── parking_lot_core v0.9.11
    │   ├── dashmap v6.1.0
    │   │   └── rustycluster v2.2.7 (*)
    │   └── parking_lot v0.12.4
    │       ├── prometheus v0.14.0
    │       │   └── rustycluster v2.2.7 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       └── tokio v1.45.1 (*)
    ├── rand v0.8.5 (*)
    ├── rustix v1.0.7 (*)
    ├── signal-hook-registry v1.4.5
    │   └── tokio v1.45.1 (*)
    ├── socket2 v0.5.10
    │   ├── hyper-util v0.1.14 (*)
    │   ├── redis v0.29.5 (*)
    │   ├── redis v0.31.0 (*)
    │   ├── tokio v1.45.1 (*)
    │   └── tonic v0.13.1 (*)
    └── tokio v1.45.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#linked-hash-map@0.5.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ linked-hash-map v0.5.6
    └── yaml-rust v0.4.5
        └── config v0.13.4
            └── rustycluster v2.2.7

warning[duplicate]: found 3 duplicate entries for crate 'base64'
   ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:16:1
   │  
16 │ ╭ base64 0.13.1 registry+https://github.com/rust-lang/crates.io-index
17 │ │ base64 0.21.7 registry+https://github.com/rust-lang/crates.io-index
18 │ │ base64 0.22.1 registry+https://github.com/rust-lang/crates.io-index
   │ ╰───────────────────────────────────────────────────────────────────┘ lock entries
   │  
   ├ base64 v0.13.1
     └── ron v0.7.1
         └── config v0.13.4
             └── rustycluster v2.2.7
   ├ base64 v0.21.7
     └── rustycluster v2.2.7
   ├ base64 v0.22.1
     └── tonic v0.13.1
         └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#linux-raw-sys@0.9.4:4:12
  │
4 │ license = "Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────────────────────────━━━━━━━━━━────━━━
  │            │                                 │             │
  │            │                                 │             rejected: license is not explicitly allowed
  │            │                                 rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ linux-raw-sys v0.9.4
    └── rustix v1.0.7
        └── tempfile v3.20.0
            └── prost-build v0.13.5
                └── tonic-build v0.13.1
                    └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#litemap@0.8.0:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ litemap v0.8.0
    └── icu_locale_core v2.0.0
        ├── icu_properties v2.0.1
        │   └── idna_adapter v1.2.1
        │       └── idna v1.0.3
        │           └── url v2.5.4
        │               ├── redis v0.29.5
        │               │   └── deadpool-redis v0.20.0
        │               │       └── rustycluster v2.2.7
        │               └── redis v0.31.0
        │                   └── rustycluster v2.2.7 (*)
        └── icu_provider v2.0.0
            ├── icu_normalizer v2.0.0
            │   └── idna_adapter v1.2.1 (*)
            └── icu_properties v2.0.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#lock_api@0.4.13:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ lock_api v0.4.13
    ├── dashmap v6.1.0
    │   └── rustycluster v2.2.7
    └── parking_lot v0.12.4
        ├── prometheus v0.14.0
        │   └── rustycluster v2.2.7 (*)
        ├── rustycluster v2.2.7 (*)
        └── tokio v1.45.1
            ├── combine v4.6.7
            │   ├── redis v0.29.5
            │   │   └── deadpool-redis v0.20.0
            │   │       └── rustycluster v2.2.7 (*)
            │   └── redis v0.31.0
            │       └── rustycluster v2.2.7 (*)
            ├── deadpool v0.12.2
            │   └── deadpool-redis v0.20.0 (*)
            ├── deadpool-runtime v0.1.4
            │   └── deadpool v0.12.2 (*)
            ├── h2 v0.4.10
            │   ├── hyper v1.6.0
            │   │   ├── hyper-timeout v0.5.2
            │   │   │   └── tonic v0.13.1
            │   │   │       └── rustycluster v2.2.7 (*)
            │   │   ├── hyper-util v0.1.14
            │   │   │   ├── hyper-timeout v0.5.2 (*)
            │   │   │   └── tonic v0.13.1 (*)
            │   │   └── tonic v0.13.1 (*)
            │   └── tonic v0.13.1 (*)
            ├── hyper v1.6.0 (*)
            ├── hyper-timeout v0.5.2 (*)
            ├── hyper-util v0.1.14 (*)
            ├── redis v0.29.5 (*)
            ├── redis v0.31.0 (*)
            ├── rustycluster v2.2.7 (*)
            ├── tokio-stream v0.1.17
            │   └── tonic v0.13.1 (*)
            ├── tokio-util v0.7.15
            │   ├── combine v4.6.7 (*)
            │   ├── h2 v0.4.10 (*)
            │   ├── redis v0.29.5 (*)
            │   ├── redis v0.31.0 (*)
            │   └── tower v0.5.2
            │       ├── axum v0.8.4
            │       │   └── tonic v0.13.1 (*)
            │       └── tonic v0.13.1 (*)
            ├── tonic v0.13.1 (*)
            └── tower v0.5.2 (*)

warning[duplicate]: found 2 duplicate entries for crate 'bitflags'
   ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:19:1
   │  
19 │ ╭ bitflags 1.3.2 registry+https://github.com/rust-lang/crates.io-index
20 │ │ bitflags 2.9.1 registry+https://github.com/rust-lang/crates.io-index
   │ ╰────────────────────────────────────────────────────────────────────┘ lock entries
   │  
   ├ bitflags v1.3.2
     └── ron v0.7.1
         └── config v0.13.4
             └── rustycluster v2.2.7
   ├ bitflags v2.9.1
     ├── redox_syscall v0.5.13
     │   └── parking_lot_core v0.9.11
     │       ├── dashmap v6.1.0
     │       │   └── rustycluster v2.2.7
     │       └── parking_lot v0.12.4
     │           ├── prometheus v0.14.0
     │           │   └── rustycluster v2.2.7 (*)
     │           ├── rustycluster v2.2.7 (*)
     │           └── tokio v1.45.1
     │               ├── combine v4.6.7
     │               │   ├── redis v0.29.5
     │               │   │   └── deadpool-redis v0.20.0
     │               │   │       └── rustycluster v2.2.7 (*)
     │               │   └── redis v0.31.0
     │               │       └── rustycluster v2.2.7 (*)
     │               ├── deadpool v0.12.2
     │               │   └── deadpool-redis v0.20.0 (*)
     │               ├── deadpool-runtime v0.1.4
     │               │   └── deadpool v0.12.2 (*)
     │               ├── h2 v0.4.10
     │               │   ├── hyper v1.6.0
     │               │   │   ├── hyper-timeout v0.5.2
     │               │   │   │   └── tonic v0.13.1
     │               │   │   │       └── rustycluster v2.2.7 (*)
     │               │   │   ├── hyper-util v0.1.14
     │               │   │   │   ├── hyper-timeout v0.5.2 (*)
     │               │   │   │   └── tonic v0.13.1 (*)
     │               │   │   └── tonic v0.13.1 (*)
     │               │   └── tonic v0.13.1 (*)
     │               ├── hyper v1.6.0 (*)
     │               ├── hyper-timeout v0.5.2 (*)
     │               ├── hyper-util v0.1.14 (*)
     │               ├── redis v0.29.5 (*)
     │               ├── redis v0.31.0 (*)
     │               ├── rustycluster v2.2.7 (*)
     │               ├── tokio-stream v0.1.17
     │               │   └── tonic v0.13.1 (*)
     │               ├── tokio-util v0.7.15
     │               │   ├── combine v4.6.7 (*)
     │               │   ├── h2 v0.4.10 (*)
     │               │   ├── redis v0.29.5 (*)
     │               │   ├── redis v0.31.0 (*)
     │               │   └── tower v0.5.2
     │               │       ├── axum v0.8.4
     │               │       │   └── tonic v0.13.1 (*)
     │               │       └── tonic v0.13.1 (*)
     │               ├── tonic v0.13.1 (*)
     │               └── tower v0.5.2 (*)
     ├── rustix v1.0.7
     │   └── tempfile v3.20.0
     │       └── prost-build v0.13.5
     │           └── tonic-build v0.13.1
     │               └── (build) rustycluster v2.2.7 (*)
     └── wit-bindgen-rt v0.39.0
         └── wasi v0.14.2+wasi-0.2.4
             └── getrandom v0.3.3
                 ├── tempfile v3.20.0 (*)
                 └── uuid v1.17.0
                     └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#log@0.4.27:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ log v0.4.27
    ├── iana-time-zone v0.1.63
    │   └── chrono v0.4.41
    │       └── tracing-subscriber v0.3.19
    │           ├── rustycluster v2.2.7
    │           └── tracing-appender v0.2.3
    │               └── rustycluster v2.2.7 (*)
    ├── prost-build v0.13.5
    │   └── tonic-build v0.13.1
    │       └── (build) rustycluster v2.2.7 (*)
    ├── rustycluster v2.2.7 (*)
    ├── tracing-log v0.1.4
    │   └── rustycluster v2.2.7 (*)
    ├── tracing-log v0.2.0
    │   └── tracing-subscriber v0.3.19 (*)
    └── wasm-bindgen-backend v0.2.100
        └── wasm-bindgen-macro-support v0.2.100
            └── wasm-bindgen-macro v0.2.100
                └── wasm-bindgen v0.2.100
                    ├── iana-time-zone v0.1.63 (*)
                    └── js-sys v0.3.77
                        └── iana-time-zone v0.1.63 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#matchers@0.1.0:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ matchers v0.1.0
    └── tracing-subscriber v0.3.19
        ├── rustycluster v2.2.7
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#matchit@0.8.4:4:12
  │
4 │ license = "MIT AND BSD-3-Clause"
  │            ━━━─────━━━━━━━━━━━━
  │            │       │
  │            │       rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ BSD-3-Clause - BSD 3-Clause "New" or "Revised" License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ matchit v0.8.4
    └── axum v0.8.4
        └── tonic v0.13.1
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#memchr@2.7.5:4:12
  │
4 │ license = "Unlicense OR MIT"
  │            ━━━━━━━━━────━━━
  │            │            │
  │            │            rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unlicense - The Unlicense:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ memchr v2.7.5
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── combine v4.6.7
    │   ├── redis v0.29.5
    │   │   └── deadpool-redis v0.20.0
    │   │       └── rustycluster v2.2.7 (*)
    │   └── redis v0.31.0
    │       └── rustycluster v2.2.7 (*)
    ├── futures-util v0.3.31
    │   ├── axum v0.8.4 (*)
    │   ├── futures v0.3.31
    │   │   └── rustycluster v2.2.7 (*)
    │   ├── futures-executor v0.3.31
    │   │   └── futures v0.3.31 (*)
    │   ├── hyper v1.6.0
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper-util v0.1.14
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   ├── redis v0.29.5 (*)
    │   ├── redis v0.31.0 (*)
    │   └── tower v0.5.2
    │       ├── axum v0.8.4 (*)
    │       └── tonic v0.13.1 (*)
    ├── nom v7.1.3
    │   └── config v0.13.4
    │       └── rustycluster v2.2.7 (*)
    ├── object v0.36.7
    │   └── backtrace v0.3.75
    │       └── tokio v1.45.1
    │           ├── combine v4.6.7 (*)
    │           ├── deadpool v0.12.2
    │           │   └── deadpool-redis v0.20.0 (*)
    │           ├── deadpool-runtime v0.1.4
    │           │   └── deadpool v0.12.2 (*)
    │           ├── h2 v0.4.10
    │           │   ├── hyper v1.6.0 (*)
    │           │   └── tonic v0.13.1 (*)
    │           ├── hyper v1.6.0 (*)
    │           ├── hyper-timeout v0.5.2 (*)
    │           ├── hyper-util v0.1.14 (*)
    │           ├── redis v0.29.5 (*)
    │           ├── redis v0.31.0 (*)
    │           ├── rustycluster v2.2.7 (*)
    │           ├── tokio-stream v0.1.17
    │           │   └── tonic v0.13.1 (*)
    │           ├── tokio-util v0.7.15
    │           │   ├── combine v4.6.7 (*)
    │           │   ├── h2 v0.4.10 (*)
    │           │   ├── redis v0.29.5 (*)
    │           │   ├── redis v0.31.0 (*)
    │           │   └── tower v0.5.2 (*)
    │           ├── tonic v0.13.1 (*)
    │           └── tower v0.5.2 (*)
    ├── pest v2.8.1
    │   ├── json5 v0.4.1
    │   │   └── config v0.13.4 (*)
    │   ├── pest_derive v2.8.1
    │   │   └── json5 v0.4.1 (*)
    │   ├── pest_generator v2.8.1
    │   │   └── pest_derive v2.8.1 (*)
    │   └── pest_meta v2.8.1
    │       └── pest_generator v2.8.1 (*)
    ├── prometheus v0.14.0
    │   └── rustycluster v2.2.7 (*)
    └── serde_json v1.0.140
        ├── config v0.13.4 (*)
        └── tracing-subscriber v0.3.19
            ├── rustycluster v2.2.7 (*)
            └── tracing-appender v0.2.3
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#mime@0.3.17:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ mime v0.3.17
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    └── axum-core v0.5.2
        └── axum v0.8.4 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#minimal-lexical@0.2.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ minimal-lexical v0.2.1
    └── nom v7.1.3
        └── config v0.13.4
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#miniz_oxide@0.8.9:4:12
  │
4 │ license = "MIT OR Zlib OR Apache-2.0"
  │            ━━━────━━━━────━━━━━━━━━━
  │            │      │       │
  │            │      │       rejected: license is not explicitly allowed
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Zlib - zlib License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ miniz_oxide v0.8.9
    └── backtrace v0.3.75
        └── tokio v1.45.1
            ├── combine v4.6.7
            │   ├── redis v0.29.5
            │   │   └── deadpool-redis v0.20.0
            │   │       └── rustycluster v2.2.7
            │   └── redis v0.31.0
            │       └── rustycluster v2.2.7 (*)
            ├── deadpool v0.12.2
            │   └── deadpool-redis v0.20.0 (*)
            ├── deadpool-runtime v0.1.4
            │   └── deadpool v0.12.2 (*)
            ├── h2 v0.4.10
            │   ├── hyper v1.6.0
            │   │   ├── hyper-timeout v0.5.2
            │   │   │   └── tonic v0.13.1
            │   │   │       └── rustycluster v2.2.7 (*)
            │   │   ├── hyper-util v0.1.14
            │   │   │   ├── hyper-timeout v0.5.2 (*)
            │   │   │   └── tonic v0.13.1 (*)
            │   │   └── tonic v0.13.1 (*)
            │   └── tonic v0.13.1 (*)
            ├── hyper v1.6.0 (*)
            ├── hyper-timeout v0.5.2 (*)
            ├── hyper-util v0.1.14 (*)
            ├── redis v0.29.5 (*)
            ├── redis v0.31.0 (*)
            ├── rustycluster v2.2.7 (*)
            ├── tokio-stream v0.1.17
            │   └── tonic v0.13.1 (*)
            ├── tokio-util v0.7.15
            │   ├── combine v4.6.7 (*)
            │   ├── h2 v0.4.10 (*)
            │   ├── redis v0.29.5 (*)
            │   ├── redis v0.31.0 (*)
            │   └── tower v0.5.2
            │       ├── axum v0.8.4
            │       │   └── tonic v0.13.1 (*)
            │       └── tonic v0.13.1 (*)
            ├── tonic v0.13.1 (*)
            └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#mio@1.0.4:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ mio v1.0.4
    └── tokio v1.45.1
        ├── combine v4.6.7
        │   ├── redis v0.29.5
        │   │   └── deadpool-redis v0.20.0
        │   │       └── rustycluster v2.2.7
        │   └── redis v0.31.0
        │       └── rustycluster v2.2.7 (*)
        ├── deadpool v0.12.2
        │   └── deadpool-redis v0.20.0 (*)
        ├── deadpool-runtime v0.1.4
        │   └── deadpool v0.12.2 (*)
        ├── h2 v0.4.10
        │   ├── hyper v1.6.0
        │   │   ├── hyper-timeout v0.5.2
        │   │   │   └── tonic v0.13.1
        │   │   │       └── rustycluster v2.2.7 (*)
        │   │   ├── hyper-util v0.1.14
        │   │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   │   └── tonic v0.13.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── hyper v1.6.0 (*)
        ├── hyper-timeout v0.5.2 (*)
        ├── hyper-util v0.1.14 (*)
        ├── redis v0.29.5 (*)
        ├── redis v0.31.0 (*)
        ├── rustycluster v2.2.7 (*)
        ├── tokio-stream v0.1.17
        │   └── tonic v0.13.1 (*)
        ├── tokio-util v0.7.15
        │   ├── combine v4.6.7 (*)
        │   ├── h2 v0.4.10 (*)
        │   ├── redis v0.29.5 (*)
        │   ├── redis v0.31.0 (*)
        │   └── tower v0.5.2
        │       ├── axum v0.8.4
        │       │   └── tonic v0.13.1 (*)
        │       └── tonic v0.13.1 (*)
        ├── tonic v0.13.1 (*)
        └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#multimap@0.10.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ multimap v0.10.1
    └── prost-build v0.13.5
        └── tonic-build v0.13.1
            └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#nom@7.1.3:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ nom v7.1.3
    └── config v0.13.4
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#nu-ansi-term@0.46.0:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ nu-ansi-term v0.46.0
    └── tracing-subscriber v0.3.19
        ├── rustycluster v2.2.7
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#num-bigint@0.4.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ num-bigint v0.4.6
    ├── redis v0.29.5
    │   └── deadpool-redis v0.20.0
    │       └── rustycluster v2.2.7
    └── redis v0.31.0
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#num-conv@0.1.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ num-conv v0.1.0
    └── time v0.3.41
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#num-integer@0.1.46:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ num-integer v0.1.46
    └── num-bigint v0.4.6
        ├── redis v0.29.5
        │   └── deadpool-redis v0.20.0
        │       └── rustycluster v2.2.7
        └── redis v0.31.0
            └── rustycluster v2.2.7 (*)

warning[duplicate]: found 2 duplicate entries for crate 'getrandom'
   ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:60:1
   │  
60 │ ╭ getrandom 0.2.16 registry+https://github.com/rust-lang/crates.io-index
61 │ │ getrandom 0.3.3 registry+https://github.com/rust-lang/crates.io-index
   │ ╰─────────────────────────────────────────────────────────────────────┘ lock entries
   │  
   ├ getrandom v0.2.16
     └── rand_core v0.6.4
         ├── crypto-common v0.1.6
         │   ├── aead v0.5.2
         │   │   └── aes-gcm v0.10.3
         │   │       └── rustycluster v2.2.7
         │   ├── cipher v0.4.4
         │   │   ├── aes v0.8.4
         │   │   │   └── aes-gcm v0.10.3 (*)
         │   │   ├── aes-gcm v0.10.3 (*)
         │   │   └── ctr v0.9.2
         │   │       └── aes-gcm v0.10.3 (*)
         │   ├── digest v0.10.7
         │   │   ├── hmac v0.12.1
         │   │   │   └── pbkdf2 v0.12.2
         │   │   │       └── rustycluster v2.2.7 (*)
         │   │   ├── pbkdf2 v0.12.2 (*)
         │   │   └── sha2 v0.10.9
         │   │       ├── (build) pest_meta v2.8.1
         │   │       │   └── pest_generator v2.8.1
         │   │       │       └── pest_derive v2.8.1
         │   │       │           └── json5 v0.4.1
         │   │       │               └── config v0.13.4
         │   │       │                   └── rustycluster v2.2.7 (*)
         │   │       └── rustycluster v2.2.7 (*)
         │   └── universal-hash v0.5.1
         │       └── polyval v0.6.2
         │           └── ghash v0.5.1
         │               └── aes-gcm v0.10.3 (*)
         ├── rand v0.8.5
         │   └── rustycluster v2.2.7 (*)
         └── rand_chacha v0.3.1
             └── rand v0.8.5 (*)
   ├ getrandom v0.3.3
     ├── tempfile v3.20.0
     │   └── prost-build v0.13.5
     │       └── tonic-build v0.13.1
     │           └── (build) rustycluster v2.2.7
     └── uuid v1.17.0
         └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#num-traits@0.2.19:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ num-traits v0.2.19
    ├── chrono v0.4.41
    │   └── tracing-subscriber v0.3.19
    │       ├── rustycluster v2.2.7
    │       └── tracing-appender v0.2.3
    │           └── rustycluster v2.2.7 (*)
    ├── num-bigint v0.4.6
    │   ├── redis v0.29.5
    │   │   └── deadpool-redis v0.20.0
    │   │       └── rustycluster v2.2.7 (*)
    │   └── redis v0.31.0
    │       └── rustycluster v2.2.7 (*)
    └── num-integer v0.1.46
        └── num-bigint v0.4.6 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#num_cpus@1.17.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ num_cpus v1.17.0
    ├── deadpool v0.12.2
    │   └── deadpool-redis v0.20.0
    │       └── rustycluster v2.2.7
    └── rustycluster v2.2.7 (*)

warning[duplicate]: found 2 duplicate entries for crate 'hashbrown'
   ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:65:1
   │  
65 │ ╭ hashbrown 0.14.5 registry+https://github.com/rust-lang/crates.io-index
66 │ │ hashbrown 0.15.4 registry+https://github.com/rust-lang/crates.io-index
   │ ╰──────────────────────────────────────────────────────────────────────┘ lock entries
   │  
   ├ hashbrown v0.14.5
     └── dashmap v6.1.0
         └── rustycluster v2.2.7
   ├ hashbrown v0.15.4
     └── indexmap v2.9.0
         ├── h2 v0.4.10
         │   ├── hyper v1.6.0
         │   │   ├── hyper-timeout v0.5.2
         │   │   │   └── tonic v0.13.1
         │   │   │       └── rustycluster v2.2.7
         │   │   ├── hyper-util v0.1.14
         │   │   │   ├── hyper-timeout v0.5.2 (*)
         │   │   │   └── tonic v0.13.1 (*)
         │   │   └── tonic v0.13.1 (*)
         │   └── tonic v0.13.1 (*)
         ├── petgraph v0.7.1
         │   └── prost-build v0.13.5
         │       └── tonic-build v0.13.1
         │           └── (build) rustycluster v2.2.7 (*)
         └── tower v0.5.2
             ├── axum v0.8.4
             │   └── tonic v0.13.1 (*)
             └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#object@0.36.7:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ object v0.36.7
    └── backtrace v0.3.75
        └── tokio v1.45.1
            ├── combine v4.6.7
            │   ├── redis v0.29.5
            │   │   └── deadpool-redis v0.20.0
            │   │       └── rustycluster v2.2.7
            │   └── redis v0.31.0
            │       └── rustycluster v2.2.7 (*)
            ├── deadpool v0.12.2
            │   └── deadpool-redis v0.20.0 (*)
            ├── deadpool-runtime v0.1.4
            │   └── deadpool v0.12.2 (*)
            ├── h2 v0.4.10
            │   ├── hyper v1.6.0
            │   │   ├── hyper-timeout v0.5.2
            │   │   │   └── tonic v0.13.1
            │   │   │       └── rustycluster v2.2.7 (*)
            │   │   ├── hyper-util v0.1.14
            │   │   │   ├── hyper-timeout v0.5.2 (*)
            │   │   │   └── tonic v0.13.1 (*)
            │   │   └── tonic v0.13.1 (*)
            │   └── tonic v0.13.1 (*)
            ├── hyper v1.6.0 (*)
            ├── hyper-timeout v0.5.2 (*)
            ├── hyper-util v0.1.14 (*)
            ├── redis v0.29.5 (*)
            ├── redis v0.31.0 (*)
            ├── rustycluster v2.2.7 (*)
            ├── tokio-stream v0.1.17
            │   └── tonic v0.13.1 (*)
            ├── tokio-util v0.7.15
            │   ├── combine v4.6.7 (*)
            │   ├── h2 v0.4.10 (*)
            │   ├── redis v0.29.5 (*)
            │   ├── redis v0.31.0 (*)
            │   └── tower v0.5.2
            │       ├── axum v0.8.4
            │       │   └── tonic v0.13.1 (*)
            │       └── tonic v0.13.1 (*)
            ├── tonic v0.13.1 (*)
            └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#once_cell@1.21.3:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ once_cell v1.21.3
    ├── dashmap v6.1.0
    │   └── rustycluster v2.2.7
    ├── js-sys v0.3.77
    │   └── iana-time-zone v0.1.63
    │       └── chrono v0.4.41
    │           └── tracing-subscriber v0.3.19
    │               ├── rustycluster v2.2.7 (*)
    │               └── tracing-appender v0.2.3
    │                   └── rustycluster v2.2.7 (*)
    ├── prost-build v0.13.5
    │   └── tonic-build v0.13.1
    │       └── (build) rustycluster v2.2.7 (*)
    ├── protobuf v3.7.2
    │   └── prometheus v0.14.0
    │       └── rustycluster v2.2.7 (*)
    ├── tempfile v3.20.0
    │   └── prost-build v0.13.5 (*)
    ├── tracing-core v0.1.34
    │   ├── tracing v0.1.41
    │   │   ├── h2 v0.4.10
    │   │   │   ├── hyper v1.6.0
    │   │   │   │   ├── hyper-timeout v0.5.2
    │   │   │   │   │   └── tonic v0.13.1
    │   │   │   │   │       └── rustycluster v2.2.7 (*)
    │   │   │   │   ├── hyper-util v0.1.14
    │   │   │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   │   │   └── tonic v0.13.1 (*)
    │   │   │   │   └── tonic v0.13.1 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper-util v0.1.14 (*)
    │   │   ├── rustycluster v2.2.7 (*)
    │   │   ├── tokio v1.45.1
    │   │   │   ├── combine v4.6.7
    │   │   │   │   ├── redis v0.29.5
    │   │   │   │   │   └── deadpool-redis v0.20.0
    │   │   │   │   │       └── rustycluster v2.2.7 (*)
    │   │   │   │   └── redis v0.31.0
    │   │   │   │       └── rustycluster v2.2.7 (*)
    │   │   │   ├── deadpool v0.12.2
    │   │   │   │   └── deadpool-redis v0.20.0 (*)
    │   │   │   ├── deadpool-runtime v0.1.4
    │   │   │   │   └── deadpool v0.12.2 (*)
    │   │   │   ├── h2 v0.4.10 (*)
    │   │   │   ├── hyper v1.6.0 (*)
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   ├── hyper-util v0.1.14 (*)
    │   │   │   ├── redis v0.29.5 (*)
    │   │   │   ├── redis v0.31.0 (*)
    │   │   │   ├── rustycluster v2.2.7 (*)
    │   │   │   ├── tokio-stream v0.1.17
    │   │   │   │   └── tonic v0.13.1 (*)
    │   │   │   ├── tokio-util v0.7.15
    │   │   │   │   ├── combine v4.6.7 (*)
    │   │   │   │   ├── h2 v0.4.10 (*)
    │   │   │   │   ├── redis v0.29.5 (*)
    │   │   │   │   ├── redis v0.31.0 (*)
    │   │   │   │   └── tower v0.5.2
    │   │   │   │       ├── axum v0.8.4
    │   │   │   │       │   └── tonic v0.13.1 (*)
    │   │   │   │       └── tonic v0.13.1 (*)
    │   │   │   ├── tonic v0.13.1 (*)
    │   │   │   └── tower v0.5.2 (*)
    │   │   ├── tonic v0.13.1 (*)
    │   │   ├── tower v0.5.2 (*)
    │   │   └── tracing-subscriber v0.3.19 (*)
    │   ├── tracing-log v0.1.4
    │   │   └── rustycluster v2.2.7 (*)
    │   ├── tracing-log v0.2.0
    │   │   └── tracing-subscriber v0.3.19 (*)
    │   ├── tracing-serde v0.2.0
    │   │   └── tracing-subscriber v0.3.19 (*)
    │   └── tracing-subscriber v0.3.19 (*)
    ├── tracing-log v0.1.4 (*)
    ├── tracing-log v0.2.0 (*)
    ├── tracing-subscriber v0.3.19 (*)
    └── wasm-bindgen v0.2.100
        ├── iana-time-zone v0.1.63 (*)
        └── js-sys v0.3.77 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#opaque-debug@0.3.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ opaque-debug v0.3.1
    ├── ghash v0.5.1
    │   └── aes-gcm v0.10.3
    │       └── rustycluster v2.2.7
    └── polyval v0.6.2
        └── ghash v0.5.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#overload@0.1.1:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ overload v0.1.1
    └── nu-ansi-term v0.46.0
        └── tracing-subscriber v0.3.19
            ├── rustycluster v2.2.7
            └── tracing-appender v0.2.3
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#parking_lot@0.12.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ parking_lot v0.12.4
    ├── prometheus v0.14.0
    │   └── rustycluster v2.2.7
    ├── rustycluster v2.2.7 (*)
    └── tokio v1.45.1
        ├── combine v4.6.7
        │   ├── redis v0.29.5
        │   │   └── deadpool-redis v0.20.0
        │   │       └── rustycluster v2.2.7 (*)
        │   └── redis v0.31.0
        │       └── rustycluster v2.2.7 (*)
        ├── deadpool v0.12.2
        │   └── deadpool-redis v0.20.0 (*)
        ├── deadpool-runtime v0.1.4
        │   └── deadpool v0.12.2 (*)
        ├── h2 v0.4.10
        │   ├── hyper v1.6.0
        │   │   ├── hyper-timeout v0.5.2
        │   │   │   └── tonic v0.13.1
        │   │   │       └── rustycluster v2.2.7 (*)
        │   │   ├── hyper-util v0.1.14
        │   │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   │   └── tonic v0.13.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── hyper v1.6.0 (*)
        ├── hyper-timeout v0.5.2 (*)
        ├── hyper-util v0.1.14 (*)
        ├── redis v0.29.5 (*)
        ├── redis v0.31.0 (*)
        ├── rustycluster v2.2.7 (*)
        ├── tokio-stream v0.1.17
        │   └── tonic v0.13.1 (*)
        ├── tokio-util v0.7.15
        │   ├── combine v4.6.7 (*)
        │   ├── h2 v0.4.10 (*)
        │   ├── redis v0.29.5 (*)
        │   ├── redis v0.31.0 (*)
        │   └── tower v0.5.2
        │       ├── axum v0.8.4
        │       │   └── tonic v0.13.1 (*)
        │       └── tonic v0.13.1 (*)
        ├── tonic v0.13.1 (*)
        └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#parking_lot_core@0.9.11:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ parking_lot_core v0.9.11
    ├── dashmap v6.1.0
    │   └── rustycluster v2.2.7
    └── parking_lot v0.12.4
        ├── prometheus v0.14.0
        │   └── rustycluster v2.2.7 (*)
        ├── rustycluster v2.2.7 (*)
        └── tokio v1.45.1
            ├── combine v4.6.7
            │   ├── redis v0.29.5
            │   │   └── deadpool-redis v0.20.0
            │   │       └── rustycluster v2.2.7 (*)
            │   └── redis v0.31.0
            │       └── rustycluster v2.2.7 (*)
            ├── deadpool v0.12.2
            │   └── deadpool-redis v0.20.0 (*)
            ├── deadpool-runtime v0.1.4
            │   └── deadpool v0.12.2 (*)
            ├── h2 v0.4.10
            │   ├── hyper v1.6.0
            │   │   ├── hyper-timeout v0.5.2
            │   │   │   └── tonic v0.13.1
            │   │   │       └── rustycluster v2.2.7 (*)
            │   │   ├── hyper-util v0.1.14
            │   │   │   ├── hyper-timeout v0.5.2 (*)
            │   │   │   └── tonic v0.13.1 (*)
            │   │   └── tonic v0.13.1 (*)
            │   └── tonic v0.13.1 (*)
            ├── hyper v1.6.0 (*)
            ├── hyper-timeout v0.5.2 (*)
            ├── hyper-util v0.1.14 (*)
            ├── redis v0.29.5 (*)
            ├── redis v0.31.0 (*)
            ├── rustycluster v2.2.7 (*)
            ├── tokio-stream v0.1.17
            │   └── tonic v0.13.1 (*)
            ├── tokio-util v0.7.15
            │   ├── combine v4.6.7 (*)
            │   ├── h2 v0.4.10 (*)
            │   ├── redis v0.29.5 (*)
            │   ├── redis v0.31.0 (*)
            │   └── tower v0.5.2
            │       ├── axum v0.8.4
            │       │   └── tonic v0.13.1 (*)
            │       └── tonic v0.13.1 (*)
            ├── tonic v0.13.1 (*)
            └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#pathdiff@0.2.3:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ pathdiff v0.2.3
    └── config v0.13.4
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#pbkdf2@0.12.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ pbkdf2 v0.12.2
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#percent-encoding@2.3.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ percent-encoding v2.3.1
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── form_urlencoded v1.2.1
    │   └── url v2.5.4
    │       ├── redis v0.29.5
    │       │   └── deadpool-redis v0.20.0
    │       │       └── rustycluster v2.2.7 (*)
    │       └── redis v0.31.0
    │           └── rustycluster v2.2.7 (*)
    ├── redis v0.29.5 (*)
    ├── redis v0.31.0 (*)
    ├── tonic v0.13.1 (*)
    └── url v2.5.4 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#pest@2.8.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ pest v2.8.1
    ├── json5 v0.4.1
    │   └── config v0.13.4
    │       └── rustycluster v2.2.7
    ├── pest_derive v2.8.1
    │   └── json5 v0.4.1 (*)
    ├── pest_generator v2.8.1
    │   └── pest_derive v2.8.1 (*)
    └── pest_meta v2.8.1
        └── pest_generator v2.8.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#pest_derive@2.8.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ pest_derive v2.8.1
    └── json5 v0.4.1
        └── config v0.13.4
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#pest_generator@2.8.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ pest_generator v2.8.1
    └── pest_derive v2.8.1
        └── json5 v0.4.1
            └── config v0.13.4
                └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#pest_meta@2.8.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ pest_meta v2.8.1
    └── pest_generator v2.8.1
        └── pest_derive v2.8.1
            └── json5 v0.4.1
                └── config v0.13.4
                    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#petgraph@0.7.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ petgraph v0.7.1
    └── prost-build v0.13.5
        └── tonic-build v0.13.1
            └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#pin-project@1.1.10:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ pin-project v1.1.10
    └── tonic v0.13.1
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#pin-project-internal@1.1.10:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ pin-project-internal v1.1.10
    └── pin-project v1.1.10
        └── tonic v0.13.1
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#pin-project-lite@0.2.16:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ pin-project-lite v0.2.16
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── axum-core v0.5.2
    │   └── axum v0.8.4 (*)
    ├── combine v4.6.7
    │   ├── redis v0.29.5
    │   │   └── deadpool-redis v0.20.0
    │   │       └── rustycluster v2.2.7 (*)
    │   └── redis v0.31.0
    │       └── rustycluster v2.2.7 (*)
    ├── futures-util v0.3.31
    │   ├── axum v0.8.4 (*)
    │   ├── futures v0.3.31
    │   │   └── rustycluster v2.2.7 (*)
    │   ├── futures-executor v0.3.31
    │   │   └── futures v0.3.31 (*)
    │   ├── hyper v1.6.0
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper-util v0.1.14
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   ├── redis v0.29.5 (*)
    │   ├── redis v0.31.0 (*)
    │   └── tower v0.5.2
    │       ├── axum v0.8.4 (*)
    │       └── tonic v0.13.1 (*)
    ├── http-body-util v0.1.3
    │   ├── axum v0.8.4 (*)
    │   ├── axum-core v0.5.2 (*)
    │   └── tonic v0.13.1 (*)
    ├── hyper v1.6.0 (*)
    ├── hyper-timeout v0.5.2 (*)
    ├── hyper-util v0.1.14 (*)
    ├── redis v0.29.5 (*)
    ├── redis v0.31.0 (*)
    ├── tokio v1.45.1
    │   ├── combine v4.6.7 (*)
    │   ├── deadpool v0.12.2
    │   │   └── deadpool-redis v0.20.0 (*)
    │   ├── deadpool-runtime v0.1.4
    │   │   └── deadpool v0.12.2 (*)
    │   ├── h2 v0.4.10
    │   │   ├── hyper v1.6.0 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── hyper v1.6.0 (*)
    │   ├── hyper-timeout v0.5.2 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   ├── redis v0.29.5 (*)
    │   ├── redis v0.31.0 (*)
    │   ├── rustycluster v2.2.7 (*)
    │   ├── tokio-stream v0.1.17
    │   │   └── tonic v0.13.1 (*)
    │   ├── tokio-util v0.7.15
    │   │   ├── combine v4.6.7 (*)
    │   │   ├── h2 v0.4.10 (*)
    │   │   ├── redis v0.29.5 (*)
    │   │   ├── redis v0.31.0 (*)
    │   │   └── tower v0.5.2 (*)
    │   ├── tonic v0.13.1 (*)
    │   └── tower v0.5.2 (*)
    ├── tokio-stream v0.1.17 (*)
    ├── tokio-util v0.7.15 (*)
    ├── tower v0.5.2 (*)
    └── tracing v0.1.41
        ├── h2 v0.4.10 (*)
        ├── hyper-util v0.1.14 (*)
        ├── rustycluster v2.2.7 (*)
        ├── tokio v1.45.1 (*)
        ├── tonic v0.13.1 (*)
        ├── tower v0.5.2 (*)
        └── tracing-subscriber v0.3.19
            ├── rustycluster v2.2.7 (*)
            └── tracing-appender v0.2.3
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#pin-utils@0.1.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ pin-utils v0.1.0
    └── futures-util v0.3.31
        ├── axum v0.8.4
        │   └── tonic v0.13.1
        │       └── rustycluster v2.2.7
        ├── futures v0.3.31
        │   └── rustycluster v2.2.7 (*)
        ├── futures-executor v0.3.31
        │   └── futures v0.3.31 (*)
        ├── hyper v1.6.0
        │   ├── hyper-timeout v0.5.2
        │   │   └── tonic v0.13.1 (*)
        │   ├── hyper-util v0.1.14
        │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── hyper-util v0.1.14 (*)
        ├── redis v0.29.5
        │   └── deadpool-redis v0.20.0
        │       └── rustycluster v2.2.7 (*)
        ├── redis v0.31.0
        │   └── rustycluster v2.2.7 (*)
        └── tower v0.5.2
            ├── axum v0.8.4 (*)
            └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#polyval@0.6.2:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ polyval v0.6.2
    └── ghash v0.5.1
        └── aes-gcm v0.10.3
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#potential_utf@0.1.2:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ potential_utf v0.1.2
    ├── icu_collections v2.0.0
    │   ├── icu_normalizer v2.0.0
    │   │   └── idna_adapter v1.2.1
    │   │       └── idna v1.0.3
    │   │           └── url v2.5.4
    │   │               ├── redis v0.29.5
    │   │               │   └── deadpool-redis v0.20.0
    │   │               │       └── rustycluster v2.2.7
    │   │               └── redis v0.31.0
    │   │                   └── rustycluster v2.2.7 (*)
    │   └── icu_properties v2.0.1
    │       └── idna_adapter v1.2.1 (*)
    └── icu_properties v2.0.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#powerfmt@0.2.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ powerfmt v0.2.0
    ├── deranged v0.4.0
    │   └── time v0.3.41
    │       └── tracing-appender v0.2.3
    │           └── rustycluster v2.2.7
    └── time v0.3.41 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#ppv-lite86@0.2.21:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ ppv-lite86 v0.2.21
    └── rand_chacha v0.3.1
        └── rand v0.8.5
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#prettyplease@0.2.35:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ prettyplease v0.2.35
    ├── prost-build v0.13.5
    │   └── tonic-build v0.13.1
    │       └── (build) rustycluster v2.2.7
    └── tonic-build v0.13.1 (*)

warning[duplicate]: found 2 duplicate entries for crate 'redis'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:153:1
    │  
153 │ ╭ redis 0.29.5 registry+https://github.com/rust-lang/crates.io-index
154 │ │ redis 0.31.0 registry+https://github.com/rust-lang/crates.io-index
    │ ╰──────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ redis v0.29.5
      └── deadpool-redis v0.20.0
          └── rustycluster v2.2.7
    ├ redis v0.31.0
      └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#proc-macro2@1.0.95:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ proc-macro2 v1.0.95
    ├── async-trait v0.1.88
    │   ├── config v0.13.4
    │   │   └── rustycluster v2.2.7
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7 (*)
    ├── displaydoc v0.2.5
    │   ├── icu_collections v2.0.0
    │   │   ├── icu_normalizer v2.0.0
    │   │   │   └── idna_adapter v1.2.1
    │   │   │       └── idna v1.0.3
    │   │   │           └── url v2.5.4
    │   │   │               ├── redis v0.29.5
    │   │   │               │   └── deadpool-redis v0.20.0
    │   │   │               │       └── rustycluster v2.2.7 (*)
    │   │   │               └── redis v0.31.0
    │   │   │                   └── rustycluster v2.2.7 (*)
    │   │   └── icu_properties v2.0.1
    │   │       └── idna_adapter v1.2.1 (*)
    │   ├── icu_locale_core v2.0.0
    │   │   ├── icu_properties v2.0.1 (*)
    │   │   └── icu_provider v2.0.0
    │   │       ├── icu_normalizer v2.0.0 (*)
    │   │       └── icu_properties v2.0.1 (*)
    │   ├── icu_normalizer v2.0.0 (*)
    │   ├── icu_properties v2.0.1 (*)
    │   ├── icu_provider v2.0.0 (*)
    │   ├── tinystr v0.8.1
    │   │   ├── icu_locale_core v2.0.0 (*)
    │   │   └── icu_provider v2.0.0 (*)
    │   └── zerotrie v0.2.2
    │       ├── icu_properties v2.0.1 (*)
    │       └── icu_provider v2.0.0 (*)
    ├── futures-macro v0.3.31
    │   └── futures-util v0.3.31
    │       ├── axum v0.8.4
    │       │   └── tonic v0.13.1 (*)
    │       ├── futures v0.3.31
    │       │   └── rustycluster v2.2.7 (*)
    │       ├── futures-executor v0.3.31
    │       │   └── futures v0.3.31 (*)
    │       ├── hyper v1.6.0
    │       │   ├── hyper-timeout v0.5.2
    │       │   │   └── tonic v0.13.1 (*)
    │       │   ├── hyper-util v0.1.14
    │       │   │   ├── hyper-timeout v0.5.2 (*)
    │       │   │   └── tonic v0.13.1 (*)
    │       │   └── tonic v0.13.1 (*)
    │       ├── hyper-util v0.1.14 (*)
    │       ├── redis v0.29.5 (*)
    │       ├── redis v0.31.0 (*)
    │       └── tower v0.5.2
    │           ├── axum v0.8.4 (*)
    │           └── tonic v0.13.1 (*)
    ├── pest_generator v2.8.1
    │   └── pest_derive v2.8.1
    │       └── json5 v0.4.1
    │           └── config v0.13.4 (*)
    ├── pin-project-internal v1.1.10
    │   └── pin-project v1.1.10
    │       └── tonic v0.13.1 (*)
    ├── prettyplease v0.2.35
    │   ├── prost-build v0.13.5
    │   │   └── tonic-build v0.13.1
    │   │       └── (build) rustycluster v2.2.7 (*)
    │   └── tonic-build v0.13.1 (*)
    ├── prost-derive v0.13.5
    │   └── prost v0.13.5
    │       ├── prost-build v0.13.5 (*)
    │       ├── prost-types v0.13.5
    │       │   ├── prost-build v0.13.5 (*)
    │       │   ├── rustycluster v2.2.7 (*)
    │       │   └── tonic-build v0.13.1 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       └── tonic v0.13.1 (*)
    ├── quote v1.0.40
    │   ├── async-trait v0.1.88 (*)
    │   ├── displaydoc v0.2.5 (*)
    │   ├── futures-macro v0.3.31 (*)
    │   ├── pest_generator v2.8.1 (*)
    │   ├── pin-project-internal v1.1.10 (*)
    │   ├── prost-derive v0.13.5 (*)
    │   ├── serde_derive v1.0.219
    │   │   └── serde v1.0.219
    │   │       ├── axum v0.8.4 (*)
    │   │       ├── config v0.13.4 (*)
    │   │       ├── json5 v0.4.1 (*)
    │   │       ├── ron v0.7.1
    │   │       │   └── config v0.13.4 (*)
    │   │       ├── rustycluster v2.2.7 (*)
    │   │       ├── serde_json v1.0.140
    │   │       │   ├── config v0.13.4 (*)
    │   │       │   └── tracing-subscriber v0.3.19
    │   │       │       ├── rustycluster v2.2.7 (*)
    │   │       │       └── tracing-appender v0.2.3
    │   │       │           └── rustycluster v2.2.7 (*)
    │   │       ├── toml v0.5.11
    │   │       │   └── config v0.13.4 (*)
    │   │       ├── tracing-serde v0.2.0
    │   │       │   └── tracing-subscriber v0.3.19 (*)
    │   │       └── tracing-subscriber v0.3.19 (*)
    │   ├── syn v2.0.104
    │   │   ├── async-trait v0.1.88 (*)
    │   │   ├── displaydoc v0.2.5 (*)
    │   │   ├── futures-macro v0.3.31 (*)
    │   │   ├── pest_generator v2.8.1 (*)
    │   │   ├── pin-project-internal v1.1.10 (*)
    │   │   ├── prettyplease v0.2.35 (*)
    │   │   ├── prost-build v0.13.5 (*)
    │   │   ├── prost-derive v0.13.5 (*)
    │   │   ├── serde_derive v1.0.219 (*)
    │   │   ├── synstructure v0.13.2
    │   │   │   ├── yoke-derive v0.8.0
    │   │   │   │   └── yoke v0.8.0
    │   │   │   │       ├── icu_collections v2.0.0 (*)
    │   │   │   │       ├── icu_provider v2.0.0 (*)
    │   │   │   │       ├── zerotrie v0.2.2 (*)
    │   │   │   │       └── zerovec v0.11.2
    │   │   │   │           ├── icu_collections v2.0.0 (*)
    │   │   │   │           ├── icu_locale_core v2.0.0 (*)
    │   │   │   │           ├── icu_normalizer v2.0.0 (*)
    │   │   │   │           ├── icu_properties v2.0.1 (*)
    │   │   │   │           ├── icu_provider v2.0.0 (*)
    │   │   │   │           ├── potential_utf v0.1.2
    │   │   │   │           │   ├── icu_collections v2.0.0 (*)
    │   │   │   │           │   └── icu_properties v2.0.1 (*)
    │   │   │   │           └── tinystr v0.8.1 (*)
    │   │   │   └── zerofrom-derive v0.1.6
    │   │   │       └── zerofrom v0.1.6
    │   │   │           ├── icu_collections v2.0.0 (*)
    │   │   │           ├── icu_provider v2.0.0 (*)
    │   │   │           ├── yoke v0.8.0 (*)
    │   │   │           ├── zerotrie v0.2.2 (*)
    │   │   │           └── zerovec v0.11.2 (*)
    │   │   ├── thiserror-impl v1.0.69
    │   │   │   └── thiserror v1.0.69
    │   │   │       ├── protobuf v3.7.2
    │   │   │       │   └── prometheus v0.14.0
    │   │   │       │       └── rustycluster v2.2.7 (*)
    │   │   │       ├── protobuf-support v3.7.2
    │   │   │       │   └── protobuf v3.7.2 (*)
    │   │   │       └── tracing-appender v0.2.3 (*)
    │   │   ├── thiserror-impl v2.0.12
    │   │   │   └── thiserror v2.0.12
    │   │   │       ├── pest v2.8.1
    │   │   │       │   ├── json5 v0.4.1 (*)
    │   │   │       │   ├── pest_derive v2.8.1 (*)
    │   │   │       │   ├── pest_generator v2.8.1 (*)
    │   │   │       │   └── pest_meta v2.8.1
    │   │   │       │       └── pest_generator v2.8.1 (*)
    │   │   │       └── prometheus v0.14.0 (*)
    │   │   ├── tokio-macros v2.5.0
    │   │   │   └── tokio v1.45.1
    │   │   │       ├── combine v4.6.7
    │   │   │       │   ├── redis v0.29.5 (*)
    │   │   │       │   └── redis v0.31.0 (*)
    │   │   │       ├── deadpool v0.12.2
    │   │   │       │   └── deadpool-redis v0.20.0 (*)
    │   │   │       ├── deadpool-runtime v0.1.4
    │   │   │       │   └── deadpool v0.12.2 (*)
    │   │   │       ├── h2 v0.4.10
    │   │   │       │   ├── hyper v1.6.0 (*)
    │   │   │       │   └── tonic v0.13.1 (*)
    │   │   │       ├── hyper v1.6.0 (*)
    │   │   │       ├── hyper-timeout v0.5.2 (*)
    │   │   │       ├── hyper-util v0.1.14 (*)
    │   │   │       ├── redis v0.29.5 (*)
    │   │   │       ├── redis v0.31.0 (*)
    │   │   │       ├── rustycluster v2.2.7 (*)
    │   │   │       ├── tokio-stream v0.1.17
    │   │   │       │   └── tonic v0.13.1 (*)
    │   │   │       ├── tokio-util v0.7.15
    │   │   │       │   ├── combine v4.6.7 (*)
    │   │   │       │   ├── h2 v0.4.10 (*)
    │   │   │       │   ├── redis v0.29.5 (*)
    │   │   │       │   ├── redis v0.31.0 (*)
    │   │   │       │   └── tower v0.5.2 (*)
    │   │   │       ├── tonic v0.13.1 (*)
    │   │   │       └── tower v0.5.2 (*)
    │   │   ├── tonic-build v0.13.1 (*)
    │   │   ├── tracing-attributes v0.1.30
    │   │   │   └── tracing v0.1.41
    │   │   │       ├── h2 v0.4.10 (*)
    │   │   │       ├── hyper-util v0.1.14 (*)
    │   │   │       ├── rustycluster v2.2.7 (*)
    │   │   │       ├── tokio v1.45.1 (*)
    │   │   │       ├── tonic v0.13.1 (*)
    │   │   │       ├── tower v0.5.2 (*)
    │   │   │       └── tracing-subscriber v0.3.19 (*)
    │   │   ├── wasm-bindgen-backend v0.2.100
    │   │   │   └── wasm-bindgen-macro-support v0.2.100
    │   │   │       └── wasm-bindgen-macro v0.2.100
    │   │   │           └── wasm-bindgen v0.2.100
    │   │   │               ├── iana-time-zone v0.1.63
    │   │   │               │   └── chrono v0.4.41
    │   │   │               │       └── tracing-subscriber v0.3.19 (*)
    │   │   │               └── js-sys v0.3.77
    │   │   │                   └── iana-time-zone v0.1.63 (*)
    │   │   ├── wasm-bindgen-macro-support v0.2.100 (*)
    │   │   ├── windows-implement v0.60.0
    │   │   │   └── windows-core v0.61.2
    │   │   │       └── iana-time-zone v0.1.63 (*)
    │   │   ├── windows-interface v0.59.1
    │   │   │   └── windows-core v0.61.2 (*)
    │   │   ├── yoke-derive v0.8.0 (*)
    │   │   ├── zerofrom-derive v0.1.6 (*)
    │   │   └── zerovec-derive v0.11.1
    │   │       └── zerovec v0.11.2 (*)
    │   ├── synstructure v0.13.2 (*)
    │   ├── thiserror-impl v1.0.69 (*)
    │   ├── thiserror-impl v2.0.12 (*)
    │   ├── tokio-macros v2.5.0 (*)
    │   ├── tonic-build v0.13.1 (*)
    │   ├── tracing-attributes v0.1.30 (*)
    │   ├── wasm-bindgen-backend v0.2.100 (*)
    │   ├── wasm-bindgen-macro v0.2.100 (*)
    │   ├── wasm-bindgen-macro-support v0.2.100 (*)
    │   ├── windows-implement v0.60.0 (*)
    │   ├── windows-interface v0.59.1 (*)
    │   ├── yoke-derive v0.8.0 (*)
    │   ├── zerofrom-derive v0.1.6 (*)
    │   └── zerovec-derive v0.11.1 (*)
    ├── serde_derive v1.0.219 (*)
    ├── syn v2.0.104 (*)
    ├── synstructure v0.13.2 (*)
    ├── thiserror-impl v1.0.69 (*)
    ├── thiserror-impl v2.0.12 (*)
    ├── tokio-macros v2.5.0 (*)
    ├── tonic-build v0.13.1 (*)
    ├── tracing-attributes v0.1.30 (*)
    ├── wasm-bindgen-backend v0.2.100 (*)
    ├── wasm-bindgen-macro-support v0.2.100 (*)
    ├── windows-implement v0.60.0 (*)
    ├── windows-interface v0.59.1 (*)
    ├── yoke-derive v0.8.0 (*)
    ├── zerofrom-derive v0.1.6 (*)
    └── zerovec-derive v0.11.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#prometheus@0.14.0:4:12
  │
4 │ license = "Apache-2.0"
  │            ━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ prometheus v0.14.0
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#prost@0.13.5:4:12
  │
4 │ license = "Apache-2.0"
  │            ━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ prost v0.13.5
    ├── prost-build v0.13.5
    │   └── tonic-build v0.13.1
    │       └── (build) rustycluster v2.2.7
    ├── prost-types v0.13.5
    │   ├── prost-build v0.13.5 (*)
    │   ├── rustycluster v2.2.7 (*)
    │   └── tonic-build v0.13.1 (*)
    ├── rustycluster v2.2.7 (*)
    └── tonic v0.13.1
        └── rustycluster v2.2.7 (*)

warning[duplicate]: found 2 duplicate entries for crate 'regex-automata'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:157:1
    │  
157 │ ╭ regex-automata 0.1.10 registry+https://github.com/rust-lang/crates.io-index
158 │ │ regex-automata 0.4.9 registry+https://github.com/rust-lang/crates.io-index
    │ ╰──────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ regex-automata v0.1.10
      └── matchers v0.1.0
          └── tracing-subscriber v0.3.19
              ├── rustycluster v2.2.7
              └── tracing-appender v0.2.3
                  └── rustycluster v2.2.7 (*)
    ├ regex-automata v0.4.9
      └── regex v1.11.1
          ├── prost-build v0.13.5
          │   └── tonic-build v0.13.1
          │       └── (build) rustycluster v2.2.7
          └── tracing-subscriber v0.3.19
              ├── rustycluster v2.2.7 (*)
              └── tracing-appender v0.2.3
                  └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#prost-build@0.13.5:4:12
  │
4 │ license = "Apache-2.0"
  │            ━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ prost-build v0.13.5
    └── tonic-build v0.13.1
        └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#prost-derive@0.13.5:4:12
  │
4 │ license = "Apache-2.0"
  │            ━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ prost-derive v0.13.5
    └── prost v0.13.5
        ├── prost-build v0.13.5
        │   └── tonic-build v0.13.1
        │       └── (build) rustycluster v2.2.7
        ├── prost-types v0.13.5
        │   ├── prost-build v0.13.5 (*)
        │   ├── rustycluster v2.2.7 (*)
        │   └── tonic-build v0.13.1 (*)
        ├── rustycluster v2.2.7 (*)
        └── tonic v0.13.1
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#prost-types@0.13.5:4:12
  │
4 │ license = "Apache-2.0"
  │            ━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ prost-types v0.13.5
    ├── prost-build v0.13.5
    │   └── tonic-build v0.13.1
    │       └── (build) rustycluster v2.2.7
    ├── rustycluster v2.2.7 (*)
    └── tonic-build v0.13.1 (*)

warning[duplicate]: found 2 duplicate entries for crate 'regex-syntax'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:159:1
    │  
159 │ ╭ regex-syntax 0.6.29 registry+https://github.com/rust-lang/crates.io-index
160 │ │ regex-syntax 0.8.5 registry+https://github.com/rust-lang/crates.io-index
    │ ╰────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ regex-syntax v0.6.29
      └── regex-automata v0.1.10
          └── matchers v0.1.0
              └── tracing-subscriber v0.3.19
                  ├── rustycluster v2.2.7
                  └── tracing-appender v0.2.3
                      └── rustycluster v2.2.7 (*)
    ├ regex-syntax v0.8.5
      ├── regex v1.11.1
      │   ├── prost-build v0.13.5
      │   │   └── tonic-build v0.13.1
      │   │       └── (build) rustycluster v2.2.7
      │   └── tracing-subscriber v0.3.19
      │       ├── rustycluster v2.2.7 (*)
      │       └── tracing-appender v0.2.3
      │           └── rustycluster v2.2.7 (*)
      └── regex-automata v0.4.9
          └── regex v1.11.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#protobuf@3.7.2:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ protobuf v3.7.2
    └── prometheus v0.14.0
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#protobuf-support@3.7.2:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ protobuf-support v3.7.2
    └── protobuf v3.7.2
        └── prometheus v0.14.0
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#quote@1.0.40:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ quote v1.0.40
    ├── async-trait v0.1.88
    │   ├── config v0.13.4
    │   │   └── rustycluster v2.2.7
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7 (*)
    ├── displaydoc v0.2.5
    │   ├── icu_collections v2.0.0
    │   │   ├── icu_normalizer v2.0.0
    │   │   │   └── idna_adapter v1.2.1
    │   │   │       └── idna v1.0.3
    │   │   │           └── url v2.5.4
    │   │   │               ├── redis v0.29.5
    │   │   │               │   └── deadpool-redis v0.20.0
    │   │   │               │       └── rustycluster v2.2.7 (*)
    │   │   │               └── redis v0.31.0
    │   │   │                   └── rustycluster v2.2.7 (*)
    │   │   └── icu_properties v2.0.1
    │   │       └── idna_adapter v1.2.1 (*)
    │   ├── icu_locale_core v2.0.0
    │   │   ├── icu_properties v2.0.1 (*)
    │   │   └── icu_provider v2.0.0
    │   │       ├── icu_normalizer v2.0.0 (*)
    │   │       └── icu_properties v2.0.1 (*)
    │   ├── icu_normalizer v2.0.0 (*)
    │   ├── icu_properties v2.0.1 (*)
    │   ├── icu_provider v2.0.0 (*)
    │   ├── tinystr v0.8.1
    │   │   ├── icu_locale_core v2.0.0 (*)
    │   │   └── icu_provider v2.0.0 (*)
    │   └── zerotrie v0.2.2
    │       ├── icu_properties v2.0.1 (*)
    │       └── icu_provider v2.0.0 (*)
    ├── futures-macro v0.3.31
    │   └── futures-util v0.3.31
    │       ├── axum v0.8.4
    │       │   └── tonic v0.13.1 (*)
    │       ├── futures v0.3.31
    │       │   └── rustycluster v2.2.7 (*)
    │       ├── futures-executor v0.3.31
    │       │   └── futures v0.3.31 (*)
    │       ├── hyper v1.6.0
    │       │   ├── hyper-timeout v0.5.2
    │       │   │   └── tonic v0.13.1 (*)
    │       │   ├── hyper-util v0.1.14
    │       │   │   ├── hyper-timeout v0.5.2 (*)
    │       │   │   └── tonic v0.13.1 (*)
    │       │   └── tonic v0.13.1 (*)
    │       ├── hyper-util v0.1.14 (*)
    │       ├── redis v0.29.5 (*)
    │       ├── redis v0.31.0 (*)
    │       └── tower v0.5.2
    │           ├── axum v0.8.4 (*)
    │           └── tonic v0.13.1 (*)
    ├── pest_generator v2.8.1
    │   └── pest_derive v2.8.1
    │       └── json5 v0.4.1
    │           └── config v0.13.4 (*)
    ├── pin-project-internal v1.1.10
    │   └── pin-project v1.1.10
    │       └── tonic v0.13.1 (*)
    ├── prost-derive v0.13.5
    │   └── prost v0.13.5
    │       ├── prost-build v0.13.5
    │       │   └── tonic-build v0.13.1
    │       │       └── (build) rustycluster v2.2.7 (*)
    │       ├── prost-types v0.13.5
    │       │   ├── prost-build v0.13.5 (*)
    │       │   ├── rustycluster v2.2.7 (*)
    │       │   └── tonic-build v0.13.1 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       └── tonic v0.13.1 (*)
    ├── serde_derive v1.0.219
    │   └── serde v1.0.219
    │       ├── axum v0.8.4 (*)
    │       ├── config v0.13.4 (*)
    │       ├── json5 v0.4.1 (*)
    │       ├── ron v0.7.1
    │       │   └── config v0.13.4 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       ├── serde_json v1.0.140
    │       │   ├── config v0.13.4 (*)
    │       │   └── tracing-subscriber v0.3.19
    │       │       ├── rustycluster v2.2.7 (*)
    │       │       └── tracing-appender v0.2.3
    │       │           └── rustycluster v2.2.7 (*)
    │       ├── toml v0.5.11
    │       │   └── config v0.13.4 (*)
    │       ├── tracing-serde v0.2.0
    │       │   └── tracing-subscriber v0.3.19 (*)
    │       └── tracing-subscriber v0.3.19 (*)
    ├── syn v2.0.104
    │   ├── async-trait v0.1.88 (*)
    │   ├── displaydoc v0.2.5 (*)
    │   ├── futures-macro v0.3.31 (*)
    │   ├── pest_generator v2.8.1 (*)
    │   ├── pin-project-internal v1.1.10 (*)
    │   ├── prettyplease v0.2.35
    │   │   ├── prost-build v0.13.5 (*)
    │   │   └── tonic-build v0.13.1 (*)
    │   ├── prost-build v0.13.5 (*)
    │   ├── prost-derive v0.13.5 (*)
    │   ├── serde_derive v1.0.219 (*)
    │   ├── synstructure v0.13.2
    │   │   ├── yoke-derive v0.8.0
    │   │   │   └── yoke v0.8.0
    │   │   │       ├── icu_collections v2.0.0 (*)
    │   │   │       ├── icu_provider v2.0.0 (*)
    │   │   │       ├── zerotrie v0.2.2 (*)
    │   │   │       └── zerovec v0.11.2
    │   │   │           ├── icu_collections v2.0.0 (*)
    │   │   │           ├── icu_locale_core v2.0.0 (*)
    │   │   │           ├── icu_normalizer v2.0.0 (*)
    │   │   │           ├── icu_properties v2.0.1 (*)
    │   │   │           ├── icu_provider v2.0.0 (*)
    │   │   │           ├── potential_utf v0.1.2
    │   │   │           │   ├── icu_collections v2.0.0 (*)
    │   │   │           │   └── icu_properties v2.0.1 (*)
    │   │   │           └── tinystr v0.8.1 (*)
    │   │   └── zerofrom-derive v0.1.6
    │   │       └── zerofrom v0.1.6
    │   │           ├── icu_collections v2.0.0 (*)
    │   │           ├── icu_provider v2.0.0 (*)
    │   │           ├── yoke v0.8.0 (*)
    │   │           ├── zerotrie v0.2.2 (*)
    │   │           └── zerovec v0.11.2 (*)
    │   ├── thiserror-impl v1.0.69
    │   │   └── thiserror v1.0.69
    │   │       ├── protobuf v3.7.2
    │   │       │   └── prometheus v0.14.0
    │   │       │       └── rustycluster v2.2.7 (*)
    │   │       ├── protobuf-support v3.7.2
    │   │       │   └── protobuf v3.7.2 (*)
    │   │       └── tracing-appender v0.2.3 (*)
    │   ├── thiserror-impl v2.0.12
    │   │   └── thiserror v2.0.12
    │   │       ├── pest v2.8.1
    │   │       │   ├── json5 v0.4.1 (*)
    │   │       │   ├── pest_derive v2.8.1 (*)
    │   │       │   ├── pest_generator v2.8.1 (*)
    │   │       │   └── pest_meta v2.8.1
    │   │       │       └── pest_generator v2.8.1 (*)
    │   │       └── prometheus v0.14.0 (*)
    │   ├── tokio-macros v2.5.0
    │   │   └── tokio v1.45.1
    │   │       ├── combine v4.6.7
    │   │       │   ├── redis v0.29.5 (*)
    │   │       │   └── redis v0.31.0 (*)
    │   │       ├── deadpool v0.12.2
    │   │       │   └── deadpool-redis v0.20.0 (*)
    │   │       ├── deadpool-runtime v0.1.4
    │   │       │   └── deadpool v0.12.2 (*)
    │   │       ├── h2 v0.4.10
    │   │       │   ├── hyper v1.6.0 (*)
    │   │       │   └── tonic v0.13.1 (*)
    │   │       ├── hyper v1.6.0 (*)
    │   │       ├── hyper-timeout v0.5.2 (*)
    │   │       ├── hyper-util v0.1.14 (*)
    │   │       ├── redis v0.29.5 (*)
    │   │       ├── redis v0.31.0 (*)
    │   │       ├── rustycluster v2.2.7 (*)
    │   │       ├── tokio-stream v0.1.17
    │   │       │   └── tonic v0.13.1 (*)
    │   │       ├── tokio-util v0.7.15
    │   │       │   ├── combine v4.6.7 (*)
    │   │       │   ├── h2 v0.4.10 (*)
    │   │       │   ├── redis v0.29.5 (*)
    │   │       │   ├── redis v0.31.0 (*)
    │   │       │   └── tower v0.5.2 (*)
    │   │       ├── tonic v0.13.1 (*)
    │   │       └── tower v0.5.2 (*)
    │   ├── tonic-build v0.13.1 (*)
    │   ├── tracing-attributes v0.1.30
    │   │   └── tracing v0.1.41
    │   │       ├── h2 v0.4.10 (*)
    │   │       ├── hyper-util v0.1.14 (*)
    │   │       ├── rustycluster v2.2.7 (*)
    │   │       ├── tokio v1.45.1 (*)
    │   │       ├── tonic v0.13.1 (*)
    │   │       ├── tower v0.5.2 (*)
    │   │       └── tracing-subscriber v0.3.19 (*)
    │   ├── wasm-bindgen-backend v0.2.100
    │   │   └── wasm-bindgen-macro-support v0.2.100
    │   │       └── wasm-bindgen-macro v0.2.100
    │   │           └── wasm-bindgen v0.2.100
    │   │               ├── iana-time-zone v0.1.63
    │   │               │   └── chrono v0.4.41
    │   │               │       └── tracing-subscriber v0.3.19 (*)
    │   │               └── js-sys v0.3.77
    │   │                   └── iana-time-zone v0.1.63 (*)
    │   ├── wasm-bindgen-macro-support v0.2.100 (*)
    │   ├── windows-implement v0.60.0
    │   │   └── windows-core v0.61.2
    │   │       └── iana-time-zone v0.1.63 (*)
    │   ├── windows-interface v0.59.1
    │   │   └── windows-core v0.61.2 (*)
    │   ├── yoke-derive v0.8.0 (*)
    │   ├── zerofrom-derive v0.1.6 (*)
    │   └── zerovec-derive v0.11.1
    │       └── zerovec v0.11.2 (*)
    ├── synstructure v0.13.2 (*)
    ├── thiserror-impl v1.0.69 (*)
    ├── thiserror-impl v2.0.12 (*)
    ├── tokio-macros v2.5.0 (*)
    ├── tonic-build v0.13.1 (*)
    ├── tracing-attributes v0.1.30 (*)
    ├── wasm-bindgen-backend v0.2.100 (*)
    ├── wasm-bindgen-macro v0.2.100 (*)
    ├── wasm-bindgen-macro-support v0.2.100 (*)
    ├── windows-implement v0.60.0 (*)
    ├── windows-interface v0.59.1 (*)
    ├── yoke-derive v0.8.0 (*)
    ├── zerofrom-derive v0.1.6 (*)
    └── zerovec-derive v0.11.1 (*)

warning[duplicate]: found 2 duplicate entries for crate 'thiserror'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:185:1
    │  
185 │ ╭ thiserror 1.0.69 registry+https://github.com/rust-lang/crates.io-index
186 │ │ thiserror 2.0.12 registry+https://github.com/rust-lang/crates.io-index
    │ ╰──────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ thiserror v1.0.69
      ├── protobuf v3.7.2
      │   └── prometheus v0.14.0
      │       └── rustycluster v2.2.7
      ├── protobuf-support v3.7.2
      │   └── protobuf v3.7.2 (*)
      └── tracing-appender v0.2.3
          └── rustycluster v2.2.7 (*)
    ├ thiserror v2.0.12
      ├── pest v2.8.1
      │   ├── json5 v0.4.1
      │   │   └── config v0.13.4
      │   │       └── rustycluster v2.2.7
      │   ├── pest_derive v2.8.1
      │   │   └── json5 v0.4.1 (*)
      │   ├── pest_generator v2.8.1
      │   │   └── pest_derive v2.8.1 (*)
      │   └── pest_meta v2.8.1
      │       └── pest_generator v2.8.1 (*)
      └── prometheus v0.14.0
          └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#r-efi@5.3.0:4:12
  │
4 │ license = "MIT OR Apache-2.0 OR LGPL-2.1-or-later"
  │            ━━━────━━━━━━━━━━────━━━━━━━━━━━━━━━━━
  │            │      │             │
  │            │      │             rejected: license is not explicitly allowed
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ LGPL-2.1 - GNU Lesser General Public License v2.1 only:
  ├   - **DEPRECATED**
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├   - Copyleft
  ├ r-efi v5.3.0
    └── getrandom v0.3.3
        ├── tempfile v3.20.0
        │   └── prost-build v0.13.5
        │       └── tonic-build v0.13.1
        │           └── (build) rustycluster v2.2.7
        └── uuid v1.17.0
            └── rustycluster v2.2.7 (*)

warning[duplicate]: found 2 duplicate entries for crate 'thiserror-impl'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:187:1
    │  
187 │ ╭ thiserror-impl 1.0.69 registry+https://github.com/rust-lang/crates.io-index
188 │ │ thiserror-impl 2.0.12 registry+https://github.com/rust-lang/crates.io-index
    │ ╰───────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ thiserror-impl v1.0.69
      └── thiserror v1.0.69
          ├── protobuf v3.7.2
          │   └── prometheus v0.14.0
          │       └── rustycluster v2.2.7
          ├── protobuf-support v3.7.2
          │   └── protobuf v3.7.2 (*)
          └── tracing-appender v0.2.3
              └── rustycluster v2.2.7 (*)
    ├ thiserror-impl v2.0.12
      └── thiserror v2.0.12
          ├── pest v2.8.1
          │   ├── json5 v0.4.1
          │   │   └── config v0.13.4
          │   │       └── rustycluster v2.2.7
          │   ├── pest_derive v2.8.1
          │   │   └── json5 v0.4.1 (*)
          │   ├── pest_generator v2.8.1
          │   │   └── pest_derive v2.8.1 (*)
          │   └── pest_meta v2.8.1
          │       └── pest_generator v2.8.1 (*)
          └── prometheus v0.14.0
              └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#rand@0.8.5:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ rand v0.8.5
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#rand_chacha@0.3.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ rand_chacha v0.3.1
    └── rand v0.8.5
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#rand_core@0.6.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ rand_core v0.6.4
    ├── crypto-common v0.1.6
    │   ├── aead v0.5.2
    │   │   └── aes-gcm v0.10.3
    │   │       └── rustycluster v2.2.7
    │   ├── cipher v0.4.4
    │   │   ├── aes v0.8.4
    │   │   │   └── aes-gcm v0.10.3 (*)
    │   │   ├── aes-gcm v0.10.3 (*)
    │   │   └── ctr v0.9.2
    │   │       └── aes-gcm v0.10.3 (*)
    │   ├── digest v0.10.7
    │   │   ├── hmac v0.12.1
    │   │   │   └── pbkdf2 v0.12.2
    │   │   │       └── rustycluster v2.2.7 (*)
    │   │   ├── pbkdf2 v0.12.2 (*)
    │   │   └── sha2 v0.10.9
    │   │       ├── (build) pest_meta v2.8.1
    │   │       │   └── pest_generator v2.8.1
    │   │       │       └── pest_derive v2.8.1
    │   │       │           └── json5 v0.4.1
    │   │       │               └── config v0.13.4
    │   │       │                   └── rustycluster v2.2.7 (*)
    │   │       └── rustycluster v2.2.7 (*)
    │   └── universal-hash v0.5.1
    │       └── polyval v0.6.2
    │           └── ghash v0.5.1
    │               └── aes-gcm v0.10.3 (*)
    ├── rand v0.8.5
    │   └── rustycluster v2.2.7 (*)
    └── rand_chacha v0.3.1
        └── rand v0.8.5 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#redis@0.29.5:4:12
  │
4 │ license = "BSD-3-Clause"
  │            ━━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ BSD-3-Clause - BSD 3-Clause "New" or "Revised" License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ redis v0.29.5
    └── deadpool-redis v0.20.0
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#redis@0.31.0:4:12
  │
4 │ license = "BSD-3-Clause"
  │            ━━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ BSD-3-Clause - BSD 3-Clause "New" or "Revised" License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ redis v0.31.0
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#redox_syscall@0.5.13:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ redox_syscall v0.5.13
    └── parking_lot_core v0.9.11
        ├── dashmap v6.1.0
        │   └── rustycluster v2.2.7
        └── parking_lot v0.12.4
            ├── prometheus v0.14.0
            │   └── rustycluster v2.2.7 (*)
            ├── rustycluster v2.2.7 (*)
            └── tokio v1.45.1
                ├── combine v4.6.7
                │   ├── redis v0.29.5
                │   │   └── deadpool-redis v0.20.0
                │   │       └── rustycluster v2.2.7 (*)
                │   └── redis v0.31.0
                │       └── rustycluster v2.2.7 (*)
                ├── deadpool v0.12.2
                │   └── deadpool-redis v0.20.0 (*)
                ├── deadpool-runtime v0.1.4
                │   └── deadpool v0.12.2 (*)
                ├── h2 v0.4.10
                │   ├── hyper v1.6.0
                │   │   ├── hyper-timeout v0.5.2
                │   │   │   └── tonic v0.13.1
                │   │   │       └── rustycluster v2.2.7 (*)
                │   │   ├── hyper-util v0.1.14
                │   │   │   ├── hyper-timeout v0.5.2 (*)
                │   │   │   └── tonic v0.13.1 (*)
                │   │   └── tonic v0.13.1 (*)
                │   └── tonic v0.13.1 (*)
                ├── hyper v1.6.0 (*)
                ├── hyper-timeout v0.5.2 (*)
                ├── hyper-util v0.1.14 (*)
                ├── redis v0.29.5 (*)
                ├── redis v0.31.0 (*)
                ├── rustycluster v2.2.7 (*)
                ├── tokio-stream v0.1.17
                │   └── tonic v0.13.1 (*)
                ├── tokio-util v0.7.15
                │   ├── combine v4.6.7 (*)
                │   ├── h2 v0.4.10 (*)
                │   ├── redis v0.29.5 (*)
                │   ├── redis v0.31.0 (*)
                │   └── tower v0.5.2
                │       ├── axum v0.8.4
                │       │   └── tonic v0.13.1 (*)
                │       └── tonic v0.13.1 (*)
                ├── tonic v0.13.1 (*)
                └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#regex@1.11.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ regex v1.11.1
    ├── prost-build v0.13.5
    │   └── tonic-build v0.13.1
    │       └── (build) rustycluster v2.2.7
    └── tracing-subscriber v0.3.19
        ├── rustycluster v2.2.7 (*)
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#regex-automata@0.1.10:4:12
  │
4 │ license = "Unlicense OR MIT"
  │            ━━━━━━━━━────━━━
  │            │            │
  │            │            rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unlicense - The Unlicense:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ regex-automata v0.1.10
    └── matchers v0.1.0
        └── tracing-subscriber v0.3.19
            ├── rustycluster v2.2.7
            └── tracing-appender v0.2.3
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#regex-automata@0.4.9:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ regex-automata v0.4.9
    └── regex v1.11.1
        ├── prost-build v0.13.5
        │   └── tonic-build v0.13.1
        │       └── (build) rustycluster v2.2.7
        └── tracing-subscriber v0.3.19
            ├── rustycluster v2.2.7 (*)
            └── tracing-appender v0.2.3
                └── rustycluster v2.2.7 (*)

warning[duplicate]: found 2 duplicate entries for crate 'tracing-log'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:207:1
    │  
207 │ ╭ tracing-log 0.1.4 registry+https://github.com/rust-lang/crates.io-index
208 │ │ tracing-log 0.2.0 registry+https://github.com/rust-lang/crates.io-index
    │ ╰───────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ tracing-log v0.1.4
      └── rustycluster v2.2.7
    ├ tracing-log v0.2.0
      └── tracing-subscriber v0.3.19
          ├── rustycluster v2.2.7
          └── tracing-appender v0.2.3
              └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#regex-syntax@0.6.29:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ regex-syntax v0.6.29
    └── regex-automata v0.1.10
        └── matchers v0.1.0
            └── tracing-subscriber v0.3.19
                ├── rustycluster v2.2.7
                └── tracing-appender v0.2.3
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#regex-syntax@0.8.5:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ regex-syntax v0.8.5
    ├── regex v1.11.1
    │   ├── prost-build v0.13.5
    │   │   └── tonic-build v0.13.1
    │   │       └── (build) rustycluster v2.2.7
    │   └── tracing-subscriber v0.3.19
    │       ├── rustycluster v2.2.7 (*)
    │       └── tracing-appender v0.2.3
    │           └── rustycluster v2.2.7 (*)
    └── regex-automata v0.4.9
        └── regex v1.11.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#ron@0.7.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ ron v0.7.1
    └── config v0.13.4
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#rustc-demangle@0.1.25:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ rustc-demangle v0.1.25
    └── backtrace v0.3.75
        └── tokio v1.45.1
            ├── combine v4.6.7
            │   ├── redis v0.29.5
            │   │   └── deadpool-redis v0.20.0
            │   │       └── rustycluster v2.2.7
            │   └── redis v0.31.0
            │       └── rustycluster v2.2.7 (*)
            ├── deadpool v0.12.2
            │   └── deadpool-redis v0.20.0 (*)
            ├── deadpool-runtime v0.1.4
            │   └── deadpool v0.12.2 (*)
            ├── h2 v0.4.10
            │   ├── hyper v1.6.0
            │   │   ├── hyper-timeout v0.5.2
            │   │   │   └── tonic v0.13.1
            │   │   │       └── rustycluster v2.2.7 (*)
            │   │   ├── hyper-util v0.1.14
            │   │   │   ├── hyper-timeout v0.5.2 (*)
            │   │   │   └── tonic v0.13.1 (*)
            │   │   └── tonic v0.13.1 (*)
            │   └── tonic v0.13.1 (*)
            ├── hyper v1.6.0 (*)
            ├── hyper-timeout v0.5.2 (*)
            ├── hyper-util v0.1.14 (*)
            ├── redis v0.29.5 (*)
            ├── redis v0.31.0 (*)
            ├── rustycluster v2.2.7 (*)
            ├── tokio-stream v0.1.17
            │   └── tonic v0.13.1 (*)
            ├── tokio-util v0.7.15
            │   ├── combine v4.6.7 (*)
            │   ├── h2 v0.4.10 (*)
            │   ├── redis v0.29.5 (*)
            │   ├── redis v0.31.0 (*)
            │   └── tower v0.5.2
            │       ├── axum v0.8.4
            │       │   └── tonic v0.13.1 (*)
            │       └── tonic v0.13.1 (*)
            ├── tonic v0.13.1 (*)
            └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#rustix@1.0.7:4:12
  │
4 │ license = "Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────────────────────────━━━━━━━━━━────━━━
  │            │                                 │             │
  │            │                                 │             rejected: license is not explicitly allowed
  │            │                                 rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ rustix v1.0.7
    └── tempfile v3.20.0
        └── prost-build v0.13.5
            └── tonic-build v0.13.1
                └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#rustversion@1.0.21:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ rustversion v1.0.21
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── axum-core v0.5.2
    │   └── axum v0.8.4 (*)
    └── wasm-bindgen v0.2.100
        ├── iana-time-zone v0.1.63
        │   └── chrono v0.4.41
        │       └── tracing-subscriber v0.3.19
        │           ├── rustycluster v2.2.7 (*)
        │           └── tracing-appender v0.2.3
        │               └── rustycluster v2.2.7 (*)
        └── js-sys v0.3.77
            └── iana-time-zone v0.1.63 (*)

error[unlicensed]: rustycluster = 2.2.7 is unlicensed
  ┌─ path+file:///C:/Users/<USER>/Desktop/rust/rustycluster#2.2.7:2:9
  │
2 │ name = "rustycluster"
  │         ━━━━━━━━━━━━ a valid license expression could not be retrieved for the crate
3 │ version = "2.2.7"
4 │ license = ""
  │            ─ license expression was not specified
  │
  ├ rustycluster v2.2.7

warning[duplicate]: found 2 duplicate entries for crate 'wasi'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:221:1
    │  
221 │ ╭ wasi 0.11.1+wasi-snapshot-preview1 registry+https://github.com/rust-lang/crates.io-index
222 │ │ wasi 0.14.2+wasi-0.2.4 registry+https://github.com/rust-lang/crates.io-index
    │ ╰────────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ wasi v0.11.1+wasi-snapshot-preview1
      ├── getrandom v0.2.16
      │   └── rand_core v0.6.4
      │       ├── crypto-common v0.1.6
      │       │   ├── aead v0.5.2
      │       │   │   └── aes-gcm v0.10.3
      │       │   │       └── rustycluster v2.2.7
      │       │   ├── cipher v0.4.4
      │       │   │   ├── aes v0.8.4
      │       │   │   │   └── aes-gcm v0.10.3 (*)
      │       │   │   ├── aes-gcm v0.10.3 (*)
      │       │   │   └── ctr v0.9.2
      │       │   │       └── aes-gcm v0.10.3 (*)
      │       │   ├── digest v0.10.7
      │       │   │   ├── hmac v0.12.1
      │       │   │   │   └── pbkdf2 v0.12.2
      │       │   │   │       └── rustycluster v2.2.7 (*)
      │       │   │   ├── pbkdf2 v0.12.2 (*)
      │       │   │   └── sha2 v0.10.9
      │       │   │       ├── (build) pest_meta v2.8.1
      │       │   │       │   └── pest_generator v2.8.1
      │       │   │       │       └── pest_derive v2.8.1
      │       │   │       │           └── json5 v0.4.1
      │       │   │       │               └── config v0.13.4
      │       │   │       │                   └── rustycluster v2.2.7 (*)
      │       │   │       └── rustycluster v2.2.7 (*)
      │       │   └── universal-hash v0.5.1
      │       │       └── polyval v0.6.2
      │       │           └── ghash v0.5.1
      │       │               └── aes-gcm v0.10.3 (*)
      │       ├── rand v0.8.5
      │       │   └── rustycluster v2.2.7 (*)
      │       └── rand_chacha v0.3.1
      │           └── rand v0.8.5 (*)
      └── mio v1.0.4
          └── tokio v1.45.1
              ├── combine v4.6.7
              │   ├── redis v0.29.5
              │   │   └── deadpool-redis v0.20.0
              │   │       └── rustycluster v2.2.7 (*)
              │   └── redis v0.31.0
              │       └── rustycluster v2.2.7 (*)
              ├── deadpool v0.12.2
              │   └── deadpool-redis v0.20.0 (*)
              ├── deadpool-runtime v0.1.4
              │   └── deadpool v0.12.2 (*)
              ├── h2 v0.4.10
              │   ├── hyper v1.6.0
              │   │   ├── hyper-timeout v0.5.2
              │   │   │   └── tonic v0.13.1
              │   │   │       └── rustycluster v2.2.7 (*)
              │   │   ├── hyper-util v0.1.14
              │   │   │   ├── hyper-timeout v0.5.2 (*)
              │   │   │   └── tonic v0.13.1 (*)
              │   │   └── tonic v0.13.1 (*)
              │   └── tonic v0.13.1 (*)
              ├── hyper v1.6.0 (*)
              ├── hyper-timeout v0.5.2 (*)
              ├── hyper-util v0.1.14 (*)
              ├── redis v0.29.5 (*)
              ├── redis v0.31.0 (*)
              ├── rustycluster v2.2.7 (*)
              ├── tokio-stream v0.1.17
              │   └── tonic v0.13.1 (*)
              ├── tokio-util v0.7.15
              │   ├── combine v4.6.7 (*)
              │   ├── h2 v0.4.10 (*)
              │   ├── redis v0.29.5 (*)
              │   ├── redis v0.31.0 (*)
              │   └── tower v0.5.2
              │       ├── axum v0.8.4
              │       │   └── tonic v0.13.1 (*)
              │       └── tonic v0.13.1 (*)
              ├── tonic v0.13.1 (*)
              └── tower v0.5.2 (*)
    ├ wasi v0.14.2+wasi-0.2.4
      └── getrandom v0.3.3
          ├── tempfile v3.20.0
          │   └── prost-build v0.13.5
          │       └── tonic-build v0.13.1
          │           └── (build) rustycluster v2.2.7
          └── uuid v1.17.0
              └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#ryu@1.0.20:4:12
  │
4 │ license = "Apache-2.0 OR BSL-1.0"
  │            ━━━━━━━━━━────━━━━━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ BSL-1.0 - Boost Software License 1.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ ryu v1.0.20
    ├── redis v0.29.5
    │   └── deadpool-redis v0.20.0
    │       └── rustycluster v2.2.7
    ├── redis v0.31.0
    │   └── rustycluster v2.2.7 (*)
    └── serde_json v1.0.140
        ├── config v0.13.4
        │   └── rustycluster v2.2.7 (*)
        └── tracing-subscriber v0.3.19
            ├── rustycluster v2.2.7 (*)
            └── tracing-appender v0.2.3
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#scopeguard@1.2.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ scopeguard v1.2.0
    └── lock_api v0.4.13
        ├── dashmap v6.1.0
        │   └── rustycluster v2.2.7
        └── parking_lot v0.12.4
            ├── prometheus v0.14.0
            │   └── rustycluster v2.2.7 (*)
            ├── rustycluster v2.2.7 (*)
            └── tokio v1.45.1
                ├── combine v4.6.7
                │   ├── redis v0.29.5
                │   │   └── deadpool-redis v0.20.0
                │   │       └── rustycluster v2.2.7 (*)
                │   └── redis v0.31.0
                │       └── rustycluster v2.2.7 (*)
                ├── deadpool v0.12.2
                │   └── deadpool-redis v0.20.0 (*)
                ├── deadpool-runtime v0.1.4
                │   └── deadpool v0.12.2 (*)
                ├── h2 v0.4.10
                │   ├── hyper v1.6.0
                │   │   ├── hyper-timeout v0.5.2
                │   │   │   └── tonic v0.13.1
                │   │   │       └── rustycluster v2.2.7 (*)
                │   │   ├── hyper-util v0.1.14
                │   │   │   ├── hyper-timeout v0.5.2 (*)
                │   │   │   └── tonic v0.13.1 (*)
                │   │   └── tonic v0.13.1 (*)
                │   └── tonic v0.13.1 (*)
                ├── hyper v1.6.0 (*)
                ├── hyper-timeout v0.5.2 (*)
                ├── hyper-util v0.1.14 (*)
                ├── redis v0.29.5 (*)
                ├── redis v0.31.0 (*)
                ├── rustycluster v2.2.7 (*)
                ├── tokio-stream v0.1.17
                │   └── tonic v0.13.1 (*)
                ├── tokio-util v0.7.15
                │   ├── combine v4.6.7 (*)
                │   ├── h2 v0.4.10 (*)
                │   ├── redis v0.29.5 (*)
                │   ├── redis v0.31.0 (*)
                │   └── tower v0.5.2
                │       ├── axum v0.8.4
                │       │   └── tonic v0.13.1 (*)
                │       └── tonic v0.13.1 (*)
                ├── tonic v0.13.1 (*)
                └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#serde@1.0.219:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ serde v1.0.219
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── config v0.13.4
    │   └── rustycluster v2.2.7 (*)
    ├── json5 v0.4.1
    │   └── config v0.13.4 (*)
    ├── ron v0.7.1
    │   └── config v0.13.4 (*)
    ├── rustycluster v2.2.7 (*)
    ├── serde_json v1.0.140
    │   ├── config v0.13.4 (*)
    │   └── tracing-subscriber v0.3.19
    │       ├── rustycluster v2.2.7 (*)
    │       └── tracing-appender v0.2.3
    │           └── rustycluster v2.2.7 (*)
    ├── toml v0.5.11
    │   └── config v0.13.4 (*)
    ├── tracing-serde v0.2.0
    │   └── tracing-subscriber v0.3.19 (*)
    └── tracing-subscriber v0.3.19 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#serde_derive@1.0.219:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ serde_derive v1.0.219
    └── serde v1.0.219
        ├── axum v0.8.4
        │   └── tonic v0.13.1
        │       └── rustycluster v2.2.7
        ├── config v0.13.4
        │   └── rustycluster v2.2.7 (*)
        ├── json5 v0.4.1
        │   └── config v0.13.4 (*)
        ├── ron v0.7.1
        │   └── config v0.13.4 (*)
        ├── rustycluster v2.2.7 (*)
        ├── serde_json v1.0.140
        │   ├── config v0.13.4 (*)
        │   └── tracing-subscriber v0.3.19
        │       ├── rustycluster v2.2.7 (*)
        │       └── tracing-appender v0.2.3
        │           └── rustycluster v2.2.7 (*)
        ├── toml v0.5.11
        │   └── config v0.13.4 (*)
        ├── tracing-serde v0.2.0
        │   └── tracing-subscriber v0.3.19 (*)
        └── tracing-subscriber v0.3.19 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#serde_json@1.0.140:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ serde_json v1.0.140
    ├── config v0.13.4
    │   └── rustycluster v2.2.7
    └── tracing-subscriber v0.3.19
        ├── rustycluster v2.2.7 (*)
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#sha1_smol@1.0.1:4:12
  │
4 │ license = "BSD-3-Clause"
  │            ━━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ BSD-3-Clause - BSD 3-Clause "New" or "Revised" License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ sha1_smol v1.0.1
    └── redis v0.31.0
        └── rustycluster v2.2.7

warning[duplicate]: found 3 duplicate entries for crate 'windows-sys'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:237:1
    │  
237 │ ╭ windows-sys 0.52.0 registry+https://github.com/rust-lang/crates.io-index
238 │ │ windows-sys 0.59.0 registry+https://github.com/rust-lang/crates.io-index
239 │ │ windows-sys 0.60.2 registry+https://github.com/rust-lang/crates.io-index
    │ ╰────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ windows-sys v0.52.0
      ├── socket2 v0.5.10
      │   ├── hyper-util v0.1.14
      │   │   ├── hyper-timeout v0.5.2
      │   │   │   └── tonic v0.13.1
      │   │   │       └── rustycluster v2.2.7
      │   │   └── tonic v0.13.1 (*)
      │   ├── redis v0.29.5
      │   │   └── deadpool-redis v0.20.0
      │   │       └── rustycluster v2.2.7 (*)
      │   ├── redis v0.31.0
      │   │   └── rustycluster v2.2.7 (*)
      │   ├── tokio v1.45.1
      │   │   ├── combine v4.6.7
      │   │   │   ├── redis v0.29.5 (*)
      │   │   │   └── redis v0.31.0 (*)
      │   │   ├── deadpool v0.12.2
      │   │   │   └── deadpool-redis v0.20.0 (*)
      │   │   ├── deadpool-runtime v0.1.4
      │   │   │   └── deadpool v0.12.2 (*)
      │   │   ├── h2 v0.4.10
      │   │   │   ├── hyper v1.6.0
      │   │   │   │   ├── hyper-timeout v0.5.2 (*)
      │   │   │   │   ├── hyper-util v0.1.14 (*)
      │   │   │   │   └── tonic v0.13.1 (*)
      │   │   │   └── tonic v0.13.1 (*)
      │   │   ├── hyper v1.6.0 (*)
      │   │   ├── hyper-timeout v0.5.2 (*)
      │   │   ├── hyper-util v0.1.14 (*)
      │   │   ├── redis v0.29.5 (*)
      │   │   ├── redis v0.31.0 (*)
      │   │   ├── rustycluster v2.2.7 (*)
      │   │   ├── tokio-stream v0.1.17
      │   │   │   └── tonic v0.13.1 (*)
      │   │   ├── tokio-util v0.7.15
      │   │   │   ├── combine v4.6.7 (*)
      │   │   │   ├── h2 v0.4.10 (*)
      │   │   │   ├── redis v0.29.5 (*)
      │   │   │   ├── redis v0.31.0 (*)
      │   │   │   └── tower v0.5.2
      │   │   │       ├── axum v0.8.4
      │   │   │       │   └── tonic v0.13.1 (*)
      │   │   │       └── tonic v0.13.1 (*)
      │   │   ├── tonic v0.13.1 (*)
      │   │   └── tower v0.5.2 (*)
      │   └── tonic v0.13.1 (*)
      └── tokio v1.45.1 (*)
    ├ windows-sys v0.59.0
      ├── mio v1.0.4
      │   └── tokio v1.45.1
      │       ├── combine v4.6.7
      │       │   ├── redis v0.29.5
      │       │   │   └── deadpool-redis v0.20.0
      │       │   │       └── rustycluster v2.2.7
      │       │   └── redis v0.31.0
      │       │       └── rustycluster v2.2.7 (*)
      │       ├── deadpool v0.12.2
      │       │   └── deadpool-redis v0.20.0 (*)
      │       ├── deadpool-runtime v0.1.4
      │       │   └── deadpool v0.12.2 (*)
      │       ├── h2 v0.4.10
      │       │   ├── hyper v1.6.0
      │       │   │   ├── hyper-timeout v0.5.2
      │       │   │   │   └── tonic v0.13.1
      │       │   │   │       └── rustycluster v2.2.7 (*)
      │       │   │   ├── hyper-util v0.1.14
      │       │   │   │   ├── hyper-timeout v0.5.2 (*)
      │       │   │   │   └── tonic v0.13.1 (*)
      │       │   │   └── tonic v0.13.1 (*)
      │       │   └── tonic v0.13.1 (*)
      │       ├── hyper v1.6.0 (*)
      │       ├── hyper-timeout v0.5.2 (*)
      │       ├── hyper-util v0.1.14 (*)
      │       ├── redis v0.29.5 (*)
      │       ├── redis v0.31.0 (*)
      │       ├── rustycluster v2.2.7 (*)
      │       ├── tokio-stream v0.1.17
      │       │   └── tonic v0.13.1 (*)
      │       ├── tokio-util v0.7.15
      │       │   ├── combine v4.6.7 (*)
      │       │   ├── h2 v0.4.10 (*)
      │       │   ├── redis v0.29.5 (*)
      │       │   ├── redis v0.31.0 (*)
      │       │   └── tower v0.5.2
      │       │       ├── axum v0.8.4
      │       │       │   └── tonic v0.13.1 (*)
      │       │       └── tonic v0.13.1 (*)
      │       ├── tonic v0.13.1 (*)
      │       └── tower v0.5.2 (*)
      ├── rustix v1.0.7
      │   └── tempfile v3.20.0
      │       └── prost-build v0.13.5
      │           └── tonic-build v0.13.1
      │               └── (build) rustycluster v2.2.7 (*)
      └── tempfile v3.20.0 (*)
    ├ windows-sys v0.60.2
      └── errno v0.3.13
          └── rustix v1.0.7
              └── tempfile v3.20.0
                  └── prost-build v0.13.5
                      └── tonic-build v0.13.1
                          └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#sha2@0.10.9:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ sha2 v0.10.9
    ├── (build) pest_meta v2.8.1
    │   └── pest_generator v2.8.1
    │       └── pest_derive v2.8.1
    │           └── json5 v0.4.1
    │               └── config v0.13.4
    │                   └── rustycluster v2.2.7
    └── rustycluster v2.2.7 (*)

warning[duplicate]: found 2 duplicate entries for crate 'windows-targets'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:240:1
    │  
240 │ ╭ windows-targets 0.52.6 registry+https://github.com/rust-lang/crates.io-index
241 │ │ windows-targets 0.53.2 registry+https://github.com/rust-lang/crates.io-index
    │ ╰────────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ windows-targets v0.52.6
      ├── backtrace v0.3.75
      │   └── tokio v1.45.1
      │       ├── combine v4.6.7
      │       │   ├── redis v0.29.5
      │       │   │   └── deadpool-redis v0.20.0
      │       │   │       └── rustycluster v2.2.7
      │       │   └── redis v0.31.0
      │       │       └── rustycluster v2.2.7 (*)
      │       ├── deadpool v0.12.2
      │       │   └── deadpool-redis v0.20.0 (*)
      │       ├── deadpool-runtime v0.1.4
      │       │   └── deadpool v0.12.2 (*)
      │       ├── h2 v0.4.10
      │       │   ├── hyper v1.6.0
      │       │   │   ├── hyper-timeout v0.5.2
      │       │   │   │   └── tonic v0.13.1
      │       │   │   │       └── rustycluster v2.2.7 (*)
      │       │   │   ├── hyper-util v0.1.14
      │       │   │   │   ├── hyper-timeout v0.5.2 (*)
      │       │   │   │   └── tonic v0.13.1 (*)
      │       │   │   └── tonic v0.13.1 (*)
      │       │   └── tonic v0.13.1 (*)
      │       ├── hyper v1.6.0 (*)
      │       ├── hyper-timeout v0.5.2 (*)
      │       ├── hyper-util v0.1.14 (*)
      │       ├── redis v0.29.5 (*)
      │       ├── redis v0.31.0 (*)
      │       ├── rustycluster v2.2.7 (*)
      │       ├── tokio-stream v0.1.17
      │       │   └── tonic v0.13.1 (*)
      │       ├── tokio-util v0.7.15
      │       │   ├── combine v4.6.7 (*)
      │       │   ├── h2 v0.4.10 (*)
      │       │   ├── redis v0.29.5 (*)
      │       │   ├── redis v0.31.0 (*)
      │       │   └── tower v0.5.2
      │       │       ├── axum v0.8.4
      │       │       │   └── tonic v0.13.1 (*)
      │       │       └── tonic v0.13.1 (*)
      │       ├── tonic v0.13.1 (*)
      │       └── tower v0.5.2 (*)
      ├── parking_lot_core v0.9.11
      │   ├── dashmap v6.1.0
      │   │   └── rustycluster v2.2.7 (*)
      │   └── parking_lot v0.12.4
      │       ├── prometheus v0.14.0
      │       │   └── rustycluster v2.2.7 (*)
      │       ├── rustycluster v2.2.7 (*)
      │       └── tokio v1.45.1 (*)
      ├── windows-sys v0.52.0
      │   ├── socket2 v0.5.10
      │   │   ├── hyper-util v0.1.14 (*)
      │   │   ├── redis v0.29.5 (*)
      │   │   ├── redis v0.31.0 (*)
      │   │   ├── tokio v1.45.1 (*)
      │   │   └── tonic v0.13.1 (*)
      │   └── tokio v1.45.1 (*)
      └── windows-sys v0.59.0
          ├── mio v1.0.4
          │   └── tokio v1.45.1 (*)
          ├── rustix v1.0.7
          │   └── tempfile v3.20.0
          │       └── prost-build v0.13.5
          │           └── tonic-build v0.13.1
          │               └── (build) rustycluster v2.2.7 (*)
          └── tempfile v3.20.0 (*)
    ├ windows-targets v0.53.2
      └── windows-sys v0.60.2
          └── errno v0.3.13
              └── rustix v1.0.7
                  └── tempfile v3.20.0
                      └── prost-build v0.13.5
                          └── tonic-build v0.13.1
                              └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#sharded-slab@0.1.7:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ sharded-slab v0.1.7
    └── tracing-subscriber v0.3.19
        ├── rustycluster v2.2.7
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

warning[duplicate]: found 2 duplicate entries for crate 'windows_aarch64_gnullvm'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:242:1
    │  
242 │ ╭ windows_aarch64_gnullvm 0.52.6 registry+https://github.com/rust-lang/crates.io-index
243 │ │ windows_aarch64_gnullvm 0.53.0 registry+https://github.com/rust-lang/crates.io-index
    │ ╰────────────────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ windows_aarch64_gnullvm v0.52.6
      └── windows-targets v0.52.6
          ├── backtrace v0.3.75
          │   └── tokio v1.45.1
          │       ├── combine v4.6.7
          │       │   ├── redis v0.29.5
          │       │   │   └── deadpool-redis v0.20.0
          │       │   │       └── rustycluster v2.2.7
          │       │   └── redis v0.31.0
          │       │       └── rustycluster v2.2.7 (*)
          │       ├── deadpool v0.12.2
          │       │   └── deadpool-redis v0.20.0 (*)
          │       ├── deadpool-runtime v0.1.4
          │       │   └── deadpool v0.12.2 (*)
          │       ├── h2 v0.4.10
          │       │   ├── hyper v1.6.0
          │       │   │   ├── hyper-timeout v0.5.2
          │       │   │   │   └── tonic v0.13.1
          │       │   │   │       └── rustycluster v2.2.7 (*)
          │       │   │   ├── hyper-util v0.1.14
          │       │   │   │   ├── hyper-timeout v0.5.2 (*)
          │       │   │   │   └── tonic v0.13.1 (*)
          │       │   │   └── tonic v0.13.1 (*)
          │       │   └── tonic v0.13.1 (*)
          │       ├── hyper v1.6.0 (*)
          │       ├── hyper-timeout v0.5.2 (*)
          │       ├── hyper-util v0.1.14 (*)
          │       ├── redis v0.29.5 (*)
          │       ├── redis v0.31.0 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       ├── tokio-stream v0.1.17
          │       │   └── tonic v0.13.1 (*)
          │       ├── tokio-util v0.7.15
          │       │   ├── combine v4.6.7 (*)
          │       │   ├── h2 v0.4.10 (*)
          │       │   ├── redis v0.29.5 (*)
          │       │   ├── redis v0.31.0 (*)
          │       │   └── tower v0.5.2
          │       │       ├── axum v0.8.4
          │       │       │   └── tonic v0.13.1 (*)
          │       │       └── tonic v0.13.1 (*)
          │       ├── tonic v0.13.1 (*)
          │       └── tower v0.5.2 (*)
          ├── parking_lot_core v0.9.11
          │   ├── dashmap v6.1.0
          │   │   └── rustycluster v2.2.7 (*)
          │   └── parking_lot v0.12.4
          │       ├── prometheus v0.14.0
          │       │   └── rustycluster v2.2.7 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       └── tokio v1.45.1 (*)
          ├── windows-sys v0.52.0
          │   ├── socket2 v0.5.10
          │   │   ├── hyper-util v0.1.14 (*)
          │   │   ├── redis v0.29.5 (*)
          │   │   ├── redis v0.31.0 (*)
          │   │   ├── tokio v1.45.1 (*)
          │   │   └── tonic v0.13.1 (*)
          │   └── tokio v1.45.1 (*)
          └── windows-sys v0.59.0
              ├── mio v1.0.4
              │   └── tokio v1.45.1 (*)
              ├── rustix v1.0.7
              │   └── tempfile v3.20.0
              │       └── prost-build v0.13.5
              │           └── tonic-build v0.13.1
              │               └── (build) rustycluster v2.2.7 (*)
              └── tempfile v3.20.0 (*)
    ├ windows_aarch64_gnullvm v0.53.0
      └── windows-targets v0.53.2
          └── windows-sys v0.60.2
              └── errno v0.3.13
                  └── rustix v1.0.7
                      └── tempfile v3.20.0
                          └── prost-build v0.13.5
                              └── tonic-build v0.13.1
                                  └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#shlex@1.3.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ shlex v1.3.0
    └── cc v1.2.27
        └── (build) iana-time-zone-haiku v0.1.2
            └── iana-time-zone v0.1.63
                └── chrono v0.4.41
                    └── tracing-subscriber v0.3.19
                        ├── rustycluster v2.2.7
                        └── tracing-appender v0.2.3
                            └── rustycluster v2.2.7 (*)

warning[duplicate]: found 2 duplicate entries for crate 'windows_aarch64_msvc'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:244:1
    │  
244 │ ╭ windows_aarch64_msvc 0.52.6 registry+https://github.com/rust-lang/crates.io-index
245 │ │ windows_aarch64_msvc 0.53.0 registry+https://github.com/rust-lang/crates.io-index
    │ ╰─────────────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ windows_aarch64_msvc v0.52.6
      └── windows-targets v0.52.6
          ├── backtrace v0.3.75
          │   └── tokio v1.45.1
          │       ├── combine v4.6.7
          │       │   ├── redis v0.29.5
          │       │   │   └── deadpool-redis v0.20.0
          │       │   │       └── rustycluster v2.2.7
          │       │   └── redis v0.31.0
          │       │       └── rustycluster v2.2.7 (*)
          │       ├── deadpool v0.12.2
          │       │   └── deadpool-redis v0.20.0 (*)
          │       ├── deadpool-runtime v0.1.4
          │       │   └── deadpool v0.12.2 (*)
          │       ├── h2 v0.4.10
          │       │   ├── hyper v1.6.0
          │       │   │   ├── hyper-timeout v0.5.2
          │       │   │   │   └── tonic v0.13.1
          │       │   │   │       └── rustycluster v2.2.7 (*)
          │       │   │   ├── hyper-util v0.1.14
          │       │   │   │   ├── hyper-timeout v0.5.2 (*)
          │       │   │   │   └── tonic v0.13.1 (*)
          │       │   │   └── tonic v0.13.1 (*)
          │       │   └── tonic v0.13.1 (*)
          │       ├── hyper v1.6.0 (*)
          │       ├── hyper-timeout v0.5.2 (*)
          │       ├── hyper-util v0.1.14 (*)
          │       ├── redis v0.29.5 (*)
          │       ├── redis v0.31.0 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       ├── tokio-stream v0.1.17
          │       │   └── tonic v0.13.1 (*)
          │       ├── tokio-util v0.7.15
          │       │   ├── combine v4.6.7 (*)
          │       │   ├── h2 v0.4.10 (*)
          │       │   ├── redis v0.29.5 (*)
          │       │   ├── redis v0.31.0 (*)
          │       │   └── tower v0.5.2
          │       │       ├── axum v0.8.4
          │       │       │   └── tonic v0.13.1 (*)
          │       │       └── tonic v0.13.1 (*)
          │       ├── tonic v0.13.1 (*)
          │       └── tower v0.5.2 (*)
          ├── parking_lot_core v0.9.11
          │   ├── dashmap v6.1.0
          │   │   └── rustycluster v2.2.7 (*)
          │   └── parking_lot v0.12.4
          │       ├── prometheus v0.14.0
          │       │   └── rustycluster v2.2.7 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       └── tokio v1.45.1 (*)
          ├── windows-sys v0.52.0
          │   ├── socket2 v0.5.10
          │   │   ├── hyper-util v0.1.14 (*)
          │   │   ├── redis v0.29.5 (*)
          │   │   ├── redis v0.31.0 (*)
          │   │   ├── tokio v1.45.1 (*)
          │   │   └── tonic v0.13.1 (*)
          │   └── tokio v1.45.1 (*)
          └── windows-sys v0.59.0
              ├── mio v1.0.4
              │   └── tokio v1.45.1 (*)
              ├── rustix v1.0.7
              │   └── tempfile v3.20.0
              │       └── prost-build v0.13.5
              │           └── tonic-build v0.13.1
              │               └── (build) rustycluster v2.2.7 (*)
              └── tempfile v3.20.0 (*)
    ├ windows_aarch64_msvc v0.53.0
      └── windows-targets v0.53.2
          └── windows-sys v0.60.2
              └── errno v0.3.13
                  └── rustix v1.0.7
                      └── tempfile v3.20.0
                          └── prost-build v0.13.5
                              └── tonic-build v0.13.1
                                  └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#signal-hook-registry@1.4.5:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ signal-hook-registry v1.4.5
    └── tokio v1.45.1
        ├── combine v4.6.7
        │   ├── redis v0.29.5
        │   │   └── deadpool-redis v0.20.0
        │   │       └── rustycluster v2.2.7
        │   └── redis v0.31.0
        │       └── rustycluster v2.2.7 (*)
        ├── deadpool v0.12.2
        │   └── deadpool-redis v0.20.0 (*)
        ├── deadpool-runtime v0.1.4
        │   └── deadpool v0.12.2 (*)
        ├── h2 v0.4.10
        │   ├── hyper v1.6.0
        │   │   ├── hyper-timeout v0.5.2
        │   │   │   └── tonic v0.13.1
        │   │   │       └── rustycluster v2.2.7 (*)
        │   │   ├── hyper-util v0.1.14
        │   │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   │   └── tonic v0.13.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── hyper v1.6.0 (*)
        ├── hyper-timeout v0.5.2 (*)
        ├── hyper-util v0.1.14 (*)
        ├── redis v0.29.5 (*)
        ├── redis v0.31.0 (*)
        ├── rustycluster v2.2.7 (*)
        ├── tokio-stream v0.1.17
        │   └── tonic v0.13.1 (*)
        ├── tokio-util v0.7.15
        │   ├── combine v4.6.7 (*)
        │   ├── h2 v0.4.10 (*)
        │   ├── redis v0.29.5 (*)
        │   ├── redis v0.31.0 (*)
        │   └── tower v0.5.2
        │       ├── axum v0.8.4
        │       │   └── tonic v0.13.1 (*)
        │       └── tonic v0.13.1 (*)
        ├── tonic v0.13.1 (*)
        └── tower v0.5.2 (*)

warning[duplicate]: found 2 duplicate entries for crate 'windows_i686_gnu'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:246:1
    │  
246 │ ╭ windows_i686_gnu 0.52.6 registry+https://github.com/rust-lang/crates.io-index
247 │ │ windows_i686_gnu 0.53.0 registry+https://github.com/rust-lang/crates.io-index
    │ ╰─────────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ windows_i686_gnu v0.52.6
      └── windows-targets v0.52.6
          ├── backtrace v0.3.75
          │   └── tokio v1.45.1
          │       ├── combine v4.6.7
          │       │   ├── redis v0.29.5
          │       │   │   └── deadpool-redis v0.20.0
          │       │   │       └── rustycluster v2.2.7
          │       │   └── redis v0.31.0
          │       │       └── rustycluster v2.2.7 (*)
          │       ├── deadpool v0.12.2
          │       │   └── deadpool-redis v0.20.0 (*)
          │       ├── deadpool-runtime v0.1.4
          │       │   └── deadpool v0.12.2 (*)
          │       ├── h2 v0.4.10
          │       │   ├── hyper v1.6.0
          │       │   │   ├── hyper-timeout v0.5.2
          │       │   │   │   └── tonic v0.13.1
          │       │   │   │       └── rustycluster v2.2.7 (*)
          │       │   │   ├── hyper-util v0.1.14
          │       │   │   │   ├── hyper-timeout v0.5.2 (*)
          │       │   │   │   └── tonic v0.13.1 (*)
          │       │   │   └── tonic v0.13.1 (*)
          │       │   └── tonic v0.13.1 (*)
          │       ├── hyper v1.6.0 (*)
          │       ├── hyper-timeout v0.5.2 (*)
          │       ├── hyper-util v0.1.14 (*)
          │       ├── redis v0.29.5 (*)
          │       ├── redis v0.31.0 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       ├── tokio-stream v0.1.17
          │       │   └── tonic v0.13.1 (*)
          │       ├── tokio-util v0.7.15
          │       │   ├── combine v4.6.7 (*)
          │       │   ├── h2 v0.4.10 (*)
          │       │   ├── redis v0.29.5 (*)
          │       │   ├── redis v0.31.0 (*)
          │       │   └── tower v0.5.2
          │       │       ├── axum v0.8.4
          │       │       │   └── tonic v0.13.1 (*)
          │       │       └── tonic v0.13.1 (*)
          │       ├── tonic v0.13.1 (*)
          │       └── tower v0.5.2 (*)
          ├── parking_lot_core v0.9.11
          │   ├── dashmap v6.1.0
          │   │   └── rustycluster v2.2.7 (*)
          │   └── parking_lot v0.12.4
          │       ├── prometheus v0.14.0
          │       │   └── rustycluster v2.2.7 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       └── tokio v1.45.1 (*)
          ├── windows-sys v0.52.0
          │   ├── socket2 v0.5.10
          │   │   ├── hyper-util v0.1.14 (*)
          │   │   ├── redis v0.29.5 (*)
          │   │   ├── redis v0.31.0 (*)
          │   │   ├── tokio v1.45.1 (*)
          │   │   └── tonic v0.13.1 (*)
          │   └── tokio v1.45.1 (*)
          └── windows-sys v0.59.0
              ├── mio v1.0.4
              │   └── tokio v1.45.1 (*)
              ├── rustix v1.0.7
              │   └── tempfile v3.20.0
              │       └── prost-build v0.13.5
              │           └── tonic-build v0.13.1
              │               └── (build) rustycluster v2.2.7 (*)
              └── tempfile v3.20.0 (*)
    ├ windows_i686_gnu v0.53.0
      └── windows-targets v0.53.2
          └── windows-sys v0.60.2
              └── errno v0.3.13
                  └── rustix v1.0.7
                      └── tempfile v3.20.0
                          └── prost-build v0.13.5
                              └── tonic-build v0.13.1
                                  └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#slab@0.4.10:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ slab v0.4.10
    ├── futures-util v0.3.31
    │   ├── axum v0.8.4
    │   │   └── tonic v0.13.1
    │   │       └── rustycluster v2.2.7
    │   ├── futures v0.3.31
    │   │   └── rustycluster v2.2.7 (*)
    │   ├── futures-executor v0.3.31
    │   │   └── futures v0.3.31 (*)
    │   ├── hyper v1.6.0
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper-util v0.1.14
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   ├── redis v0.29.5
    │   │   └── deadpool-redis v0.20.0
    │   │       └── rustycluster v2.2.7 (*)
    │   ├── redis v0.31.0
    │   │   └── rustycluster v2.2.7 (*)
    │   └── tower v0.5.2
    │       ├── axum v0.8.4 (*)
    │       └── tonic v0.13.1 (*)
    ├── h2 v0.4.10
    │   ├── hyper v1.6.0 (*)
    │   └── tonic v0.13.1 (*)
    └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#smallvec@1.15.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ smallvec v1.15.1
    ├── hyper v1.6.0
    │   ├── hyper-timeout v0.5.2
    │   │   └── tonic v0.13.1
    │   │       └── rustycluster v2.2.7
    │   ├── hyper-util v0.1.14
    │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tonic v0.13.1 (*)
    ├── icu_normalizer v2.0.0
    │   └── idna_adapter v1.2.1
    │       └── idna v1.0.3
    │           └── url v2.5.4
    │               ├── redis v0.29.5
    │               │   └── deadpool-redis v0.20.0
    │               │       └── rustycluster v2.2.7 (*)
    │               └── redis v0.31.0
    │                   └── rustycluster v2.2.7 (*)
    ├── idna v1.0.3 (*)
    ├── parking_lot_core v0.9.11
    │   ├── dashmap v6.1.0
    │   │   └── rustycluster v2.2.7 (*)
    │   └── parking_lot v0.12.4
    │       ├── prometheus v0.14.0
    │       │   └── rustycluster v2.2.7 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       └── tokio v1.45.1
    │           ├── combine v4.6.7
    │           │   ├── redis v0.29.5 (*)
    │           │   └── redis v0.31.0 (*)
    │           ├── deadpool v0.12.2
    │           │   └── deadpool-redis v0.20.0 (*)
    │           ├── deadpool-runtime v0.1.4
    │           │   └── deadpool v0.12.2 (*)
    │           ├── h2 v0.4.10
    │           │   ├── hyper v1.6.0 (*)
    │           │   └── tonic v0.13.1 (*)
    │           ├── hyper v1.6.0 (*)
    │           ├── hyper-timeout v0.5.2 (*)
    │           ├── hyper-util v0.1.14 (*)
    │           ├── redis v0.29.5 (*)
    │           ├── redis v0.31.0 (*)
    │           ├── rustycluster v2.2.7 (*)
    │           ├── tokio-stream v0.1.17
    │           │   └── tonic v0.13.1 (*)
    │           ├── tokio-util v0.7.15
    │           │   ├── combine v4.6.7 (*)
    │           │   ├── h2 v0.4.10 (*)
    │           │   ├── redis v0.29.5 (*)
    │           │   ├── redis v0.31.0 (*)
    │           │   └── tower v0.5.2
    │           │       ├── axum v0.8.4
    │           │       │   └── tonic v0.13.1 (*)
    │           │       └── tonic v0.13.1 (*)
    │           ├── tonic v0.13.1 (*)
    │           └── tower v0.5.2 (*)
    └── tracing-subscriber v0.3.19
        ├── rustycluster v2.2.7 (*)
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

warning[duplicate]: found 2 duplicate entries for crate 'windows_i686_gnullvm'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:248:1
    │  
248 │ ╭ windows_i686_gnullvm 0.52.6 registry+https://github.com/rust-lang/crates.io-index
249 │ │ windows_i686_gnullvm 0.53.0 registry+https://github.com/rust-lang/crates.io-index
    │ ╰─────────────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ windows_i686_gnullvm v0.52.6
      └── windows-targets v0.52.6
          ├── backtrace v0.3.75
          │   └── tokio v1.45.1
          │       ├── combine v4.6.7
          │       │   ├── redis v0.29.5
          │       │   │   └── deadpool-redis v0.20.0
          │       │   │       └── rustycluster v2.2.7
          │       │   └── redis v0.31.0
          │       │       └── rustycluster v2.2.7 (*)
          │       ├── deadpool v0.12.2
          │       │   └── deadpool-redis v0.20.0 (*)
          │       ├── deadpool-runtime v0.1.4
          │       │   └── deadpool v0.12.2 (*)
          │       ├── h2 v0.4.10
          │       │   ├── hyper v1.6.0
          │       │   │   ├── hyper-timeout v0.5.2
          │       │   │   │   └── tonic v0.13.1
          │       │   │   │       └── rustycluster v2.2.7 (*)
          │       │   │   ├── hyper-util v0.1.14
          │       │   │   │   ├── hyper-timeout v0.5.2 (*)
          │       │   │   │   └── tonic v0.13.1 (*)
          │       │   │   └── tonic v0.13.1 (*)
          │       │   └── tonic v0.13.1 (*)
          │       ├── hyper v1.6.0 (*)
          │       ├── hyper-timeout v0.5.2 (*)
          │       ├── hyper-util v0.1.14 (*)
          │       ├── redis v0.29.5 (*)
          │       ├── redis v0.31.0 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       ├── tokio-stream v0.1.17
          │       │   └── tonic v0.13.1 (*)
          │       ├── tokio-util v0.7.15
          │       │   ├── combine v4.6.7 (*)
          │       │   ├── h2 v0.4.10 (*)
          │       │   ├── redis v0.29.5 (*)
          │       │   ├── redis v0.31.0 (*)
          │       │   └── tower v0.5.2
          │       │       ├── axum v0.8.4
          │       │       │   └── tonic v0.13.1 (*)
          │       │       └── tonic v0.13.1 (*)
          │       ├── tonic v0.13.1 (*)
          │       └── tower v0.5.2 (*)
          ├── parking_lot_core v0.9.11
          │   ├── dashmap v6.1.0
          │   │   └── rustycluster v2.2.7 (*)
          │   └── parking_lot v0.12.4
          │       ├── prometheus v0.14.0
          │       │   └── rustycluster v2.2.7 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       └── tokio v1.45.1 (*)
          ├── windows-sys v0.52.0
          │   ├── socket2 v0.5.10
          │   │   ├── hyper-util v0.1.14 (*)
          │   │   ├── redis v0.29.5 (*)
          │   │   ├── redis v0.31.0 (*)
          │   │   ├── tokio v1.45.1 (*)
          │   │   └── tonic v0.13.1 (*)
          │   └── tokio v1.45.1 (*)
          └── windows-sys v0.59.0
              ├── mio v1.0.4
              │   └── tokio v1.45.1 (*)
              ├── rustix v1.0.7
              │   └── tempfile v3.20.0
              │       └── prost-build v0.13.5
              │           └── tonic-build v0.13.1
              │               └── (build) rustycluster v2.2.7 (*)
              └── tempfile v3.20.0 (*)
    ├ windows_i686_gnullvm v0.53.0
      └── windows-targets v0.53.2
          └── windows-sys v0.60.2
              └── errno v0.3.13
                  └── rustix v1.0.7
                      └── tempfile v3.20.0
                          └── prost-build v0.13.5
                              └── tonic-build v0.13.1
                                  └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#socket2@0.5.10:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ socket2 v0.5.10
    ├── hyper-util v0.1.14
    │   ├── hyper-timeout v0.5.2
    │   │   └── tonic v0.13.1
    │   │       └── rustycluster v2.2.7
    │   └── tonic v0.13.1 (*)
    ├── redis v0.29.5
    │   └── deadpool-redis v0.20.0
    │       └── rustycluster v2.2.7 (*)
    ├── redis v0.31.0
    │   └── rustycluster v2.2.7 (*)
    ├── tokio v1.45.1
    │   ├── combine v4.6.7
    │   │   ├── redis v0.29.5 (*)
    │   │   └── redis v0.31.0 (*)
    │   ├── deadpool v0.12.2
    │   │   └── deadpool-redis v0.20.0 (*)
    │   ├── deadpool-runtime v0.1.4
    │   │   └── deadpool v0.12.2 (*)
    │   ├── h2 v0.4.10
    │   │   ├── hyper v1.6.0
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   ├── hyper-util v0.1.14 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── hyper v1.6.0 (*)
    │   ├── hyper-timeout v0.5.2 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   ├── redis v0.29.5 (*)
    │   ├── redis v0.31.0 (*)
    │   ├── rustycluster v2.2.7 (*)
    │   ├── tokio-stream v0.1.17
    │   │   └── tonic v0.13.1 (*)
    │   ├── tokio-util v0.7.15
    │   │   ├── combine v4.6.7 (*)
    │   │   ├── h2 v0.4.10 (*)
    │   │   ├── redis v0.29.5 (*)
    │   │   ├── redis v0.31.0 (*)
    │   │   └── tower v0.5.2
    │   │       ├── axum v0.8.4
    │   │       │   └── tonic v0.13.1 (*)
    │   │       └── tonic v0.13.1 (*)
    │   ├── tonic v0.13.1 (*)
    │   └── tower v0.5.2 (*)
    └── tonic v0.13.1 (*)

warning[duplicate]: found 2 duplicate entries for crate 'windows_i686_msvc'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:250:1
    │  
250 │ ╭ windows_i686_msvc 0.52.6 registry+https://github.com/rust-lang/crates.io-index
251 │ │ windows_i686_msvc 0.53.0 registry+https://github.com/rust-lang/crates.io-index
    │ ╰──────────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ windows_i686_msvc v0.52.6
      └── windows-targets v0.52.6
          ├── backtrace v0.3.75
          │   └── tokio v1.45.1
          │       ├── combine v4.6.7
          │       │   ├── redis v0.29.5
          │       │   │   └── deadpool-redis v0.20.0
          │       │   │       └── rustycluster v2.2.7
          │       │   └── redis v0.31.0
          │       │       └── rustycluster v2.2.7 (*)
          │       ├── deadpool v0.12.2
          │       │   └── deadpool-redis v0.20.0 (*)
          │       ├── deadpool-runtime v0.1.4
          │       │   └── deadpool v0.12.2 (*)
          │       ├── h2 v0.4.10
          │       │   ├── hyper v1.6.0
          │       │   │   ├── hyper-timeout v0.5.2
          │       │   │   │   └── tonic v0.13.1
          │       │   │   │       └── rustycluster v2.2.7 (*)
          │       │   │   ├── hyper-util v0.1.14
          │       │   │   │   ├── hyper-timeout v0.5.2 (*)
          │       │   │   │   └── tonic v0.13.1 (*)
          │       │   │   └── tonic v0.13.1 (*)
          │       │   └── tonic v0.13.1 (*)
          │       ├── hyper v1.6.0 (*)
          │       ├── hyper-timeout v0.5.2 (*)
          │       ├── hyper-util v0.1.14 (*)
          │       ├── redis v0.29.5 (*)
          │       ├── redis v0.31.0 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       ├── tokio-stream v0.1.17
          │       │   └── tonic v0.13.1 (*)
          │       ├── tokio-util v0.7.15
          │       │   ├── combine v4.6.7 (*)
          │       │   ├── h2 v0.4.10 (*)
          │       │   ├── redis v0.29.5 (*)
          │       │   ├── redis v0.31.0 (*)
          │       │   └── tower v0.5.2
          │       │       ├── axum v0.8.4
          │       │       │   └── tonic v0.13.1 (*)
          │       │       └── tonic v0.13.1 (*)
          │       ├── tonic v0.13.1 (*)
          │       └── tower v0.5.2 (*)
          ├── parking_lot_core v0.9.11
          │   ├── dashmap v6.1.0
          │   │   └── rustycluster v2.2.7 (*)
          │   └── parking_lot v0.12.4
          │       ├── prometheus v0.14.0
          │       │   └── rustycluster v2.2.7 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       └── tokio v1.45.1 (*)
          ├── windows-sys v0.52.0
          │   ├── socket2 v0.5.10
          │   │   ├── hyper-util v0.1.14 (*)
          │   │   ├── redis v0.29.5 (*)
          │   │   ├── redis v0.31.0 (*)
          │   │   ├── tokio v1.45.1 (*)
          │   │   └── tonic v0.13.1 (*)
          │   └── tokio v1.45.1 (*)
          └── windows-sys v0.59.0
              ├── mio v1.0.4
              │   └── tokio v1.45.1 (*)
              ├── rustix v1.0.7
              │   └── tempfile v3.20.0
              │       └── prost-build v0.13.5
              │           └── tonic-build v0.13.1
              │               └── (build) rustycluster v2.2.7 (*)
              └── tempfile v3.20.0 (*)
    ├ windows_i686_msvc v0.53.0
      └── windows-targets v0.53.2
          └── windows-sys v0.60.2
              └── errno v0.3.13
                  └── rustix v1.0.7
                      └── tempfile v3.20.0
                          └── prost-build v0.13.5
                              └── tonic-build v0.13.1
                                  └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#stable_deref_trait@1.2.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ stable_deref_trait v1.2.0
    ├── icu_provider v2.0.0
    │   ├── icu_normalizer v2.0.0
    │   │   └── idna_adapter v1.2.1
    │   │       └── idna v1.0.3
    │   │           └── url v2.5.4
    │   │               ├── redis v0.29.5
    │   │               │   └── deadpool-redis v0.20.0
    │   │               │       └── rustycluster v2.2.7
    │   │               └── redis v0.31.0
    │   │                   └── rustycluster v2.2.7 (*)
    │   └── icu_properties v2.0.1
    │       └── idna_adapter v1.2.1 (*)
    └── yoke v0.8.0
        ├── icu_collections v2.0.0
        │   ├── icu_normalizer v2.0.0 (*)
        │   └── icu_properties v2.0.1 (*)
        ├── icu_provider v2.0.0 (*)
        ├── zerotrie v0.2.2
        │   ├── icu_properties v2.0.1 (*)
        │   └── icu_provider v2.0.0 (*)
        └── zerovec v0.11.2
            ├── icu_collections v2.0.0 (*)
            ├── icu_locale_core v2.0.0
            │   ├── icu_properties v2.0.1 (*)
            │   └── icu_provider v2.0.0 (*)
            ├── icu_normalizer v2.0.0 (*)
            ├── icu_properties v2.0.1 (*)
            ├── icu_provider v2.0.0 (*)
            ├── potential_utf v0.1.2
            │   ├── icu_collections v2.0.0 (*)
            │   └── icu_properties v2.0.1 (*)
            └── tinystr v0.8.1
                ├── icu_locale_core v2.0.0 (*)
                └── icu_provider v2.0.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#subtle@2.6.1:4:12
  │
4 │ license = "BSD-3-Clause"
  │            ━━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ BSD-3-Clause - BSD 3-Clause "New" or "Revised" License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ subtle v2.6.1
    ├── aes-gcm v0.10.3
    │   └── rustycluster v2.2.7
    ├── digest v0.10.7
    │   ├── hmac v0.12.1
    │   │   └── pbkdf2 v0.12.2
    │   │       └── rustycluster v2.2.7 (*)
    │   ├── pbkdf2 v0.12.2 (*)
    │   └── sha2 v0.10.9
    │       ├── (build) pest_meta v2.8.1
    │       │   └── pest_generator v2.8.1
    │       │       └── pest_derive v2.8.1
    │       │           └── json5 v0.4.1
    │       │               └── config v0.13.4
    │       │                   └── rustycluster v2.2.7 (*)
    │       └── rustycluster v2.2.7 (*)
    └── universal-hash v0.5.1
        └── polyval v0.6.2
            └── ghash v0.5.1
                └── aes-gcm v0.10.3 (*)

warning[duplicate]: found 2 duplicate entries for crate 'windows_x86_64_gnu'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:252:1
    │  
252 │ ╭ windows_x86_64_gnu 0.52.6 registry+https://github.com/rust-lang/crates.io-index
253 │ │ windows_x86_64_gnu 0.53.0 registry+https://github.com/rust-lang/crates.io-index
    │ ╰───────────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ windows_x86_64_gnu v0.52.6
      └── windows-targets v0.52.6
          ├── backtrace v0.3.75
          │   └── tokio v1.45.1
          │       ├── combine v4.6.7
          │       │   ├── redis v0.29.5
          │       │   │   └── deadpool-redis v0.20.0
          │       │   │       └── rustycluster v2.2.7
          │       │   └── redis v0.31.0
          │       │       └── rustycluster v2.2.7 (*)
          │       ├── deadpool v0.12.2
          │       │   └── deadpool-redis v0.20.0 (*)
          │       ├── deadpool-runtime v0.1.4
          │       │   └── deadpool v0.12.2 (*)
          │       ├── h2 v0.4.10
          │       │   ├── hyper v1.6.0
          │       │   │   ├── hyper-timeout v0.5.2
          │       │   │   │   └── tonic v0.13.1
          │       │   │   │       └── rustycluster v2.2.7 (*)
          │       │   │   ├── hyper-util v0.1.14
          │       │   │   │   ├── hyper-timeout v0.5.2 (*)
          │       │   │   │   └── tonic v0.13.1 (*)
          │       │   │   └── tonic v0.13.1 (*)
          │       │   └── tonic v0.13.1 (*)
          │       ├── hyper v1.6.0 (*)
          │       ├── hyper-timeout v0.5.2 (*)
          │       ├── hyper-util v0.1.14 (*)
          │       ├── redis v0.29.5 (*)
          │       ├── redis v0.31.0 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       ├── tokio-stream v0.1.17
          │       │   └── tonic v0.13.1 (*)
          │       ├── tokio-util v0.7.15
          │       │   ├── combine v4.6.7 (*)
          │       │   ├── h2 v0.4.10 (*)
          │       │   ├── redis v0.29.5 (*)
          │       │   ├── redis v0.31.0 (*)
          │       │   └── tower v0.5.2
          │       │       ├── axum v0.8.4
          │       │       │   └── tonic v0.13.1 (*)
          │       │       └── tonic v0.13.1 (*)
          │       ├── tonic v0.13.1 (*)
          │       └── tower v0.5.2 (*)
          ├── parking_lot_core v0.9.11
          │   ├── dashmap v6.1.0
          │   │   └── rustycluster v2.2.7 (*)
          │   └── parking_lot v0.12.4
          │       ├── prometheus v0.14.0
          │       │   └── rustycluster v2.2.7 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       └── tokio v1.45.1 (*)
          ├── windows-sys v0.52.0
          │   ├── socket2 v0.5.10
          │   │   ├── hyper-util v0.1.14 (*)
          │   │   ├── redis v0.29.5 (*)
          │   │   ├── redis v0.31.0 (*)
          │   │   ├── tokio v1.45.1 (*)
          │   │   └── tonic v0.13.1 (*)
          │   └── tokio v1.45.1 (*)
          └── windows-sys v0.59.0
              ├── mio v1.0.4
              │   └── tokio v1.45.1 (*)
              ├── rustix v1.0.7
              │   └── tempfile v3.20.0
              │       └── prost-build v0.13.5
              │           └── tonic-build v0.13.1
              │               └── (build) rustycluster v2.2.7 (*)
              └── tempfile v3.20.0 (*)
    ├ windows_x86_64_gnu v0.53.0
      └── windows-targets v0.53.2
          └── windows-sys v0.60.2
              └── errno v0.3.13
                  └── rustix v1.0.7
                      └── tempfile v3.20.0
                          └── prost-build v0.13.5
                              └── tonic-build v0.13.1
                                  └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#syn@2.0.104:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ syn v2.0.104
    ├── async-trait v0.1.88
    │   ├── config v0.13.4
    │   │   └── rustycluster v2.2.7
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7 (*)
    ├── displaydoc v0.2.5
    │   ├── icu_collections v2.0.0
    │   │   ├── icu_normalizer v2.0.0
    │   │   │   └── idna_adapter v1.2.1
    │   │   │       └── idna v1.0.3
    │   │   │           └── url v2.5.4
    │   │   │               ├── redis v0.29.5
    │   │   │               │   └── deadpool-redis v0.20.0
    │   │   │               │       └── rustycluster v2.2.7 (*)
    │   │   │               └── redis v0.31.0
    │   │   │                   └── rustycluster v2.2.7 (*)
    │   │   └── icu_properties v2.0.1
    │   │       └── idna_adapter v1.2.1 (*)
    │   ├── icu_locale_core v2.0.0
    │   │   ├── icu_properties v2.0.1 (*)
    │   │   └── icu_provider v2.0.0
    │   │       ├── icu_normalizer v2.0.0 (*)
    │   │       └── icu_properties v2.0.1 (*)
    │   ├── icu_normalizer v2.0.0 (*)
    │   ├── icu_properties v2.0.1 (*)
    │   ├── icu_provider v2.0.0 (*)
    │   ├── tinystr v0.8.1
    │   │   ├── icu_locale_core v2.0.0 (*)
    │   │   └── icu_provider v2.0.0 (*)
    │   └── zerotrie v0.2.2
    │       ├── icu_properties v2.0.1 (*)
    │       └── icu_provider v2.0.0 (*)
    ├── futures-macro v0.3.31
    │   └── futures-util v0.3.31
    │       ├── axum v0.8.4
    │       │   └── tonic v0.13.1 (*)
    │       ├── futures v0.3.31
    │       │   └── rustycluster v2.2.7 (*)
    │       ├── futures-executor v0.3.31
    │       │   └── futures v0.3.31 (*)
    │       ├── hyper v1.6.0
    │       │   ├── hyper-timeout v0.5.2
    │       │   │   └── tonic v0.13.1 (*)
    │       │   ├── hyper-util v0.1.14
    │       │   │   ├── hyper-timeout v0.5.2 (*)
    │       │   │   └── tonic v0.13.1 (*)
    │       │   └── tonic v0.13.1 (*)
    │       ├── hyper-util v0.1.14 (*)
    │       ├── redis v0.29.5 (*)
    │       ├── redis v0.31.0 (*)
    │       └── tower v0.5.2
    │           ├── axum v0.8.4 (*)
    │           └── tonic v0.13.1 (*)
    ├── pest_generator v2.8.1
    │   └── pest_derive v2.8.1
    │       └── json5 v0.4.1
    │           └── config v0.13.4 (*)
    ├── pin-project-internal v1.1.10
    │   └── pin-project v1.1.10
    │       └── tonic v0.13.1 (*)
    ├── prettyplease v0.2.35
    │   ├── prost-build v0.13.5
    │   │   └── tonic-build v0.13.1
    │   │       └── (build) rustycluster v2.2.7 (*)
    │   └── tonic-build v0.13.1 (*)
    ├── prost-build v0.13.5 (*)
    ├── prost-derive v0.13.5
    │   └── prost v0.13.5
    │       ├── prost-build v0.13.5 (*)
    │       ├── prost-types v0.13.5
    │       │   ├── prost-build v0.13.5 (*)
    │       │   ├── rustycluster v2.2.7 (*)
    │       │   └── tonic-build v0.13.1 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       └── tonic v0.13.1 (*)
    ├── serde_derive v1.0.219
    │   └── serde v1.0.219
    │       ├── axum v0.8.4 (*)
    │       ├── config v0.13.4 (*)
    │       ├── json5 v0.4.1 (*)
    │       ├── ron v0.7.1
    │       │   └── config v0.13.4 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       ├── serde_json v1.0.140
    │       │   ├── config v0.13.4 (*)
    │       │   └── tracing-subscriber v0.3.19
    │       │       ├── rustycluster v2.2.7 (*)
    │       │       └── tracing-appender v0.2.3
    │       │           └── rustycluster v2.2.7 (*)
    │       ├── toml v0.5.11
    │       │   └── config v0.13.4 (*)
    │       ├── tracing-serde v0.2.0
    │       │   └── tracing-subscriber v0.3.19 (*)
    │       └── tracing-subscriber v0.3.19 (*)
    ├── synstructure v0.13.2
    │   ├── yoke-derive v0.8.0
    │   │   └── yoke v0.8.0
    │   │       ├── icu_collections v2.0.0 (*)
    │   │       ├── icu_provider v2.0.0 (*)
    │   │       ├── zerotrie v0.2.2 (*)
    │   │       └── zerovec v0.11.2
    │   │           ├── icu_collections v2.0.0 (*)
    │   │           ├── icu_locale_core v2.0.0 (*)
    │   │           ├── icu_normalizer v2.0.0 (*)
    │   │           ├── icu_properties v2.0.1 (*)
    │   │           ├── icu_provider v2.0.0 (*)
    │   │           ├── potential_utf v0.1.2
    │   │           │   ├── icu_collections v2.0.0 (*)
    │   │           │   └── icu_properties v2.0.1 (*)
    │   │           └── tinystr v0.8.1 (*)
    │   └── zerofrom-derive v0.1.6
    │       └── zerofrom v0.1.6
    │           ├── icu_collections v2.0.0 (*)
    │           ├── icu_provider v2.0.0 (*)
    │           ├── yoke v0.8.0 (*)
    │           ├── zerotrie v0.2.2 (*)
    │           └── zerovec v0.11.2 (*)
    ├── thiserror-impl v1.0.69
    │   └── thiserror v1.0.69
    │       ├── protobuf v3.7.2
    │       │   └── prometheus v0.14.0
    │       │       └── rustycluster v2.2.7 (*)
    │       ├── protobuf-support v3.7.2
    │       │   └── protobuf v3.7.2 (*)
    │       └── tracing-appender v0.2.3 (*)
    ├── thiserror-impl v2.0.12
    │   └── thiserror v2.0.12
    │       ├── pest v2.8.1
    │       │   ├── json5 v0.4.1 (*)
    │       │   ├── pest_derive v2.8.1 (*)
    │       │   ├── pest_generator v2.8.1 (*)
    │       │   └── pest_meta v2.8.1
    │       │       └── pest_generator v2.8.1 (*)
    │       └── prometheus v0.14.0 (*)
    ├── tokio-macros v2.5.0
    │   └── tokio v1.45.1
    │       ├── combine v4.6.7
    │       │   ├── redis v0.29.5 (*)
    │       │   └── redis v0.31.0 (*)
    │       ├── deadpool v0.12.2
    │       │   └── deadpool-redis v0.20.0 (*)
    │       ├── deadpool-runtime v0.1.4
    │       │   └── deadpool v0.12.2 (*)
    │       ├── h2 v0.4.10
    │       │   ├── hyper v1.6.0 (*)
    │       │   └── tonic v0.13.1 (*)
    │       ├── hyper v1.6.0 (*)
    │       ├── hyper-timeout v0.5.2 (*)
    │       ├── hyper-util v0.1.14 (*)
    │       ├── redis v0.29.5 (*)
    │       ├── redis v0.31.0 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       ├── tokio-stream v0.1.17
    │       │   └── tonic v0.13.1 (*)
    │       ├── tokio-util v0.7.15
    │       │   ├── combine v4.6.7 (*)
    │       │   ├── h2 v0.4.10 (*)
    │       │   ├── redis v0.29.5 (*)
    │       │   ├── redis v0.31.0 (*)
    │       │   └── tower v0.5.2 (*)
    │       ├── tonic v0.13.1 (*)
    │       └── tower v0.5.2 (*)
    ├── tonic-build v0.13.1 (*)
    ├── tracing-attributes v0.1.30
    │   └── tracing v0.1.41
    │       ├── h2 v0.4.10 (*)
    │       ├── hyper-util v0.1.14 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       ├── tokio v1.45.1 (*)
    │       ├── tonic v0.13.1 (*)
    │       ├── tower v0.5.2 (*)
    │       └── tracing-subscriber v0.3.19 (*)
    ├── wasm-bindgen-backend v0.2.100
    │   └── wasm-bindgen-macro-support v0.2.100
    │       └── wasm-bindgen-macro v0.2.100
    │           └── wasm-bindgen v0.2.100
    │               ├── iana-time-zone v0.1.63
    │               │   └── chrono v0.4.41
    │               │       └── tracing-subscriber v0.3.19 (*)
    │               └── js-sys v0.3.77
    │                   └── iana-time-zone v0.1.63 (*)
    ├── wasm-bindgen-macro-support v0.2.100 (*)
    ├── windows-implement v0.60.0
    │   └── windows-core v0.61.2
    │       └── iana-time-zone v0.1.63 (*)
    ├── windows-interface v0.59.1
    │   └── windows-core v0.61.2 (*)
    ├── yoke-derive v0.8.0 (*)
    ├── zerofrom-derive v0.1.6 (*)
    └── zerovec-derive v0.11.1
        └── zerovec v0.11.2 (*)

warning[duplicate]: found 2 duplicate entries for crate 'windows_x86_64_gnullvm'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:254:1
    │  
254 │ ╭ windows_x86_64_gnullvm 0.52.6 registry+https://github.com/rust-lang/crates.io-index
255 │ │ windows_x86_64_gnullvm 0.53.0 registry+https://github.com/rust-lang/crates.io-index
    │ ╰───────────────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ windows_x86_64_gnullvm v0.52.6
      └── windows-targets v0.52.6
          ├── backtrace v0.3.75
          │   └── tokio v1.45.1
          │       ├── combine v4.6.7
          │       │   ├── redis v0.29.5
          │       │   │   └── deadpool-redis v0.20.0
          │       │   │       └── rustycluster v2.2.7
          │       │   └── redis v0.31.0
          │       │       └── rustycluster v2.2.7 (*)
          │       ├── deadpool v0.12.2
          │       │   └── deadpool-redis v0.20.0 (*)
          │       ├── deadpool-runtime v0.1.4
          │       │   └── deadpool v0.12.2 (*)
          │       ├── h2 v0.4.10
          │       │   ├── hyper v1.6.0
          │       │   │   ├── hyper-timeout v0.5.2
          │       │   │   │   └── tonic v0.13.1
          │       │   │   │       └── rustycluster v2.2.7 (*)
          │       │   │   ├── hyper-util v0.1.14
          │       │   │   │   ├── hyper-timeout v0.5.2 (*)
          │       │   │   │   └── tonic v0.13.1 (*)
          │       │   │   └── tonic v0.13.1 (*)
          │       │   └── tonic v0.13.1 (*)
          │       ├── hyper v1.6.0 (*)
          │       ├── hyper-timeout v0.5.2 (*)
          │       ├── hyper-util v0.1.14 (*)
          │       ├── redis v0.29.5 (*)
          │       ├── redis v0.31.0 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       ├── tokio-stream v0.1.17
          │       │   └── tonic v0.13.1 (*)
          │       ├── tokio-util v0.7.15
          │       │   ├── combine v4.6.7 (*)
          │       │   ├── h2 v0.4.10 (*)
          │       │   ├── redis v0.29.5 (*)
          │       │   ├── redis v0.31.0 (*)
          │       │   └── tower v0.5.2
          │       │       ├── axum v0.8.4
          │       │       │   └── tonic v0.13.1 (*)
          │       │       └── tonic v0.13.1 (*)
          │       ├── tonic v0.13.1 (*)
          │       └── tower v0.5.2 (*)
          ├── parking_lot_core v0.9.11
          │   ├── dashmap v6.1.0
          │   │   └── rustycluster v2.2.7 (*)
          │   └── parking_lot v0.12.4
          │       ├── prometheus v0.14.0
          │       │   └── rustycluster v2.2.7 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       └── tokio v1.45.1 (*)
          ├── windows-sys v0.52.0
          │   ├── socket2 v0.5.10
          │   │   ├── hyper-util v0.1.14 (*)
          │   │   ├── redis v0.29.5 (*)
          │   │   ├── redis v0.31.0 (*)
          │   │   ├── tokio v1.45.1 (*)
          │   │   └── tonic v0.13.1 (*)
          │   └── tokio v1.45.1 (*)
          └── windows-sys v0.59.0
              ├── mio v1.0.4
              │   └── tokio v1.45.1 (*)
              ├── rustix v1.0.7
              │   └── tempfile v3.20.0
              │       └── prost-build v0.13.5
              │           └── tonic-build v0.13.1
              │               └── (build) rustycluster v2.2.7 (*)
              └── tempfile v3.20.0 (*)
    ├ windows_x86_64_gnullvm v0.53.0
      └── windows-targets v0.53.2
          └── windows-sys v0.60.2
              └── errno v0.3.13
                  └── rustix v1.0.7
                      └── tempfile v3.20.0
                          └── prost-build v0.13.5
                              └── tonic-build v0.13.1
                                  └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#sync_wrapper@1.0.2:4:12
  │
4 │ license = "Apache-2.0"
  │            ━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ sync_wrapper v1.0.2
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── axum-core v0.5.2
    │   └── axum v0.8.4 (*)
    └── tower v0.5.2
        ├── axum v0.8.4 (*)
        └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#synstructure@0.13.2:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ synstructure v0.13.2
    ├── yoke-derive v0.8.0
    │   └── yoke v0.8.0
    │       ├── icu_collections v2.0.0
    │       │   ├── icu_normalizer v2.0.0
    │       │   │   └── idna_adapter v1.2.1
    │       │   │       └── idna v1.0.3
    │       │   │           └── url v2.5.4
    │       │   │               ├── redis v0.29.5
    │       │   │               │   └── deadpool-redis v0.20.0
    │       │   │               │       └── rustycluster v2.2.7
    │       │   │               └── redis v0.31.0
    │       │   │                   └── rustycluster v2.2.7 (*)
    │       │   └── icu_properties v2.0.1
    │       │       └── idna_adapter v1.2.1 (*)
    │       ├── icu_provider v2.0.0
    │       │   ├── icu_normalizer v2.0.0 (*)
    │       │   └── icu_properties v2.0.1 (*)
    │       ├── zerotrie v0.2.2
    │       │   ├── icu_properties v2.0.1 (*)
    │       │   └── icu_provider v2.0.0 (*)
    │       └── zerovec v0.11.2
    │           ├── icu_collections v2.0.0 (*)
    │           ├── icu_locale_core v2.0.0
    │           │   ├── icu_properties v2.0.1 (*)
    │           │   └── icu_provider v2.0.0 (*)
    │           ├── icu_normalizer v2.0.0 (*)
    │           ├── icu_properties v2.0.1 (*)
    │           ├── icu_provider v2.0.0 (*)
    │           ├── potential_utf v0.1.2
    │           │   ├── icu_collections v2.0.0 (*)
    │           │   └── icu_properties v2.0.1 (*)
    │           └── tinystr v0.8.1
    │               ├── icu_locale_core v2.0.0 (*)
    │               └── icu_provider v2.0.0 (*)
    └── zerofrom-derive v0.1.6
        └── zerofrom v0.1.6
            ├── icu_collections v2.0.0 (*)
            ├── icu_provider v2.0.0 (*)
            ├── yoke v0.8.0 (*)
            ├── zerotrie v0.2.2 (*)
            └── zerovec v0.11.2 (*)

warning[duplicate]: found 2 duplicate entries for crate 'windows_x86_64_msvc'
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:256:1
    │  
256 │ ╭ windows_x86_64_msvc 0.52.6 registry+https://github.com/rust-lang/crates.io-index
257 │ │ windows_x86_64_msvc 0.53.0 registry+https://github.com/rust-lang/crates.io-index
    │ ╰────────────────────────────────────────────────────────────────────────────────┘ lock entries
    │  
    ├ windows_x86_64_msvc v0.52.6
      └── windows-targets v0.52.6
          ├── backtrace v0.3.75
          │   └── tokio v1.45.1
          │       ├── combine v4.6.7
          │       │   ├── redis v0.29.5
          │       │   │   └── deadpool-redis v0.20.0
          │       │   │       └── rustycluster v2.2.7
          │       │   └── redis v0.31.0
          │       │       └── rustycluster v2.2.7 (*)
          │       ├── deadpool v0.12.2
          │       │   └── deadpool-redis v0.20.0 (*)
          │       ├── deadpool-runtime v0.1.4
          │       │   └── deadpool v0.12.2 (*)
          │       ├── h2 v0.4.10
          │       │   ├── hyper v1.6.0
          │       │   │   ├── hyper-timeout v0.5.2
          │       │   │   │   └── tonic v0.13.1
          │       │   │   │       └── rustycluster v2.2.7 (*)
          │       │   │   ├── hyper-util v0.1.14
          │       │   │   │   ├── hyper-timeout v0.5.2 (*)
          │       │   │   │   └── tonic v0.13.1 (*)
          │       │   │   └── tonic v0.13.1 (*)
          │       │   └── tonic v0.13.1 (*)
          │       ├── hyper v1.6.0 (*)
          │       ├── hyper-timeout v0.5.2 (*)
          │       ├── hyper-util v0.1.14 (*)
          │       ├── redis v0.29.5 (*)
          │       ├── redis v0.31.0 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       ├── tokio-stream v0.1.17
          │       │   └── tonic v0.13.1 (*)
          │       ├── tokio-util v0.7.15
          │       │   ├── combine v4.6.7 (*)
          │       │   ├── h2 v0.4.10 (*)
          │       │   ├── redis v0.29.5 (*)
          │       │   ├── redis v0.31.0 (*)
          │       │   └── tower v0.5.2
          │       │       ├── axum v0.8.4
          │       │       │   └── tonic v0.13.1 (*)
          │       │       └── tonic v0.13.1 (*)
          │       ├── tonic v0.13.1 (*)
          │       └── tower v0.5.2 (*)
          ├── parking_lot_core v0.9.11
          │   ├── dashmap v6.1.0
          │   │   └── rustycluster v2.2.7 (*)
          │   └── parking_lot v0.12.4
          │       ├── prometheus v0.14.0
          │       │   └── rustycluster v2.2.7 (*)
          │       ├── rustycluster v2.2.7 (*)
          │       └── tokio v1.45.1 (*)
          ├── windows-sys v0.52.0
          │   ├── socket2 v0.5.10
          │   │   ├── hyper-util v0.1.14 (*)
          │   │   ├── redis v0.29.5 (*)
          │   │   ├── redis v0.31.0 (*)
          │   │   ├── tokio v1.45.1 (*)
          │   │   └── tonic v0.13.1 (*)
          │   └── tokio v1.45.1 (*)
          └── windows-sys v0.59.0
              ├── mio v1.0.4
              │   └── tokio v1.45.1 (*)
              ├── rustix v1.0.7
              │   └── tempfile v3.20.0
              │       └── prost-build v0.13.5
              │           └── tonic-build v0.13.1
              │               └── (build) rustycluster v2.2.7 (*)
              └── tempfile v3.20.0 (*)
    ├ windows_x86_64_msvc v0.53.0
      └── windows-targets v0.53.2
          └── windows-sys v0.60.2
              └── errno v0.3.13
                  └── rustix v1.0.7
                      └── tempfile v3.20.0
                          └── prost-build v0.13.5
                              └── tonic-build v0.13.1
                                  └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tempfile@3.20.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tempfile v3.20.0
    └── prost-build v0.13.5
        └── tonic-build v0.13.1
            └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#thiserror@1.0.69:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ thiserror v1.0.69
    ├── protobuf v3.7.2
    │   └── prometheus v0.14.0
    │       └── rustycluster v2.2.7
    ├── protobuf-support v3.7.2
    │   └── protobuf v3.7.2 (*)
    └── tracing-appender v0.2.3
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#thiserror@2.0.12:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ thiserror v2.0.12
    ├── pest v2.8.1
    │   ├── json5 v0.4.1
    │   │   └── config v0.13.4
    │   │       └── rustycluster v2.2.7
    │   ├── pest_derive v2.8.1
    │   │   └── json5 v0.4.1 (*)
    │   ├── pest_generator v2.8.1
    │   │   └── pest_derive v2.8.1 (*)
    │   └── pest_meta v2.8.1
    │       └── pest_generator v2.8.1 (*)
    └── prometheus v0.14.0
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#thiserror-impl@1.0.69:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ thiserror-impl v1.0.69
    └── thiserror v1.0.69
        ├── protobuf v3.7.2
        │   └── prometheus v0.14.0
        │       └── rustycluster v2.2.7
        ├── protobuf-support v3.7.2
        │   └── protobuf v3.7.2 (*)
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#thiserror-impl@2.0.12:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ thiserror-impl v2.0.12
    └── thiserror v2.0.12
        ├── pest v2.8.1
        │   ├── json5 v0.4.1
        │   │   └── config v0.13.4
        │   │       └── rustycluster v2.2.7
        │   ├── pest_derive v2.8.1
        │   │   └── json5 v0.4.1 (*)
        │   ├── pest_generator v2.8.1
        │   │   └── pest_derive v2.8.1 (*)
        │   └── pest_meta v2.8.1
        │       └── pest_generator v2.8.1 (*)
        └── prometheus v0.14.0
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#thread_local@1.1.9:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ thread_local v1.1.9
    └── tracing-subscriber v0.3.19
        ├── rustycluster v2.2.7
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#time@0.3.41:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ time v0.3.41
    └── tracing-appender v0.2.3
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#time-core@0.1.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ time-core v0.1.4
    └── time v0.3.41
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tinystr@0.8.1:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ tinystr v0.8.1
    ├── icu_locale_core v2.0.0
    │   ├── icu_properties v2.0.1
    │   │   └── idna_adapter v1.2.1
    │   │       └── idna v1.0.3
    │   │           └── url v2.5.4
    │   │               ├── redis v0.29.5
    │   │               │   └── deadpool-redis v0.20.0
    │   │               │       └── rustycluster v2.2.7
    │   │               └── redis v0.31.0
    │   │                   └── rustycluster v2.2.7 (*)
    │   └── icu_provider v2.0.0
    │       ├── icu_normalizer v2.0.0
    │       │   └── idna_adapter v1.2.1 (*)
    │       └── icu_properties v2.0.1 (*)
    └── icu_provider v2.0.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tokio@1.45.1:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tokio v1.45.1
    ├── combine v4.6.7
    │   ├── redis v0.29.5
    │   │   └── deadpool-redis v0.20.0
    │   │       └── rustycluster v2.2.7
    │   └── redis v0.31.0
    │       └── rustycluster v2.2.7 (*)
    ├── deadpool v0.12.2
    │   └── deadpool-redis v0.20.0 (*)
    ├── deadpool-runtime v0.1.4
    │   └── deadpool v0.12.2 (*)
    ├── h2 v0.4.10
    │   ├── hyper v1.6.0
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1
    │   │   │       └── rustycluster v2.2.7 (*)
    │   │   ├── hyper-util v0.1.14
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tonic v0.13.1 (*)
    ├── hyper v1.6.0 (*)
    ├── hyper-timeout v0.5.2 (*)
    ├── hyper-util v0.1.14 (*)
    ├── redis v0.29.5 (*)
    ├── redis v0.31.0 (*)
    ├── rustycluster v2.2.7 (*)
    ├── tokio-stream v0.1.17
    │   └── tonic v0.13.1 (*)
    ├── tokio-util v0.7.15
    │   ├── combine v4.6.7 (*)
    │   ├── h2 v0.4.10 (*)
    │   ├── redis v0.29.5 (*)
    │   ├── redis v0.31.0 (*)
    │   └── tower v0.5.2
    │       ├── axum v0.8.4
    │       │   └── tonic v0.13.1 (*)
    │       └── tonic v0.13.1 (*)
    ├── tonic v0.13.1 (*)
    └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tokio-macros@2.5.0:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tokio-macros v2.5.0
    └── tokio v1.45.1
        ├── combine v4.6.7
        │   ├── redis v0.29.5
        │   │   └── deadpool-redis v0.20.0
        │   │       └── rustycluster v2.2.7
        │   └── redis v0.31.0
        │       └── rustycluster v2.2.7 (*)
        ├── deadpool v0.12.2
        │   └── deadpool-redis v0.20.0 (*)
        ├── deadpool-runtime v0.1.4
        │   └── deadpool v0.12.2 (*)
        ├── h2 v0.4.10
        │   ├── hyper v1.6.0
        │   │   ├── hyper-timeout v0.5.2
        │   │   │   └── tonic v0.13.1
        │   │   │       └── rustycluster v2.2.7 (*)
        │   │   ├── hyper-util v0.1.14
        │   │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   │   └── tonic v0.13.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── hyper v1.6.0 (*)
        ├── hyper-timeout v0.5.2 (*)
        ├── hyper-util v0.1.14 (*)
        ├── redis v0.29.5 (*)
        ├── redis v0.31.0 (*)
        ├── rustycluster v2.2.7 (*)
        ├── tokio-stream v0.1.17
        │   └── tonic v0.13.1 (*)
        ├── tokio-util v0.7.15
        │   ├── combine v4.6.7 (*)
        │   ├── h2 v0.4.10 (*)
        │   ├── redis v0.29.5 (*)
        │   ├── redis v0.31.0 (*)
        │   └── tower v0.5.2
        │       ├── axum v0.8.4
        │       │   └── tonic v0.13.1 (*)
        │       └── tonic v0.13.1 (*)
        ├── tonic v0.13.1 (*)
        └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tokio-stream@0.1.17:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tokio-stream v0.1.17
    └── tonic v0.13.1
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tokio-util@0.7.15:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tokio-util v0.7.15
    ├── combine v4.6.7
    │   ├── redis v0.29.5
    │   │   └── deadpool-redis v0.20.0
    │   │       └── rustycluster v2.2.7
    │   └── redis v0.31.0
    │       └── rustycluster v2.2.7 (*)
    ├── h2 v0.4.10
    │   ├── hyper v1.6.0
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1
    │   │   │       └── rustycluster v2.2.7 (*)
    │   │   ├── hyper-util v0.1.14
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tonic v0.13.1 (*)
    ├── redis v0.29.5 (*)
    ├── redis v0.31.0 (*)
    └── tower v0.5.2
        ├── axum v0.8.4
        │   └── tonic v0.13.1 (*)
        └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#toml@0.5.11:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ toml v0.5.11
    └── config v0.13.4
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tonic@0.13.1:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tonic v0.13.1
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tonic-build@0.13.1:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tonic-build v0.13.1
    └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tower@0.5.2:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tower v0.5.2
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tower-layer@0.3.3:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tower-layer v0.3.3
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── axum-core v0.5.2
    │   └── axum v0.8.4 (*)
    ├── tonic v0.13.1 (*)
    └── tower v0.5.2
        ├── axum v0.8.4 (*)
        └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tower-service@0.3.3:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tower-service v0.3.3
    ├── axum v0.8.4
    │   └── tonic v0.13.1
    │       └── rustycluster v2.2.7
    ├── axum-core v0.5.2
    │   └── axum v0.8.4 (*)
    ├── hyper-timeout v0.5.2
    │   └── tonic v0.13.1 (*)
    ├── hyper-util v0.1.14
    │   ├── hyper-timeout v0.5.2 (*)
    │   └── tonic v0.13.1 (*)
    ├── tonic v0.13.1 (*)
    └── tower v0.5.2
        ├── axum v0.8.4 (*)
        └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tracing@0.1.41:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tracing v0.1.41
    ├── h2 v0.4.10
    │   ├── hyper v1.6.0
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1
    │   │   │       └── rustycluster v2.2.7
    │   │   ├── hyper-util v0.1.14
    │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tonic v0.13.1 (*)
    ├── hyper-util v0.1.14 (*)
    ├── rustycluster v2.2.7 (*)
    ├── tokio v1.45.1
    │   ├── combine v4.6.7
    │   │   ├── redis v0.29.5
    │   │   │   └── deadpool-redis v0.20.0
    │   │   │       └── rustycluster v2.2.7 (*)
    │   │   └── redis v0.31.0
    │   │       └── rustycluster v2.2.7 (*)
    │   ├── deadpool v0.12.2
    │   │   └── deadpool-redis v0.20.0 (*)
    │   ├── deadpool-runtime v0.1.4
    │   │   └── deadpool v0.12.2 (*)
    │   ├── h2 v0.4.10 (*)
    │   ├── hyper v1.6.0 (*)
    │   ├── hyper-timeout v0.5.2 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   ├── redis v0.29.5 (*)
    │   ├── redis v0.31.0 (*)
    │   ├── rustycluster v2.2.7 (*)
    │   ├── tokio-stream v0.1.17
    │   │   └── tonic v0.13.1 (*)
    │   ├── tokio-util v0.7.15
    │   │   ├── combine v4.6.7 (*)
    │   │   ├── h2 v0.4.10 (*)
    │   │   ├── redis v0.29.5 (*)
    │   │   ├── redis v0.31.0 (*)
    │   │   └── tower v0.5.2
    │   │       ├── axum v0.8.4
    │   │       │   └── tonic v0.13.1 (*)
    │   │       └── tonic v0.13.1 (*)
    │   ├── tonic v0.13.1 (*)
    │   └── tower v0.5.2 (*)
    ├── tonic v0.13.1 (*)
    ├── tower v0.5.2 (*)
    └── tracing-subscriber v0.3.19
        ├── rustycluster v2.2.7 (*)
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tracing-appender@0.2.3:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tracing-appender v0.2.3
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tracing-attributes@0.1.30:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tracing-attributes v0.1.30
    └── tracing v0.1.41
        ├── h2 v0.4.10
        │   ├── hyper v1.6.0
        │   │   ├── hyper-timeout v0.5.2
        │   │   │   └── tonic v0.13.1
        │   │   │       └── rustycluster v2.2.7
        │   │   ├── hyper-util v0.1.14
        │   │   │   ├── hyper-timeout v0.5.2 (*)
        │   │   │   └── tonic v0.13.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tonic v0.13.1 (*)
        ├── hyper-util v0.1.14 (*)
        ├── rustycluster v2.2.7 (*)
        ├── tokio v1.45.1
        │   ├── combine v4.6.7
        │   │   ├── redis v0.29.5
        │   │   │   └── deadpool-redis v0.20.0
        │   │   │       └── rustycluster v2.2.7 (*)
        │   │   └── redis v0.31.0
        │   │       └── rustycluster v2.2.7 (*)
        │   ├── deadpool v0.12.2
        │   │   └── deadpool-redis v0.20.0 (*)
        │   ├── deadpool-runtime v0.1.4
        │   │   └── deadpool v0.12.2 (*)
        │   ├── h2 v0.4.10 (*)
        │   ├── hyper v1.6.0 (*)
        │   ├── hyper-timeout v0.5.2 (*)
        │   ├── hyper-util v0.1.14 (*)
        │   ├── redis v0.29.5 (*)
        │   ├── redis v0.31.0 (*)
        │   ├── rustycluster v2.2.7 (*)
        │   ├── tokio-stream v0.1.17
        │   │   └── tonic v0.13.1 (*)
        │   ├── tokio-util v0.7.15
        │   │   ├── combine v4.6.7 (*)
        │   │   ├── h2 v0.4.10 (*)
        │   │   ├── redis v0.29.5 (*)
        │   │   ├── redis v0.31.0 (*)
        │   │   └── tower v0.5.2
        │   │       ├── axum v0.8.4
        │   │       │   └── tonic v0.13.1 (*)
        │   │       └── tonic v0.13.1 (*)
        │   ├── tonic v0.13.1 (*)
        │   └── tower v0.5.2 (*)
        ├── tonic v0.13.1 (*)
        ├── tower v0.5.2 (*)
        └── tracing-subscriber v0.3.19
            ├── rustycluster v2.2.7 (*)
            └── tracing-appender v0.2.3
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tracing-core@0.1.34:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tracing-core v0.1.34
    ├── tracing v0.1.41
    │   ├── h2 v0.4.10
    │   │   ├── hyper v1.6.0
    │   │   │   ├── hyper-timeout v0.5.2
    │   │   │   │   └── tonic v0.13.1
    │   │   │   │       └── rustycluster v2.2.7
    │   │   │   ├── hyper-util v0.1.14
    │   │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   │   └── tonic v0.13.1 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   ├── hyper-util v0.1.14 (*)
    │   ├── rustycluster v2.2.7 (*)
    │   ├── tokio v1.45.1
    │   │   ├── combine v4.6.7
    │   │   │   ├── redis v0.29.5
    │   │   │   │   └── deadpool-redis v0.20.0
    │   │   │   │       └── rustycluster v2.2.7 (*)
    │   │   │   └── redis v0.31.0
    │   │   │       └── rustycluster v2.2.7 (*)
    │   │   ├── deadpool v0.12.2
    │   │   │   └── deadpool-redis v0.20.0 (*)
    │   │   ├── deadpool-runtime v0.1.4
    │   │   │   └── deadpool v0.12.2 (*)
    │   │   ├── h2 v0.4.10 (*)
    │   │   ├── hyper v1.6.0 (*)
    │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   ├── hyper-util v0.1.14 (*)
    │   │   ├── redis v0.29.5 (*)
    │   │   ├── redis v0.31.0 (*)
    │   │   ├── rustycluster v2.2.7 (*)
    │   │   ├── tokio-stream v0.1.17
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── tokio-util v0.7.15
    │   │   │   ├── combine v4.6.7 (*)
    │   │   │   ├── h2 v0.4.10 (*)
    │   │   │   ├── redis v0.29.5 (*)
    │   │   │   ├── redis v0.31.0 (*)
    │   │   │   └── tower v0.5.2
    │   │   │       ├── axum v0.8.4
    │   │   │       │   └── tonic v0.13.1 (*)
    │   │   │       └── tonic v0.13.1 (*)
    │   │   ├── tonic v0.13.1 (*)
    │   │   └── tower v0.5.2 (*)
    │   ├── tonic v0.13.1 (*)
    │   ├── tower v0.5.2 (*)
    │   └── tracing-subscriber v0.3.19
    │       ├── rustycluster v2.2.7 (*)
    │       └── tracing-appender v0.2.3
    │           └── rustycluster v2.2.7 (*)
    ├── tracing-log v0.1.4
    │   └── rustycluster v2.2.7 (*)
    ├── tracing-log v0.2.0
    │   └── tracing-subscriber v0.3.19 (*)
    ├── tracing-serde v0.2.0
    │   └── tracing-subscriber v0.3.19 (*)
    └── tracing-subscriber v0.3.19 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tracing-log@0.1.4:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tracing-log v0.1.4
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tracing-log@0.2.0:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tracing-log v0.2.0
    └── tracing-subscriber v0.3.19
        ├── rustycluster v2.2.7
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tracing-serde@0.2.0:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tracing-serde v0.2.0
    └── tracing-subscriber v0.3.19
        ├── rustycluster v2.2.7
        └── tracing-appender v0.2.3
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#tracing-subscriber@0.3.19:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ tracing-subscriber v0.3.19
    ├── rustycluster v2.2.7
    └── tracing-appender v0.2.3
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#try-lock@0.2.5:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ try-lock v0.2.5
    └── want v0.3.1
        └── hyper v1.6.0
            ├── hyper-timeout v0.5.2
            │   └── tonic v0.13.1
            │       └── rustycluster v2.2.7
            ├── hyper-util v0.1.14
            │   ├── hyper-timeout v0.5.2 (*)
            │   └── tonic v0.13.1 (*)
            └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#typenum@1.18.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ typenum v1.18.0
    ├── crypto-common v0.1.6
    │   ├── aead v0.5.2
    │   │   └── aes-gcm v0.10.3
    │   │       └── rustycluster v2.2.7
    │   ├── cipher v0.4.4
    │   │   ├── aes v0.8.4
    │   │   │   └── aes-gcm v0.10.3 (*)
    │   │   ├── aes-gcm v0.10.3 (*)
    │   │   └── ctr v0.9.2
    │   │       └── aes-gcm v0.10.3 (*)
    │   ├── digest v0.10.7
    │   │   ├── hmac v0.12.1
    │   │   │   └── pbkdf2 v0.12.2
    │   │   │       └── rustycluster v2.2.7 (*)
    │   │   ├── pbkdf2 v0.12.2 (*)
    │   │   └── sha2 v0.10.9
    │   │       ├── (build) pest_meta v2.8.1
    │   │       │   └── pest_generator v2.8.1
    │   │       │       └── pest_derive v2.8.1
    │   │       │           └── json5 v0.4.1
    │   │       │               └── config v0.13.4
    │   │       │                   └── rustycluster v2.2.7 (*)
    │   │       └── rustycluster v2.2.7 (*)
    │   └── universal-hash v0.5.1
    │       └── polyval v0.6.2
    │           └── ghash v0.5.1
    │               └── aes-gcm v0.10.3 (*)
    └── generic-array v0.14.7
        ├── aead v0.5.2 (*)
        ├── block-buffer v0.10.4
        │   └── digest v0.10.7 (*)
        ├── crypto-common v0.1.6 (*)
        └── inout v0.1.4
            └── cipher v0.4.4 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#ucd-trie@0.1.7:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ ucd-trie v0.1.7
    └── pest v2.8.1
        ├── json5 v0.4.1
        │   └── config v0.13.4
        │       └── rustycluster v2.2.7
        ├── pest_derive v2.8.1
        │   └── json5 v0.4.1 (*)
        ├── pest_generator v2.8.1
        │   └── pest_derive v2.8.1 (*)
        └── pest_meta v2.8.1
            └── pest_generator v2.8.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#unicode-ident@1.0.18:4:13
  │
4 │ license = "(MIT OR Apache-2.0) AND Unicode-3.0"
  │            ─━━━────━━━━━━━━━━──────━━━━━━━━━━━
  │            ││      │               │
  │            ││      │               rejected: license is not explicitly allowed
  │            ││      rejected: license is not explicitly allowed
  │            │rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ unicode-ident v1.0.18
    ├── proc-macro2 v1.0.95
    │   ├── async-trait v0.1.88
    │   │   ├── config v0.13.4
    │   │   │   └── rustycluster v2.2.7
    │   │   └── tonic v0.13.1
    │   │       └── rustycluster v2.2.7 (*)
    │   ├── displaydoc v0.2.5
    │   │   ├── icu_collections v2.0.0
    │   │   │   ├── icu_normalizer v2.0.0
    │   │   │   │   └── idna_adapter v1.2.1
    │   │   │   │       └── idna v1.0.3
    │   │   │   │           └── url v2.5.4
    │   │   │   │               ├── redis v0.29.5
    │   │   │   │               │   └── deadpool-redis v0.20.0
    │   │   │   │               │       └── rustycluster v2.2.7 (*)
    │   │   │   │               └── redis v0.31.0
    │   │   │   │                   └── rustycluster v2.2.7 (*)
    │   │   │   └── icu_properties v2.0.1
    │   │   │       └── idna_adapter v1.2.1 (*)
    │   │   ├── icu_locale_core v2.0.0
    │   │   │   ├── icu_properties v2.0.1 (*)
    │   │   │   └── icu_provider v2.0.0
    │   │   │       ├── icu_normalizer v2.0.0 (*)
    │   │   │       └── icu_properties v2.0.1 (*)
    │   │   ├── icu_normalizer v2.0.0 (*)
    │   │   ├── icu_properties v2.0.1 (*)
    │   │   ├── icu_provider v2.0.0 (*)
    │   │   ├── tinystr v0.8.1
    │   │   │   ├── icu_locale_core v2.0.0 (*)
    │   │   │   └── icu_provider v2.0.0 (*)
    │   │   └── zerotrie v0.2.2
    │   │       ├── icu_properties v2.0.1 (*)
    │   │       └── icu_provider v2.0.0 (*)
    │   ├── futures-macro v0.3.31
    │   │   └── futures-util v0.3.31
    │   │       ├── axum v0.8.4
    │   │       │   └── tonic v0.13.1 (*)
    │   │       ├── futures v0.3.31
    │   │       │   └── rustycluster v2.2.7 (*)
    │   │       ├── futures-executor v0.3.31
    │   │       │   └── futures v0.3.31 (*)
    │   │       ├── hyper v1.6.0
    │   │       │   ├── hyper-timeout v0.5.2
    │   │       │   │   └── tonic v0.13.1 (*)
    │   │       │   ├── hyper-util v0.1.14
    │   │       │   │   ├── hyper-timeout v0.5.2 (*)
    │   │       │   │   └── tonic v0.13.1 (*)
    │   │       │   └── tonic v0.13.1 (*)
    │   │       ├── hyper-util v0.1.14 (*)
    │   │       ├── redis v0.29.5 (*)
    │   │       ├── redis v0.31.0 (*)
    │   │       └── tower v0.5.2
    │   │           ├── axum v0.8.4 (*)
    │   │           └── tonic v0.13.1 (*)
    │   ├── pest_generator v2.8.1
    │   │   └── pest_derive v2.8.1
    │   │       └── json5 v0.4.1
    │   │           └── config v0.13.4 (*)
    │   ├── pin-project-internal v1.1.10
    │   │   └── pin-project v1.1.10
    │   │       └── tonic v0.13.1 (*)
    │   ├── prettyplease v0.2.35
    │   │   ├── prost-build v0.13.5
    │   │   │   └── tonic-build v0.13.1
    │   │   │       └── (build) rustycluster v2.2.7 (*)
    │   │   └── tonic-build v0.13.1 (*)
    │   ├── prost-derive v0.13.5
    │   │   └── prost v0.13.5
    │   │       ├── prost-build v0.13.5 (*)
    │   │       ├── prost-types v0.13.5
    │   │       │   ├── prost-build v0.13.5 (*)
    │   │       │   ├── rustycluster v2.2.7 (*)
    │   │       │   └── tonic-build v0.13.1 (*)
    │   │       ├── rustycluster v2.2.7 (*)
    │   │       └── tonic v0.13.1 (*)
    │   ├── quote v1.0.40
    │   │   ├── async-trait v0.1.88 (*)
    │   │   ├── displaydoc v0.2.5 (*)
    │   │   ├── futures-macro v0.3.31 (*)
    │   │   ├── pest_generator v2.8.1 (*)
    │   │   ├── pin-project-internal v1.1.10 (*)
    │   │   ├── prost-derive v0.13.5 (*)
    │   │   ├── serde_derive v1.0.219
    │   │   │   └── serde v1.0.219
    │   │   │       ├── axum v0.8.4 (*)
    │   │   │       ├── config v0.13.4 (*)
    │   │   │       ├── json5 v0.4.1 (*)
    │   │   │       ├── ron v0.7.1
    │   │   │       │   └── config v0.13.4 (*)
    │   │   │       ├── rustycluster v2.2.7 (*)
    │   │   │       ├── serde_json v1.0.140
    │   │   │       │   ├── config v0.13.4 (*)
    │   │   │       │   └── tracing-subscriber v0.3.19
    │   │   │       │       ├── rustycluster v2.2.7 (*)
    │   │   │       │       └── tracing-appender v0.2.3
    │   │   │       │           └── rustycluster v2.2.7 (*)
    │   │   │       ├── toml v0.5.11
    │   │   │       │   └── config v0.13.4 (*)
    │   │   │       ├── tracing-serde v0.2.0
    │   │   │       │   └── tracing-subscriber v0.3.19 (*)
    │   │   │       └── tracing-subscriber v0.3.19 (*)
    │   │   ├── syn v2.0.104
    │   │   │   ├── async-trait v0.1.88 (*)
    │   │   │   ├── displaydoc v0.2.5 (*)
    │   │   │   ├── futures-macro v0.3.31 (*)
    │   │   │   ├── pest_generator v2.8.1 (*)
    │   │   │   ├── pin-project-internal v1.1.10 (*)
    │   │   │   ├── prettyplease v0.2.35 (*)
    │   │   │   ├── prost-build v0.13.5 (*)
    │   │   │   ├── prost-derive v0.13.5 (*)
    │   │   │   ├── serde_derive v1.0.219 (*)
    │   │   │   ├── synstructure v0.13.2
    │   │   │   │   ├── yoke-derive v0.8.0
    │   │   │   │   │   └── yoke v0.8.0
    │   │   │   │   │       ├── icu_collections v2.0.0 (*)
    │   │   │   │   │       ├── icu_provider v2.0.0 (*)
    │   │   │   │   │       ├── zerotrie v0.2.2 (*)
    │   │   │   │   │       └── zerovec v0.11.2
    │   │   │   │   │           ├── icu_collections v2.0.0 (*)
    │   │   │   │   │           ├── icu_locale_core v2.0.0 (*)
    │   │   │   │   │           ├── icu_normalizer v2.0.0 (*)
    │   │   │   │   │           ├── icu_properties v2.0.1 (*)
    │   │   │   │   │           ├── icu_provider v2.0.0 (*)
    │   │   │   │   │           ├── potential_utf v0.1.2
    │   │   │   │   │           │   ├── icu_collections v2.0.0 (*)
    │   │   │   │   │           │   └── icu_properties v2.0.1 (*)
    │   │   │   │   │           └── tinystr v0.8.1 (*)
    │   │   │   │   └── zerofrom-derive v0.1.6
    │   │   │   │       └── zerofrom v0.1.6
    │   │   │   │           ├── icu_collections v2.0.0 (*)
    │   │   │   │           ├── icu_provider v2.0.0 (*)
    │   │   │   │           ├── yoke v0.8.0 (*)
    │   │   │   │           ├── zerotrie v0.2.2 (*)
    │   │   │   │           └── zerovec v0.11.2 (*)
    │   │   │   ├── thiserror-impl v1.0.69
    │   │   │   │   └── thiserror v1.0.69
    │   │   │   │       ├── protobuf v3.7.2
    │   │   │   │       │   └── prometheus v0.14.0
    │   │   │   │       │       └── rustycluster v2.2.7 (*)
    │   │   │   │       ├── protobuf-support v3.7.2
    │   │   │   │       │   └── protobuf v3.7.2 (*)
    │   │   │   │       └── tracing-appender v0.2.3 (*)
    │   │   │   ├── thiserror-impl v2.0.12
    │   │   │   │   └── thiserror v2.0.12
    │   │   │   │       ├── pest v2.8.1
    │   │   │   │       │   ├── json5 v0.4.1 (*)
    │   │   │   │       │   ├── pest_derive v2.8.1 (*)
    │   │   │   │       │   ├── pest_generator v2.8.1 (*)
    │   │   │   │       │   └── pest_meta v2.8.1
    │   │   │   │       │       └── pest_generator v2.8.1 (*)
    │   │   │   │       └── prometheus v0.14.0 (*)
    │   │   │   ├── tokio-macros v2.5.0
    │   │   │   │   └── tokio v1.45.1
    │   │   │   │       ├── combine v4.6.7
    │   │   │   │       │   ├── redis v0.29.5 (*)
    │   │   │   │       │   └── redis v0.31.0 (*)
    │   │   │   │       ├── deadpool v0.12.2
    │   │   │   │       │   └── deadpool-redis v0.20.0 (*)
    │   │   │   │       ├── deadpool-runtime v0.1.4
    │   │   │   │       │   └── deadpool v0.12.2 (*)
    │   │   │   │       ├── h2 v0.4.10
    │   │   │   │       │   ├── hyper v1.6.0 (*)
    │   │   │   │       │   └── tonic v0.13.1 (*)
    │   │   │   │       ├── hyper v1.6.0 (*)
    │   │   │   │       ├── hyper-timeout v0.5.2 (*)
    │   │   │   │       ├── hyper-util v0.1.14 (*)
    │   │   │   │       ├── redis v0.29.5 (*)
    │   │   │   │       ├── redis v0.31.0 (*)
    │   │   │   │       ├── rustycluster v2.2.7 (*)
    │   │   │   │       ├── tokio-stream v0.1.17
    │   │   │   │       │   └── tonic v0.13.1 (*)
    │   │   │   │       ├── tokio-util v0.7.15
    │   │   │   │       │   ├── combine v4.6.7 (*)
    │   │   │   │       │   ├── h2 v0.4.10 (*)
    │   │   │   │       │   ├── redis v0.29.5 (*)
    │   │   │   │       │   ├── redis v0.31.0 (*)
    │   │   │   │       │   └── tower v0.5.2 (*)
    │   │   │   │       ├── tonic v0.13.1 (*)
    │   │   │   │       └── tower v0.5.2 (*)
    │   │   │   ├── tonic-build v0.13.1 (*)
    │   │   │   ├── tracing-attributes v0.1.30
    │   │   │   │   └── tracing v0.1.41
    │   │   │   │       ├── h2 v0.4.10 (*)
    │   │   │   │       ├── hyper-util v0.1.14 (*)
    │   │   │   │       ├── rustycluster v2.2.7 (*)
    │   │   │   │       ├── tokio v1.45.1 (*)
    │   │   │   │       ├── tonic v0.13.1 (*)
    │   │   │   │       ├── tower v0.5.2 (*)
    │   │   │   │       └── tracing-subscriber v0.3.19 (*)
    │   │   │   ├── wasm-bindgen-backend v0.2.100
    │   │   │   │   └── wasm-bindgen-macro-support v0.2.100
    │   │   │   │       └── wasm-bindgen-macro v0.2.100
    │   │   │   │           └── wasm-bindgen v0.2.100
    │   │   │   │               ├── iana-time-zone v0.1.63
    │   │   │   │               │   └── chrono v0.4.41
    │   │   │   │               │       └── tracing-subscriber v0.3.19 (*)
    │   │   │   │               └── js-sys v0.3.77
    │   │   │   │                   └── iana-time-zone v0.1.63 (*)
    │   │   │   ├── wasm-bindgen-macro-support v0.2.100 (*)
    │   │   │   ├── windows-implement v0.60.0
    │   │   │   │   └── windows-core v0.61.2
    │   │   │   │       └── iana-time-zone v0.1.63 (*)
    │   │   │   ├── windows-interface v0.59.1
    │   │   │   │   └── windows-core v0.61.2 (*)
    │   │   │   ├── yoke-derive v0.8.0 (*)
    │   │   │   ├── zerofrom-derive v0.1.6 (*)
    │   │   │   └── zerovec-derive v0.11.1
    │   │   │       └── zerovec v0.11.2 (*)
    │   │   ├── synstructure v0.13.2 (*)
    │   │   ├── thiserror-impl v1.0.69 (*)
    │   │   ├── thiserror-impl v2.0.12 (*)
    │   │   ├── tokio-macros v2.5.0 (*)
    │   │   ├── tonic-build v0.13.1 (*)
    │   │   ├── tracing-attributes v0.1.30 (*)
    │   │   ├── wasm-bindgen-backend v0.2.100 (*)
    │   │   ├── wasm-bindgen-macro v0.2.100 (*)
    │   │   ├── wasm-bindgen-macro-support v0.2.100 (*)
    │   │   ├── windows-implement v0.60.0 (*)
    │   │   ├── windows-interface v0.59.1 (*)
    │   │   ├── yoke-derive v0.8.0 (*)
    │   │   ├── zerofrom-derive v0.1.6 (*)
    │   │   └── zerovec-derive v0.11.1 (*)
    │   ├── serde_derive v1.0.219 (*)
    │   ├── syn v2.0.104 (*)
    │   ├── synstructure v0.13.2 (*)
    │   ├── thiserror-impl v1.0.69 (*)
    │   ├── thiserror-impl v2.0.12 (*)
    │   ├── tokio-macros v2.5.0 (*)
    │   ├── tonic-build v0.13.1 (*)
    │   ├── tracing-attributes v0.1.30 (*)
    │   ├── wasm-bindgen-backend v0.2.100 (*)
    │   ├── wasm-bindgen-macro-support v0.2.100 (*)
    │   ├── windows-implement v0.60.0 (*)
    │   ├── windows-interface v0.59.1 (*)
    │   ├── yoke-derive v0.8.0 (*)
    │   ├── zerofrom-derive v0.1.6 (*)
    │   └── zerovec-derive v0.11.1 (*)
    ├── syn v2.0.104 (*)
    └── wasm-bindgen-shared v0.2.100
        ├── wasm-bindgen-backend v0.2.100 (*)
        └── wasm-bindgen-macro-support v0.2.100 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#universal-hash@0.5.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ universal-hash v0.5.1
    └── polyval v0.6.2
        └── ghash v0.5.1
            └── aes-gcm v0.10.3
                └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#url@2.5.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ url v2.5.4
    ├── redis v0.29.5
    │   └── deadpool-redis v0.20.0
    │       └── rustycluster v2.2.7
    └── redis v0.31.0
        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#utf8_iter@1.0.4:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ utf8_iter v1.0.4
    └── idna v1.0.3
        └── url v2.5.4
            ├── redis v0.29.5
            │   └── deadpool-redis v0.20.0
            │       └── rustycluster v2.2.7
            └── redis v0.31.0
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#uuid@1.17.0:4:12
  │
4 │ license = "Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────━━━
  │            │             │
  │            │             rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ uuid v1.17.0
    └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#version_check@0.9.5:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ version_check v0.9.5
    └── (build) generic-array v0.14.7
        ├── aead v0.5.2
        │   └── aes-gcm v0.10.3
        │       └── rustycluster v2.2.7
        ├── block-buffer v0.10.4
        │   └── digest v0.10.7
        │       ├── hmac v0.12.1
        │       │   └── pbkdf2 v0.12.2
        │       │       └── rustycluster v2.2.7 (*)
        │       ├── pbkdf2 v0.12.2 (*)
        │       └── sha2 v0.10.9
        │           ├── (build) pest_meta v2.8.1
        │           │   └── pest_generator v2.8.1
        │           │       └── pest_derive v2.8.1
        │           │           └── json5 v0.4.1
        │           │               └── config v0.13.4
        │           │                   └── rustycluster v2.2.7 (*)
        │           └── rustycluster v2.2.7 (*)
        ├── crypto-common v0.1.6
        │   ├── aead v0.5.2 (*)
        │   ├── cipher v0.4.4
        │   │   ├── aes v0.8.4
        │   │   │   └── aes-gcm v0.10.3 (*)
        │   │   ├── aes-gcm v0.10.3 (*)
        │   │   └── ctr v0.9.2
        │   │       └── aes-gcm v0.10.3 (*)
        │   ├── digest v0.10.7 (*)
        │   └── universal-hash v0.5.1
        │       └── polyval v0.6.2
        │           └── ghash v0.5.1
        │               └── aes-gcm v0.10.3 (*)
        └── inout v0.1.4
            └── cipher v0.4.4 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#want@0.3.1:4:12
  │
4 │ license = "MIT"
  │            ━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ want v0.3.1
    └── hyper v1.6.0
        ├── hyper-timeout v0.5.2
        │   └── tonic v0.13.1
        │       └── rustycluster v2.2.7
        ├── hyper-util v0.1.14
        │   ├── hyper-timeout v0.5.2 (*)
        │   └── tonic v0.13.1 (*)
        └── tonic v0.13.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#wasi@0.11.1+wasi-snapshot-preview1:4:12
  │
4 │ license = "Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────────────────────────━━━━━━━━━━────━━━
  │            │                                 │             │
  │            │                                 │             rejected: license is not explicitly allowed
  │            │                                 rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ wasi v0.11.1+wasi-snapshot-preview1
    ├── getrandom v0.2.16
    │   └── rand_core v0.6.4
    │       ├── crypto-common v0.1.6
    │       │   ├── aead v0.5.2
    │       │   │   └── aes-gcm v0.10.3
    │       │   │       └── rustycluster v2.2.7
    │       │   ├── cipher v0.4.4
    │       │   │   ├── aes v0.8.4
    │       │   │   │   └── aes-gcm v0.10.3 (*)
    │       │   │   ├── aes-gcm v0.10.3 (*)
    │       │   │   └── ctr v0.9.2
    │       │   │       └── aes-gcm v0.10.3 (*)
    │       │   ├── digest v0.10.7
    │       │   │   ├── hmac v0.12.1
    │       │   │   │   └── pbkdf2 v0.12.2
    │       │   │   │       └── rustycluster v2.2.7 (*)
    │       │   │   ├── pbkdf2 v0.12.2 (*)
    │       │   │   └── sha2 v0.10.9
    │       │   │       ├── (build) pest_meta v2.8.1
    │       │   │       │   └── pest_generator v2.8.1
    │       │   │       │       └── pest_derive v2.8.1
    │       │   │       │           └── json5 v0.4.1
    │       │   │       │               └── config v0.13.4
    │       │   │       │                   └── rustycluster v2.2.7 (*)
    │       │   │       └── rustycluster v2.2.7 (*)
    │       │   └── universal-hash v0.5.1
    │       │       └── polyval v0.6.2
    │       │           └── ghash v0.5.1
    │       │               └── aes-gcm v0.10.3 (*)
    │       ├── rand v0.8.5
    │       │   └── rustycluster v2.2.7 (*)
    │       └── rand_chacha v0.3.1
    │           └── rand v0.8.5 (*)
    └── mio v1.0.4
        └── tokio v1.45.1
            ├── combine v4.6.7
            │   ├── redis v0.29.5
            │   │   └── deadpool-redis v0.20.0
            │   │       └── rustycluster v2.2.7 (*)
            │   └── redis v0.31.0
            │       └── rustycluster v2.2.7 (*)
            ├── deadpool v0.12.2
            │   └── deadpool-redis v0.20.0 (*)
            ├── deadpool-runtime v0.1.4
            │   └── deadpool v0.12.2 (*)
            ├── h2 v0.4.10
            │   ├── hyper v1.6.0
            │   │   ├── hyper-timeout v0.5.2
            │   │   │   └── tonic v0.13.1
            │   │   │       └── rustycluster v2.2.7 (*)
            │   │   ├── hyper-util v0.1.14
            │   │   │   ├── hyper-timeout v0.5.2 (*)
            │   │   │   └── tonic v0.13.1 (*)
            │   │   └── tonic v0.13.1 (*)
            │   └── tonic v0.13.1 (*)
            ├── hyper v1.6.0 (*)
            ├── hyper-timeout v0.5.2 (*)
            ├── hyper-util v0.1.14 (*)
            ├── redis v0.29.5 (*)
            ├── redis v0.31.0 (*)
            ├── rustycluster v2.2.7 (*)
            ├── tokio-stream v0.1.17
            │   └── tonic v0.13.1 (*)
            ├── tokio-util v0.7.15
            │   ├── combine v4.6.7 (*)
            │   ├── h2 v0.4.10 (*)
            │   ├── redis v0.29.5 (*)
            │   ├── redis v0.31.0 (*)
            │   └── tower v0.5.2
            │       ├── axum v0.8.4
            │       │   └── tonic v0.13.1 (*)
            │       └── tonic v0.13.1 (*)
            ├── tonic v0.13.1 (*)
            └── tower v0.5.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#wasi@0.14.2+wasi-0.2.4:4:12
  │
4 │ license = "Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────────────────────────━━━━━━━━━━────━━━
  │            │                                 │             │
  │            │                                 │             rejected: license is not explicitly allowed
  │            │                                 rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ wasi v0.14.2+wasi-0.2.4
    └── getrandom v0.3.3
        ├── tempfile v3.20.0
        │   └── prost-build v0.13.5
        │       └── tonic-build v0.13.1
        │           └── (build) rustycluster v2.2.7
        └── uuid v1.17.0
            └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#wasm-bindgen@0.2.100:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ wasm-bindgen v0.2.100
    ├── iana-time-zone v0.1.63
    │   └── chrono v0.4.41
    │       └── tracing-subscriber v0.3.19
    │           ├── rustycluster v2.2.7
    │           └── tracing-appender v0.2.3
    │               └── rustycluster v2.2.7 (*)
    └── js-sys v0.3.77
        └── iana-time-zone v0.1.63 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#wasm-bindgen-backend@0.2.100:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ wasm-bindgen-backend v0.2.100
    └── wasm-bindgen-macro-support v0.2.100
        └── wasm-bindgen-macro v0.2.100
            └── wasm-bindgen v0.2.100
                ├── iana-time-zone v0.1.63
                │   └── chrono v0.4.41
                │       └── tracing-subscriber v0.3.19
                │           ├── rustycluster v2.2.7
                │           └── tracing-appender v0.2.3
                │               └── rustycluster v2.2.7 (*)
                └── js-sys v0.3.77
                    └── iana-time-zone v0.1.63 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#wasm-bindgen-macro@0.2.100:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ wasm-bindgen-macro v0.2.100
    └── wasm-bindgen v0.2.100
        ├── iana-time-zone v0.1.63
        │   └── chrono v0.4.41
        │       └── tracing-subscriber v0.3.19
        │           ├── rustycluster v2.2.7
        │           └── tracing-appender v0.2.3
        │               └── rustycluster v2.2.7 (*)
        └── js-sys v0.3.77
            └── iana-time-zone v0.1.63 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#wasm-bindgen-macro-support@0.2.100:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ wasm-bindgen-macro-support v0.2.100
    └── wasm-bindgen-macro v0.2.100
        └── wasm-bindgen v0.2.100
            ├── iana-time-zone v0.1.63
            │   └── chrono v0.4.41
            │       └── tracing-subscriber v0.3.19
            │           ├── rustycluster v2.2.7
            │           └── tracing-appender v0.2.3
            │               └── rustycluster v2.2.7 (*)
            └── js-sys v0.3.77
                └── iana-time-zone v0.1.63 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#wasm-bindgen-shared@0.2.100:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ wasm-bindgen-shared v0.2.100
    ├── wasm-bindgen-backend v0.2.100
    │   └── wasm-bindgen-macro-support v0.2.100
    │       └── wasm-bindgen-macro v0.2.100
    │           └── wasm-bindgen v0.2.100
    │               ├── iana-time-zone v0.1.63
    │               │   └── chrono v0.4.41
    │               │       └── tracing-subscriber v0.3.19
    │               │           ├── rustycluster v2.2.7
    │               │           └── tracing-appender v0.2.3
    │               │               └── rustycluster v2.2.7 (*)
    │               └── js-sys v0.3.77
    │                   └── iana-time-zone v0.1.63 (*)
    └── wasm-bindgen-macro-support v0.2.100 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#winapi@0.3.9:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ winapi v0.3.9
    └── nu-ansi-term v0.46.0
        └── tracing-subscriber v0.3.19
            ├── rustycluster v2.2.7
            └── tracing-appender v0.2.3
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#winapi-i686-pc-windows-gnu@0.4.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ winapi-i686-pc-windows-gnu v0.4.0
    └── winapi v0.3.9
        └── nu-ansi-term v0.46.0
            └── tracing-subscriber v0.3.19
                ├── rustycluster v2.2.7
                └── tracing-appender v0.2.3
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#winapi-x86_64-pc-windows-gnu@0.4.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ winapi-x86_64-pc-windows-gnu v0.4.0
    └── winapi v0.3.9
        └── nu-ansi-term v0.46.0
            └── tracing-subscriber v0.3.19
                ├── rustycluster v2.2.7
                └── tracing-appender v0.2.3
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows-core@0.61.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows-core v0.61.2
    └── iana-time-zone v0.1.63
        └── chrono v0.4.41
            └── tracing-subscriber v0.3.19
                ├── rustycluster v2.2.7
                └── tracing-appender v0.2.3
                    └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows-implement@0.60.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows-implement v0.60.0
    └── windows-core v0.61.2
        └── iana-time-zone v0.1.63
            └── chrono v0.4.41
                └── tracing-subscriber v0.3.19
                    ├── rustycluster v2.2.7
                    └── tracing-appender v0.2.3
                        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows-interface@0.59.1:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows-interface v0.59.1
    └── windows-core v0.61.2
        └── iana-time-zone v0.1.63
            └── chrono v0.4.41
                └── tracing-subscriber v0.3.19
                    ├── rustycluster v2.2.7
                    └── tracing-appender v0.2.3
                        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows-link@0.1.3:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows-link v0.1.3
    ├── chrono v0.4.41
    │   └── tracing-subscriber v0.3.19
    │       ├── rustycluster v2.2.7
    │       └── tracing-appender v0.2.3
    │           └── rustycluster v2.2.7 (*)
    ├── windows-core v0.61.2
    │   └── iana-time-zone v0.1.63
    │       └── chrono v0.4.41 (*)
    ├── windows-result v0.3.4
    │   └── windows-core v0.61.2 (*)
    └── windows-strings v0.4.2
        └── windows-core v0.61.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows-result@0.3.4:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows-result v0.3.4
    └── windows-core v0.61.2
        └── iana-time-zone v0.1.63
            └── chrono v0.4.41
                └── tracing-subscriber v0.3.19
                    ├── rustycluster v2.2.7
                    └── tracing-appender v0.2.3
                        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows-strings@0.4.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows-strings v0.4.2
    └── windows-core v0.61.2
        └── iana-time-zone v0.1.63
            └── chrono v0.4.41
                └── tracing-subscriber v0.3.19
                    ├── rustycluster v2.2.7
                    └── tracing-appender v0.2.3
                        └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows-sys@0.52.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows-sys v0.52.0
    ├── socket2 v0.5.10
    │   ├── hyper-util v0.1.14
    │   │   ├── hyper-timeout v0.5.2
    │   │   │   └── tonic v0.13.1
    │   │   │       └── rustycluster v2.2.7
    │   │   └── tonic v0.13.1 (*)
    │   ├── redis v0.29.5
    │   │   └── deadpool-redis v0.20.0
    │   │       └── rustycluster v2.2.7 (*)
    │   ├── redis v0.31.0
    │   │   └── rustycluster v2.2.7 (*)
    │   ├── tokio v1.45.1
    │   │   ├── combine v4.6.7
    │   │   │   ├── redis v0.29.5 (*)
    │   │   │   └── redis v0.31.0 (*)
    │   │   ├── deadpool v0.12.2
    │   │   │   └── deadpool-redis v0.20.0 (*)
    │   │   ├── deadpool-runtime v0.1.4
    │   │   │   └── deadpool v0.12.2 (*)
    │   │   ├── h2 v0.4.10
    │   │   │   ├── hyper v1.6.0
    │   │   │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   │   │   ├── hyper-util v0.1.14 (*)
    │   │   │   │   └── tonic v0.13.1 (*)
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── hyper v1.6.0 (*)
    │   │   ├── hyper-timeout v0.5.2 (*)
    │   │   ├── hyper-util v0.1.14 (*)
    │   │   ├── redis v0.29.5 (*)
    │   │   ├── redis v0.31.0 (*)
    │   │   ├── rustycluster v2.2.7 (*)
    │   │   ├── tokio-stream v0.1.17
    │   │   │   └── tonic v0.13.1 (*)
    │   │   ├── tokio-util v0.7.15
    │   │   │   ├── combine v4.6.7 (*)
    │   │   │   ├── h2 v0.4.10 (*)
    │   │   │   ├── redis v0.29.5 (*)
    │   │   │   ├── redis v0.31.0 (*)
    │   │   │   └── tower v0.5.2
    │   │   │       ├── axum v0.8.4
    │   │   │       │   └── tonic v0.13.1 (*)
    │   │   │       └── tonic v0.13.1 (*)
    │   │   ├── tonic v0.13.1 (*)
    │   │   └── tower v0.5.2 (*)
    │   └── tonic v0.13.1 (*)
    └── tokio v1.45.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows-sys@0.59.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows-sys v0.59.0
    ├── mio v1.0.4
    │   └── tokio v1.45.1
    │       ├── combine v4.6.7
    │       │   ├── redis v0.29.5
    │       │   │   └── deadpool-redis v0.20.0
    │       │   │       └── rustycluster v2.2.7
    │       │   └── redis v0.31.0
    │       │       └── rustycluster v2.2.7 (*)
    │       ├── deadpool v0.12.2
    │       │   └── deadpool-redis v0.20.0 (*)
    │       ├── deadpool-runtime v0.1.4
    │       │   └── deadpool v0.12.2 (*)
    │       ├── h2 v0.4.10
    │       │   ├── hyper v1.6.0
    │       │   │   ├── hyper-timeout v0.5.2
    │       │   │   │   └── tonic v0.13.1
    │       │   │   │       └── rustycluster v2.2.7 (*)
    │       │   │   ├── hyper-util v0.1.14
    │       │   │   │   ├── hyper-timeout v0.5.2 (*)
    │       │   │   │   └── tonic v0.13.1 (*)
    │       │   │   └── tonic v0.13.1 (*)
    │       │   └── tonic v0.13.1 (*)
    │       ├── hyper v1.6.0 (*)
    │       ├── hyper-timeout v0.5.2 (*)
    │       ├── hyper-util v0.1.14 (*)
    │       ├── redis v0.29.5 (*)
    │       ├── redis v0.31.0 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       ├── tokio-stream v0.1.17
    │       │   └── tonic v0.13.1 (*)
    │       ├── tokio-util v0.7.15
    │       │   ├── combine v4.6.7 (*)
    │       │   ├── h2 v0.4.10 (*)
    │       │   ├── redis v0.29.5 (*)
    │       │   ├── redis v0.31.0 (*)
    │       │   └── tower v0.5.2
    │       │       ├── axum v0.8.4
    │       │       │   └── tonic v0.13.1 (*)
    │       │       └── tonic v0.13.1 (*)
    │       ├── tonic v0.13.1 (*)
    │       └── tower v0.5.2 (*)
    ├── rustix v1.0.7
    │   └── tempfile v3.20.0
    │       └── prost-build v0.13.5
    │           └── tonic-build v0.13.1
    │               └── (build) rustycluster v2.2.7 (*)
    └── tempfile v3.20.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows-sys@0.60.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows-sys v0.60.2
    └── errno v0.3.13
        └── rustix v1.0.7
            └── tempfile v3.20.0
                └── prost-build v0.13.5
                    └── tonic-build v0.13.1
                        └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows-targets@0.52.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows-targets v0.52.6
    ├── backtrace v0.3.75
    │   └── tokio v1.45.1
    │       ├── combine v4.6.7
    │       │   ├── redis v0.29.5
    │       │   │   └── deadpool-redis v0.20.0
    │       │   │       └── rustycluster v2.2.7
    │       │   └── redis v0.31.0
    │       │       └── rustycluster v2.2.7 (*)
    │       ├── deadpool v0.12.2
    │       │   └── deadpool-redis v0.20.0 (*)
    │       ├── deadpool-runtime v0.1.4
    │       │   └── deadpool v0.12.2 (*)
    │       ├── h2 v0.4.10
    │       │   ├── hyper v1.6.0
    │       │   │   ├── hyper-timeout v0.5.2
    │       │   │   │   └── tonic v0.13.1
    │       │   │   │       └── rustycluster v2.2.7 (*)
    │       │   │   ├── hyper-util v0.1.14
    │       │   │   │   ├── hyper-timeout v0.5.2 (*)
    │       │   │   │   └── tonic v0.13.1 (*)
    │       │   │   └── tonic v0.13.1 (*)
    │       │   └── tonic v0.13.1 (*)
    │       ├── hyper v1.6.0 (*)
    │       ├── hyper-timeout v0.5.2 (*)
    │       ├── hyper-util v0.1.14 (*)
    │       ├── redis v0.29.5 (*)
    │       ├── redis v0.31.0 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       ├── tokio-stream v0.1.17
    │       │   └── tonic v0.13.1 (*)
    │       ├── tokio-util v0.7.15
    │       │   ├── combine v4.6.7 (*)
    │       │   ├── h2 v0.4.10 (*)
    │       │   ├── redis v0.29.5 (*)
    │       │   ├── redis v0.31.0 (*)
    │       │   └── tower v0.5.2
    │       │       ├── axum v0.8.4
    │       │       │   └── tonic v0.13.1 (*)
    │       │       └── tonic v0.13.1 (*)
    │       ├── tonic v0.13.1 (*)
    │       └── tower v0.5.2 (*)
    ├── parking_lot_core v0.9.11
    │   ├── dashmap v6.1.0
    │   │   └── rustycluster v2.2.7 (*)
    │   └── parking_lot v0.12.4
    │       ├── prometheus v0.14.0
    │       │   └── rustycluster v2.2.7 (*)
    │       ├── rustycluster v2.2.7 (*)
    │       └── tokio v1.45.1 (*)
    ├── windows-sys v0.52.0
    │   ├── socket2 v0.5.10
    │   │   ├── hyper-util v0.1.14 (*)
    │   │   ├── redis v0.29.5 (*)
    │   │   ├── redis v0.31.0 (*)
    │   │   ├── tokio v1.45.1 (*)
    │   │   └── tonic v0.13.1 (*)
    │   └── tokio v1.45.1 (*)
    └── windows-sys v0.59.0
        ├── mio v1.0.4
        │   └── tokio v1.45.1 (*)
        ├── rustix v1.0.7
        │   └── tempfile v3.20.0
        │       └── prost-build v0.13.5
        │           └── tonic-build v0.13.1
        │               └── (build) rustycluster v2.2.7 (*)
        └── tempfile v3.20.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows-targets@0.53.2:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows-targets v0.53.2
    └── windows-sys v0.60.2
        └── errno v0.3.13
            └── rustix v1.0.7
                └── tempfile v3.20.0
                    └── prost-build v0.13.5
                        └── tonic-build v0.13.1
                            └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_aarch64_gnullvm@0.52.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_aarch64_gnullvm v0.52.6
    └── windows-targets v0.52.6
        ├── backtrace v0.3.75
        │   └── tokio v1.45.1
        │       ├── combine v4.6.7
        │       │   ├── redis v0.29.5
        │       │   │   └── deadpool-redis v0.20.0
        │       │   │       └── rustycluster v2.2.7
        │       │   └── redis v0.31.0
        │       │       └── rustycluster v2.2.7 (*)
        │       ├── deadpool v0.12.2
        │       │   └── deadpool-redis v0.20.0 (*)
        │       ├── deadpool-runtime v0.1.4
        │       │   └── deadpool v0.12.2 (*)
        │       ├── h2 v0.4.10
        │       │   ├── hyper v1.6.0
        │       │   │   ├── hyper-timeout v0.5.2
        │       │   │   │   └── tonic v0.13.1
        │       │   │   │       └── rustycluster v2.2.7 (*)
        │       │   │   ├── hyper-util v0.1.14
        │       │   │   │   ├── hyper-timeout v0.5.2 (*)
        │       │   │   │   └── tonic v0.13.1 (*)
        │       │   │   └── tonic v0.13.1 (*)
        │       │   └── tonic v0.13.1 (*)
        │       ├── hyper v1.6.0 (*)
        │       ├── hyper-timeout v0.5.2 (*)
        │       ├── hyper-util v0.1.14 (*)
        │       ├── redis v0.29.5 (*)
        │       ├── redis v0.31.0 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       ├── tokio-stream v0.1.17
        │       │   └── tonic v0.13.1 (*)
        │       ├── tokio-util v0.7.15
        │       │   ├── combine v4.6.7 (*)
        │       │   ├── h2 v0.4.10 (*)
        │       │   ├── redis v0.29.5 (*)
        │       │   ├── redis v0.31.0 (*)
        │       │   └── tower v0.5.2
        │       │       ├── axum v0.8.4
        │       │       │   └── tonic v0.13.1 (*)
        │       │       └── tonic v0.13.1 (*)
        │       ├── tonic v0.13.1 (*)
        │       └── tower v0.5.2 (*)
        ├── parking_lot_core v0.9.11
        │   ├── dashmap v6.1.0
        │   │   └── rustycluster v2.2.7 (*)
        │   └── parking_lot v0.12.4
        │       ├── prometheus v0.14.0
        │       │   └── rustycluster v2.2.7 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       └── tokio v1.45.1 (*)
        ├── windows-sys v0.52.0
        │   ├── socket2 v0.5.10
        │   │   ├── hyper-util v0.1.14 (*)
        │   │   ├── redis v0.29.5 (*)
        │   │   ├── redis v0.31.0 (*)
        │   │   ├── tokio v1.45.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tokio v1.45.1 (*)
        └── windows-sys v0.59.0
            ├── mio v1.0.4
            │   └── tokio v1.45.1 (*)
            ├── rustix v1.0.7
            │   └── tempfile v3.20.0
            │       └── prost-build v0.13.5
            │           └── tonic-build v0.13.1
            │               └── (build) rustycluster v2.2.7 (*)
            └── tempfile v3.20.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_aarch64_gnullvm@0.53.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_aarch64_gnullvm v0.53.0
    └── windows-targets v0.53.2
        └── windows-sys v0.60.2
            └── errno v0.3.13
                └── rustix v1.0.7
                    └── tempfile v3.20.0
                        └── prost-build v0.13.5
                            └── tonic-build v0.13.1
                                └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_aarch64_msvc@0.52.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_aarch64_msvc v0.52.6
    └── windows-targets v0.52.6
        ├── backtrace v0.3.75
        │   └── tokio v1.45.1
        │       ├── combine v4.6.7
        │       │   ├── redis v0.29.5
        │       │   │   └── deadpool-redis v0.20.0
        │       │   │       └── rustycluster v2.2.7
        │       │   └── redis v0.31.0
        │       │       └── rustycluster v2.2.7 (*)
        │       ├── deadpool v0.12.2
        │       │   └── deadpool-redis v0.20.0 (*)
        │       ├── deadpool-runtime v0.1.4
        │       │   └── deadpool v0.12.2 (*)
        │       ├── h2 v0.4.10
        │       │   ├── hyper v1.6.0
        │       │   │   ├── hyper-timeout v0.5.2
        │       │   │   │   └── tonic v0.13.1
        │       │   │   │       └── rustycluster v2.2.7 (*)
        │       │   │   ├── hyper-util v0.1.14
        │       │   │   │   ├── hyper-timeout v0.5.2 (*)
        │       │   │   │   └── tonic v0.13.1 (*)
        │       │   │   └── tonic v0.13.1 (*)
        │       │   └── tonic v0.13.1 (*)
        │       ├── hyper v1.6.0 (*)
        │       ├── hyper-timeout v0.5.2 (*)
        │       ├── hyper-util v0.1.14 (*)
        │       ├── redis v0.29.5 (*)
        │       ├── redis v0.31.0 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       ├── tokio-stream v0.1.17
        │       │   └── tonic v0.13.1 (*)
        │       ├── tokio-util v0.7.15
        │       │   ├── combine v4.6.7 (*)
        │       │   ├── h2 v0.4.10 (*)
        │       │   ├── redis v0.29.5 (*)
        │       │   ├── redis v0.31.0 (*)
        │       │   └── tower v0.5.2
        │       │       ├── axum v0.8.4
        │       │       │   └── tonic v0.13.1 (*)
        │       │       └── tonic v0.13.1 (*)
        │       ├── tonic v0.13.1 (*)
        │       └── tower v0.5.2 (*)
        ├── parking_lot_core v0.9.11
        │   ├── dashmap v6.1.0
        │   │   └── rustycluster v2.2.7 (*)
        │   └── parking_lot v0.12.4
        │       ├── prometheus v0.14.0
        │       │   └── rustycluster v2.2.7 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       └── tokio v1.45.1 (*)
        ├── windows-sys v0.52.0
        │   ├── socket2 v0.5.10
        │   │   ├── hyper-util v0.1.14 (*)
        │   │   ├── redis v0.29.5 (*)
        │   │   ├── redis v0.31.0 (*)
        │   │   ├── tokio v1.45.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tokio v1.45.1 (*)
        └── windows-sys v0.59.0
            ├── mio v1.0.4
            │   └── tokio v1.45.1 (*)
            ├── rustix v1.0.7
            │   └── tempfile v3.20.0
            │       └── prost-build v0.13.5
            │           └── tonic-build v0.13.1
            │               └── (build) rustycluster v2.2.7 (*)
            └── tempfile v3.20.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_aarch64_msvc@0.53.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_aarch64_msvc v0.53.0
    └── windows-targets v0.53.2
        └── windows-sys v0.60.2
            └── errno v0.3.13
                └── rustix v1.0.7
                    └── tempfile v3.20.0
                        └── prost-build v0.13.5
                            └── tonic-build v0.13.1
                                └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_i686_gnu@0.52.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_i686_gnu v0.52.6
    └── windows-targets v0.52.6
        ├── backtrace v0.3.75
        │   └── tokio v1.45.1
        │       ├── combine v4.6.7
        │       │   ├── redis v0.29.5
        │       │   │   └── deadpool-redis v0.20.0
        │       │   │       └── rustycluster v2.2.7
        │       │   └── redis v0.31.0
        │       │       └── rustycluster v2.2.7 (*)
        │       ├── deadpool v0.12.2
        │       │   └── deadpool-redis v0.20.0 (*)
        │       ├── deadpool-runtime v0.1.4
        │       │   └── deadpool v0.12.2 (*)
        │       ├── h2 v0.4.10
        │       │   ├── hyper v1.6.0
        │       │   │   ├── hyper-timeout v0.5.2
        │       │   │   │   └── tonic v0.13.1
        │       │   │   │       └── rustycluster v2.2.7 (*)
        │       │   │   ├── hyper-util v0.1.14
        │       │   │   │   ├── hyper-timeout v0.5.2 (*)
        │       │   │   │   └── tonic v0.13.1 (*)
        │       │   │   └── tonic v0.13.1 (*)
        │       │   └── tonic v0.13.1 (*)
        │       ├── hyper v1.6.0 (*)
        │       ├── hyper-timeout v0.5.2 (*)
        │       ├── hyper-util v0.1.14 (*)
        │       ├── redis v0.29.5 (*)
        │       ├── redis v0.31.0 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       ├── tokio-stream v0.1.17
        │       │   └── tonic v0.13.1 (*)
        │       ├── tokio-util v0.7.15
        │       │   ├── combine v4.6.7 (*)
        │       │   ├── h2 v0.4.10 (*)
        │       │   ├── redis v0.29.5 (*)
        │       │   ├── redis v0.31.0 (*)
        │       │   └── tower v0.5.2
        │       │       ├── axum v0.8.4
        │       │       │   └── tonic v0.13.1 (*)
        │       │       └── tonic v0.13.1 (*)
        │       ├── tonic v0.13.1 (*)
        │       └── tower v0.5.2 (*)
        ├── parking_lot_core v0.9.11
        │   ├── dashmap v6.1.0
        │   │   └── rustycluster v2.2.7 (*)
        │   └── parking_lot v0.12.4
        │       ├── prometheus v0.14.0
        │       │   └── rustycluster v2.2.7 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       └── tokio v1.45.1 (*)
        ├── windows-sys v0.52.0
        │   ├── socket2 v0.5.10
        │   │   ├── hyper-util v0.1.14 (*)
        │   │   ├── redis v0.29.5 (*)
        │   │   ├── redis v0.31.0 (*)
        │   │   ├── tokio v1.45.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tokio v1.45.1 (*)
        └── windows-sys v0.59.0
            ├── mio v1.0.4
            │   └── tokio v1.45.1 (*)
            ├── rustix v1.0.7
            │   └── tempfile v3.20.0
            │       └── prost-build v0.13.5
            │           └── tonic-build v0.13.1
            │               └── (build) rustycluster v2.2.7 (*)
            └── tempfile v3.20.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_i686_gnu@0.53.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_i686_gnu v0.53.0
    └── windows-targets v0.53.2
        └── windows-sys v0.60.2
            └── errno v0.3.13
                └── rustix v1.0.7
                    └── tempfile v3.20.0
                        └── prost-build v0.13.5
                            └── tonic-build v0.13.1
                                └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_i686_gnullvm@0.52.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_i686_gnullvm v0.52.6
    └── windows-targets v0.52.6
        ├── backtrace v0.3.75
        │   └── tokio v1.45.1
        │       ├── combine v4.6.7
        │       │   ├── redis v0.29.5
        │       │   │   └── deadpool-redis v0.20.0
        │       │   │       └── rustycluster v2.2.7
        │       │   └── redis v0.31.0
        │       │       └── rustycluster v2.2.7 (*)
        │       ├── deadpool v0.12.2
        │       │   └── deadpool-redis v0.20.0 (*)
        │       ├── deadpool-runtime v0.1.4
        │       │   └── deadpool v0.12.2 (*)
        │       ├── h2 v0.4.10
        │       │   ├── hyper v1.6.0
        │       │   │   ├── hyper-timeout v0.5.2
        │       │   │   │   └── tonic v0.13.1
        │       │   │   │       └── rustycluster v2.2.7 (*)
        │       │   │   ├── hyper-util v0.1.14
        │       │   │   │   ├── hyper-timeout v0.5.2 (*)
        │       │   │   │   └── tonic v0.13.1 (*)
        │       │   │   └── tonic v0.13.1 (*)
        │       │   └── tonic v0.13.1 (*)
        │       ├── hyper v1.6.0 (*)
        │       ├── hyper-timeout v0.5.2 (*)
        │       ├── hyper-util v0.1.14 (*)
        │       ├── redis v0.29.5 (*)
        │       ├── redis v0.31.0 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       ├── tokio-stream v0.1.17
        │       │   └── tonic v0.13.1 (*)
        │       ├── tokio-util v0.7.15
        │       │   ├── combine v4.6.7 (*)
        │       │   ├── h2 v0.4.10 (*)
        │       │   ├── redis v0.29.5 (*)
        │       │   ├── redis v0.31.0 (*)
        │       │   └── tower v0.5.2
        │       │       ├── axum v0.8.4
        │       │       │   └── tonic v0.13.1 (*)
        │       │       └── tonic v0.13.1 (*)
        │       ├── tonic v0.13.1 (*)
        │       └── tower v0.5.2 (*)
        ├── parking_lot_core v0.9.11
        │   ├── dashmap v6.1.0
        │   │   └── rustycluster v2.2.7 (*)
        │   └── parking_lot v0.12.4
        │       ├── prometheus v0.14.0
        │       │   └── rustycluster v2.2.7 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       └── tokio v1.45.1 (*)
        ├── windows-sys v0.52.0
        │   ├── socket2 v0.5.10
        │   │   ├── hyper-util v0.1.14 (*)
        │   │   ├── redis v0.29.5 (*)
        │   │   ├── redis v0.31.0 (*)
        │   │   ├── tokio v1.45.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tokio v1.45.1 (*)
        └── windows-sys v0.59.0
            ├── mio v1.0.4
            │   └── tokio v1.45.1 (*)
            ├── rustix v1.0.7
            │   └── tempfile v3.20.0
            │       └── prost-build v0.13.5
            │           └── tonic-build v0.13.1
            │               └── (build) rustycluster v2.2.7 (*)
            └── tempfile v3.20.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_i686_gnullvm@0.53.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_i686_gnullvm v0.53.0
    └── windows-targets v0.53.2
        └── windows-sys v0.60.2
            └── errno v0.3.13
                └── rustix v1.0.7
                    └── tempfile v3.20.0
                        └── prost-build v0.13.5
                            └── tonic-build v0.13.1
                                └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_i686_msvc@0.52.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_i686_msvc v0.52.6
    └── windows-targets v0.52.6
        ├── backtrace v0.3.75
        │   └── tokio v1.45.1
        │       ├── combine v4.6.7
        │       │   ├── redis v0.29.5
        │       │   │   └── deadpool-redis v0.20.0
        │       │   │       └── rustycluster v2.2.7
        │       │   └── redis v0.31.0
        │       │       └── rustycluster v2.2.7 (*)
        │       ├── deadpool v0.12.2
        │       │   └── deadpool-redis v0.20.0 (*)
        │       ├── deadpool-runtime v0.1.4
        │       │   └── deadpool v0.12.2 (*)
        │       ├── h2 v0.4.10
        │       │   ├── hyper v1.6.0
        │       │   │   ├── hyper-timeout v0.5.2
        │       │   │   │   └── tonic v0.13.1
        │       │   │   │       └── rustycluster v2.2.7 (*)
        │       │   │   ├── hyper-util v0.1.14
        │       │   │   │   ├── hyper-timeout v0.5.2 (*)
        │       │   │   │   └── tonic v0.13.1 (*)
        │       │   │   └── tonic v0.13.1 (*)
        │       │   └── tonic v0.13.1 (*)
        │       ├── hyper v1.6.0 (*)
        │       ├── hyper-timeout v0.5.2 (*)
        │       ├── hyper-util v0.1.14 (*)
        │       ├── redis v0.29.5 (*)
        │       ├── redis v0.31.0 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       ├── tokio-stream v0.1.17
        │       │   └── tonic v0.13.1 (*)
        │       ├── tokio-util v0.7.15
        │       │   ├── combine v4.6.7 (*)
        │       │   ├── h2 v0.4.10 (*)
        │       │   ├── redis v0.29.5 (*)
        │       │   ├── redis v0.31.0 (*)
        │       │   └── tower v0.5.2
        │       │       ├── axum v0.8.4
        │       │       │   └── tonic v0.13.1 (*)
        │       │       └── tonic v0.13.1 (*)
        │       ├── tonic v0.13.1 (*)
        │       └── tower v0.5.2 (*)
        ├── parking_lot_core v0.9.11
        │   ├── dashmap v6.1.0
        │   │   └── rustycluster v2.2.7 (*)
        │   └── parking_lot v0.12.4
        │       ├── prometheus v0.14.0
        │       │   └── rustycluster v2.2.7 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       └── tokio v1.45.1 (*)
        ├── windows-sys v0.52.0
        │   ├── socket2 v0.5.10
        │   │   ├── hyper-util v0.1.14 (*)
        │   │   ├── redis v0.29.5 (*)
        │   │   ├── redis v0.31.0 (*)
        │   │   ├── tokio v1.45.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tokio v1.45.1 (*)
        └── windows-sys v0.59.0
            ├── mio v1.0.4
            │   └── tokio v1.45.1 (*)
            ├── rustix v1.0.7
            │   └── tempfile v3.20.0
            │       └── prost-build v0.13.5
            │           └── tonic-build v0.13.1
            │               └── (build) rustycluster v2.2.7 (*)
            └── tempfile v3.20.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_i686_msvc@0.53.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_i686_msvc v0.53.0
    └── windows-targets v0.53.2
        └── windows-sys v0.60.2
            └── errno v0.3.13
                └── rustix v1.0.7
                    └── tempfile v3.20.0
                        └── prost-build v0.13.5
                            └── tonic-build v0.13.1
                                └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_gnu@0.52.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_x86_64_gnu v0.52.6
    └── windows-targets v0.52.6
        ├── backtrace v0.3.75
        │   └── tokio v1.45.1
        │       ├── combine v4.6.7
        │       │   ├── redis v0.29.5
        │       │   │   └── deadpool-redis v0.20.0
        │       │   │       └── rustycluster v2.2.7
        │       │   └── redis v0.31.0
        │       │       └── rustycluster v2.2.7 (*)
        │       ├── deadpool v0.12.2
        │       │   └── deadpool-redis v0.20.0 (*)
        │       ├── deadpool-runtime v0.1.4
        │       │   └── deadpool v0.12.2 (*)
        │       ├── h2 v0.4.10
        │       │   ├── hyper v1.6.0
        │       │   │   ├── hyper-timeout v0.5.2
        │       │   │   │   └── tonic v0.13.1
        │       │   │   │       └── rustycluster v2.2.7 (*)
        │       │   │   ├── hyper-util v0.1.14
        │       │   │   │   ├── hyper-timeout v0.5.2 (*)
        │       │   │   │   └── tonic v0.13.1 (*)
        │       │   │   └── tonic v0.13.1 (*)
        │       │   └── tonic v0.13.1 (*)
        │       ├── hyper v1.6.0 (*)
        │       ├── hyper-timeout v0.5.2 (*)
        │       ├── hyper-util v0.1.14 (*)
        │       ├── redis v0.29.5 (*)
        │       ├── redis v0.31.0 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       ├── tokio-stream v0.1.17
        │       │   └── tonic v0.13.1 (*)
        │       ├── tokio-util v0.7.15
        │       │   ├── combine v4.6.7 (*)
        │       │   ├── h2 v0.4.10 (*)
        │       │   ├── redis v0.29.5 (*)
        │       │   ├── redis v0.31.0 (*)
        │       │   └── tower v0.5.2
        │       │       ├── axum v0.8.4
        │       │       │   └── tonic v0.13.1 (*)
        │       │       └── tonic v0.13.1 (*)
        │       ├── tonic v0.13.1 (*)
        │       └── tower v0.5.2 (*)
        ├── parking_lot_core v0.9.11
        │   ├── dashmap v6.1.0
        │   │   └── rustycluster v2.2.7 (*)
        │   └── parking_lot v0.12.4
        │       ├── prometheus v0.14.0
        │       │   └── rustycluster v2.2.7 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       └── tokio v1.45.1 (*)
        ├── windows-sys v0.52.0
        │   ├── socket2 v0.5.10
        │   │   ├── hyper-util v0.1.14 (*)
        │   │   ├── redis v0.29.5 (*)
        │   │   ├── redis v0.31.0 (*)
        │   │   ├── tokio v1.45.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tokio v1.45.1 (*)
        └── windows-sys v0.59.0
            ├── mio v1.0.4
            │   └── tokio v1.45.1 (*)
            ├── rustix v1.0.7
            │   └── tempfile v3.20.0
            │       └── prost-build v0.13.5
            │           └── tonic-build v0.13.1
            │               └── (build) rustycluster v2.2.7 (*)
            └── tempfile v3.20.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_gnu@0.53.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_x86_64_gnu v0.53.0
    └── windows-targets v0.53.2
        └── windows-sys v0.60.2
            └── errno v0.3.13
                └── rustix v1.0.7
                    └── tempfile v3.20.0
                        └── prost-build v0.13.5
                            └── tonic-build v0.13.1
                                └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_gnullvm@0.52.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_x86_64_gnullvm v0.52.6
    └── windows-targets v0.52.6
        ├── backtrace v0.3.75
        │   └── tokio v1.45.1
        │       ├── combine v4.6.7
        │       │   ├── redis v0.29.5
        │       │   │   └── deadpool-redis v0.20.0
        │       │   │       └── rustycluster v2.2.7
        │       │   └── redis v0.31.0
        │       │       └── rustycluster v2.2.7 (*)
        │       ├── deadpool v0.12.2
        │       │   └── deadpool-redis v0.20.0 (*)
        │       ├── deadpool-runtime v0.1.4
        │       │   └── deadpool v0.12.2 (*)
        │       ├── h2 v0.4.10
        │       │   ├── hyper v1.6.0
        │       │   │   ├── hyper-timeout v0.5.2
        │       │   │   │   └── tonic v0.13.1
        │       │   │   │       └── rustycluster v2.2.7 (*)
        │       │   │   ├── hyper-util v0.1.14
        │       │   │   │   ├── hyper-timeout v0.5.2 (*)
        │       │   │   │   └── tonic v0.13.1 (*)
        │       │   │   └── tonic v0.13.1 (*)
        │       │   └── tonic v0.13.1 (*)
        │       ├── hyper v1.6.0 (*)
        │       ├── hyper-timeout v0.5.2 (*)
        │       ├── hyper-util v0.1.14 (*)
        │       ├── redis v0.29.5 (*)
        │       ├── redis v0.31.0 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       ├── tokio-stream v0.1.17
        │       │   └── tonic v0.13.1 (*)
        │       ├── tokio-util v0.7.15
        │       │   ├── combine v4.6.7 (*)
        │       │   ├── h2 v0.4.10 (*)
        │       │   ├── redis v0.29.5 (*)
        │       │   ├── redis v0.31.0 (*)
        │       │   └── tower v0.5.2
        │       │       ├── axum v0.8.4
        │       │       │   └── tonic v0.13.1 (*)
        │       │       └── tonic v0.13.1 (*)
        │       ├── tonic v0.13.1 (*)
        │       └── tower v0.5.2 (*)
        ├── parking_lot_core v0.9.11
        │   ├── dashmap v6.1.0
        │   │   └── rustycluster v2.2.7 (*)
        │   └── parking_lot v0.12.4
        │       ├── prometheus v0.14.0
        │       │   └── rustycluster v2.2.7 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       └── tokio v1.45.1 (*)
        ├── windows-sys v0.52.0
        │   ├── socket2 v0.5.10
        │   │   ├── hyper-util v0.1.14 (*)
        │   │   ├── redis v0.29.5 (*)
        │   │   ├── redis v0.31.0 (*)
        │   │   ├── tokio v1.45.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tokio v1.45.1 (*)
        └── windows-sys v0.59.0
            ├── mio v1.0.4
            │   └── tokio v1.45.1 (*)
            ├── rustix v1.0.7
            │   └── tempfile v3.20.0
            │       └── prost-build v0.13.5
            │           └── tonic-build v0.13.1
            │               └── (build) rustycluster v2.2.7 (*)
            └── tempfile v3.20.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_gnullvm@0.53.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_x86_64_gnullvm v0.53.0
    └── windows-targets v0.53.2
        └── windows-sys v0.60.2
            └── errno v0.3.13
                └── rustix v1.0.7
                    └── tempfile v3.20.0
                        └── prost-build v0.13.5
                            └── tonic-build v0.13.1
                                └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.52.6:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_x86_64_msvc v0.52.6
    └── windows-targets v0.52.6
        ├── backtrace v0.3.75
        │   └── tokio v1.45.1
        │       ├── combine v4.6.7
        │       │   ├── redis v0.29.5
        │       │   │   └── deadpool-redis v0.20.0
        │       │   │       └── rustycluster v2.2.7
        │       │   └── redis v0.31.0
        │       │       └── rustycluster v2.2.7 (*)
        │       ├── deadpool v0.12.2
        │       │   └── deadpool-redis v0.20.0 (*)
        │       ├── deadpool-runtime v0.1.4
        │       │   └── deadpool v0.12.2 (*)
        │       ├── h2 v0.4.10
        │       │   ├── hyper v1.6.0
        │       │   │   ├── hyper-timeout v0.5.2
        │       │   │   │   └── tonic v0.13.1
        │       │   │   │       └── rustycluster v2.2.7 (*)
        │       │   │   ├── hyper-util v0.1.14
        │       │   │   │   ├── hyper-timeout v0.5.2 (*)
        │       │   │   │   └── tonic v0.13.1 (*)
        │       │   │   └── tonic v0.13.1 (*)
        │       │   └── tonic v0.13.1 (*)
        │       ├── hyper v1.6.0 (*)
        │       ├── hyper-timeout v0.5.2 (*)
        │       ├── hyper-util v0.1.14 (*)
        │       ├── redis v0.29.5 (*)
        │       ├── redis v0.31.0 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       ├── tokio-stream v0.1.17
        │       │   └── tonic v0.13.1 (*)
        │       ├── tokio-util v0.7.15
        │       │   ├── combine v4.6.7 (*)
        │       │   ├── h2 v0.4.10 (*)
        │       │   ├── redis v0.29.5 (*)
        │       │   ├── redis v0.31.0 (*)
        │       │   └── tower v0.5.2
        │       │       ├── axum v0.8.4
        │       │       │   └── tonic v0.13.1 (*)
        │       │       └── tonic v0.13.1 (*)
        │       ├── tonic v0.13.1 (*)
        │       └── tower v0.5.2 (*)
        ├── parking_lot_core v0.9.11
        │   ├── dashmap v6.1.0
        │   │   └── rustycluster v2.2.7 (*)
        │   └── parking_lot v0.12.4
        │       ├── prometheus v0.14.0
        │       │   └── rustycluster v2.2.7 (*)
        │       ├── rustycluster v2.2.7 (*)
        │       └── tokio v1.45.1 (*)
        ├── windows-sys v0.52.0
        │   ├── socket2 v0.5.10
        │   │   ├── hyper-util v0.1.14 (*)
        │   │   ├── redis v0.29.5 (*)
        │   │   ├── redis v0.31.0 (*)
        │   │   ├── tokio v1.45.1 (*)
        │   │   └── tonic v0.13.1 (*)
        │   └── tokio v1.45.1 (*)
        └── windows-sys v0.59.0
            ├── mio v1.0.4
            │   └── tokio v1.45.1 (*)
            ├── rustix v1.0.7
            │   └── tempfile v3.20.0
            │       └── prost-build v0.13.5
            │           └── tonic-build v0.13.1
            │               └── (build) rustycluster v2.2.7 (*)
            └── tempfile v3.20.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#windows_x86_64_msvc@0.53.0:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ windows_x86_64_msvc v0.53.0
    └── windows-targets v0.53.2
        └── windows-sys v0.60.2
            └── errno v0.3.13
                └── rustix v1.0.7
                    └── tempfile v3.20.0
                        └── prost-build v0.13.5
                            └── tonic-build v0.13.1
                                └── (build) rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#wit-bindgen-rt@0.39.0:4:12
  │
4 │ license = "Apache-2.0 WITH LLVM-exception OR Apache-2.0 OR MIT"
  │            ━━━━━━━━━━────────────────────────━━━━━━━━━━────━━━
  │            │                                 │             │
  │            │                                 │             rejected: license is not explicitly allowed
  │            │                                 rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ wit-bindgen-rt v0.39.0
    └── wasi v0.14.2+wasi-0.2.4
        └── getrandom v0.3.3
            ├── tempfile v3.20.0
            │   └── prost-build v0.13.5
            │       └── tonic-build v0.13.1
            │           └── (build) rustycluster v2.2.7
            └── uuid v1.17.0
                └── rustycluster v2.2.7 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#writeable@0.6.1:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ writeable v0.6.1
    ├── icu_locale_core v2.0.0
    │   ├── icu_properties v2.0.1
    │   │   └── idna_adapter v1.2.1
    │   │       └── idna v1.0.3
    │   │           └── url v2.5.4
    │   │               ├── redis v0.29.5
    │   │               │   └── deadpool-redis v0.20.0
    │   │               │       └── rustycluster v2.2.7
    │   │               └── redis v0.31.0
    │   │                   └── rustycluster v2.2.7 (*)
    │   └── icu_provider v2.0.0
    │       ├── icu_normalizer v2.0.0
    │       │   └── idna_adapter v1.2.1 (*)
    │       └── icu_properties v2.0.1 (*)
    └── icu_provider v2.0.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#yaml-rust@0.4.5:4:12
  │
4 │ license = "MIT OR Apache-2.0"
  │            ━━━────━━━━━━━━━━
  │            │      │
  │            │      rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ yaml-rust v0.4.5
    └── config v0.13.4
        └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#yoke@0.8.0:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ yoke v0.8.0
    ├── icu_collections v2.0.0
    │   ├── icu_normalizer v2.0.0
    │   │   └── idna_adapter v1.2.1
    │   │       └── idna v1.0.3
    │   │           └── url v2.5.4
    │   │               ├── redis v0.29.5
    │   │               │   └── deadpool-redis v0.20.0
    │   │               │       └── rustycluster v2.2.7
    │   │               └── redis v0.31.0
    │   │                   └── rustycluster v2.2.7 (*)
    │   └── icu_properties v2.0.1
    │       └── idna_adapter v1.2.1 (*)
    ├── icu_provider v2.0.0
    │   ├── icu_normalizer v2.0.0 (*)
    │   └── icu_properties v2.0.1 (*)
    ├── zerotrie v0.2.2
    │   ├── icu_properties v2.0.1 (*)
    │   └── icu_provider v2.0.0 (*)
    └── zerovec v0.11.2
        ├── icu_collections v2.0.0 (*)
        ├── icu_locale_core v2.0.0
        │   ├── icu_properties v2.0.1 (*)
        │   └── icu_provider v2.0.0 (*)
        ├── icu_normalizer v2.0.0 (*)
        ├── icu_properties v2.0.1 (*)
        ├── icu_provider v2.0.0 (*)
        ├── potential_utf v0.1.2
        │   ├── icu_collections v2.0.0 (*)
        │   └── icu_properties v2.0.1 (*)
        └── tinystr v0.8.1
            ├── icu_locale_core v2.0.0 (*)
            └── icu_provider v2.0.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#yoke-derive@0.8.0:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ yoke-derive v0.8.0
    └── yoke v0.8.0
        ├── icu_collections v2.0.0
        │   ├── icu_normalizer v2.0.0
        │   │   └── idna_adapter v1.2.1
        │   │       └── idna v1.0.3
        │   │           └── url v2.5.4
        │   │               ├── redis v0.29.5
        │   │               │   └── deadpool-redis v0.20.0
        │   │               │       └── rustycluster v2.2.7
        │   │               └── redis v0.31.0
        │   │                   └── rustycluster v2.2.7 (*)
        │   └── icu_properties v2.0.1
        │       └── idna_adapter v1.2.1 (*)
        ├── icu_provider v2.0.0
        │   ├── icu_normalizer v2.0.0 (*)
        │   └── icu_properties v2.0.1 (*)
        ├── zerotrie v0.2.2
        │   ├── icu_properties v2.0.1 (*)
        │   └── icu_provider v2.0.0 (*)
        └── zerovec v0.11.2
            ├── icu_collections v2.0.0 (*)
            ├── icu_locale_core v2.0.0
            │   ├── icu_properties v2.0.1 (*)
            │   └── icu_provider v2.0.0 (*)
            ├── icu_normalizer v2.0.0 (*)
            ├── icu_properties v2.0.1 (*)
            ├── icu_provider v2.0.0 (*)
            ├── potential_utf v0.1.2
            │   ├── icu_collections v2.0.0 (*)
            │   └── icu_properties v2.0.1 (*)
            └── tinystr v0.8.1
                ├── icu_locale_core v2.0.0 (*)
                └── icu_provider v2.0.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#zerocopy@0.8.26:4:12
  │
4 │ license = "BSD-2-Clause OR Apache-2.0 OR MIT"
  │            ━━━━━━━━━━━━────━━━━━━━━━━────━━━
  │            │               │             │
  │            │               │             rejected: license is not explicitly allowed
  │            │               rejected: license is not explicitly allowed
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ BSD-2-Clause - BSD 2-Clause "Simplified" License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ Apache-2.0 - Apache License 2.0:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ MIT - MIT License:
  ├   - OSI approved
  ├   - FSF Free/Libre
  ├ zerocopy v0.8.26
    └── ppv-lite86 v0.2.21
        └── rand_chacha v0.3.1
            └── rand v0.8.5
                └── rustycluster v2.2.7

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#zerofrom@0.1.6:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ zerofrom v0.1.6
    ├── icu_collections v2.0.0
    │   ├── icu_normalizer v2.0.0
    │   │   └── idna_adapter v1.2.1
    │   │       └── idna v1.0.3
    │   │           └── url v2.5.4
    │   │               ├── redis v0.29.5
    │   │               │   └── deadpool-redis v0.20.0
    │   │               │       └── rustycluster v2.2.7
    │   │               └── redis v0.31.0
    │   │                   └── rustycluster v2.2.7 (*)
    │   └── icu_properties v2.0.1
    │       └── idna_adapter v1.2.1 (*)
    ├── icu_provider v2.0.0
    │   ├── icu_normalizer v2.0.0 (*)
    │   └── icu_properties v2.0.1 (*)
    ├── yoke v0.8.0
    │   ├── icu_collections v2.0.0 (*)
    │   ├── icu_provider v2.0.0 (*)
    │   ├── zerotrie v0.2.2
    │   │   ├── icu_properties v2.0.1 (*)
    │   │   └── icu_provider v2.0.0 (*)
    │   └── zerovec v0.11.2
    │       ├── icu_collections v2.0.0 (*)
    │       ├── icu_locale_core v2.0.0
    │       │   ├── icu_properties v2.0.1 (*)
    │       │   └── icu_provider v2.0.0 (*)
    │       ├── icu_normalizer v2.0.0 (*)
    │       ├── icu_properties v2.0.1 (*)
    │       ├── icu_provider v2.0.0 (*)
    │       ├── potential_utf v0.1.2
    │       │   ├── icu_collections v2.0.0 (*)
    │       │   └── icu_properties v2.0.1 (*)
    │       └── tinystr v0.8.1
    │           ├── icu_locale_core v2.0.0 (*)
    │           └── icu_provider v2.0.0 (*)
    ├── zerotrie v0.2.2 (*)
    └── zerovec v0.11.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#zerofrom-derive@0.1.6:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ zerofrom-derive v0.1.6
    └── zerofrom v0.1.6
        ├── icu_collections v2.0.0
        │   ├── icu_normalizer v2.0.0
        │   │   └── idna_adapter v1.2.1
        │   │       └── idna v1.0.3
        │   │           └── url v2.5.4
        │   │               ├── redis v0.29.5
        │   │               │   └── deadpool-redis v0.20.0
        │   │               │       └── rustycluster v2.2.7
        │   │               └── redis v0.31.0
        │   │                   └── rustycluster v2.2.7 (*)
        │   └── icu_properties v2.0.1
        │       └── idna_adapter v1.2.1 (*)
        ├── icu_provider v2.0.0
        │   ├── icu_normalizer v2.0.0 (*)
        │   └── icu_properties v2.0.1 (*)
        ├── yoke v0.8.0
        │   ├── icu_collections v2.0.0 (*)
        │   ├── icu_provider v2.0.0 (*)
        │   ├── zerotrie v0.2.2
        │   │   ├── icu_properties v2.0.1 (*)
        │   │   └── icu_provider v2.0.0 (*)
        │   └── zerovec v0.11.2
        │       ├── icu_collections v2.0.0 (*)
        │       ├── icu_locale_core v2.0.0
        │       │   ├── icu_properties v2.0.1 (*)
        │       │   └── icu_provider v2.0.0 (*)
        │       ├── icu_normalizer v2.0.0 (*)
        │       ├── icu_properties v2.0.1 (*)
        │       ├── icu_provider v2.0.0 (*)
        │       ├── potential_utf v0.1.2
        │       │   ├── icu_collections v2.0.0 (*)
        │       │   └── icu_properties v2.0.1 (*)
        │       └── tinystr v0.8.1
        │           ├── icu_locale_core v2.0.0 (*)
        │           └── icu_provider v2.0.0 (*)
        ├── zerotrie v0.2.2 (*)
        └── zerovec v0.11.2 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#zerotrie@0.2.2:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ zerotrie v0.2.2
    ├── icu_properties v2.0.1
    │   └── idna_adapter v1.2.1
    │       └── idna v1.0.3
    │           └── url v2.5.4
    │               ├── redis v0.29.5
    │               │   └── deadpool-redis v0.20.0
    │               │       └── rustycluster v2.2.7
    │               └── redis v0.31.0
    │                   └── rustycluster v2.2.7 (*)
    └── icu_provider v2.0.0
        ├── icu_normalizer v2.0.0
        │   └── idna_adapter v1.2.1 (*)
        └── icu_properties v2.0.1 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#zerovec@0.11.2:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ zerovec v0.11.2
    ├── icu_collections v2.0.0
    │   ├── icu_normalizer v2.0.0
    │   │   └── idna_adapter v1.2.1
    │   │       └── idna v1.0.3
    │   │           └── url v2.5.4
    │   │               ├── redis v0.29.5
    │   │               │   └── deadpool-redis v0.20.0
    │   │               │       └── rustycluster v2.2.7
    │   │               └── redis v0.31.0
    │   │                   └── rustycluster v2.2.7 (*)
    │   └── icu_properties v2.0.1
    │       └── idna_adapter v1.2.1 (*)
    ├── icu_locale_core v2.0.0
    │   ├── icu_properties v2.0.1 (*)
    │   └── icu_provider v2.0.0
    │       ├── icu_normalizer v2.0.0 (*)
    │       └── icu_properties v2.0.1 (*)
    ├── icu_normalizer v2.0.0 (*)
    ├── icu_properties v2.0.1 (*)
    ├── icu_provider v2.0.0 (*)
    ├── potential_utf v0.1.2
    │   ├── icu_collections v2.0.0 (*)
    │   └── icu_properties v2.0.1 (*)
    └── tinystr v0.8.1
        ├── icu_locale_core v2.0.0 (*)
        └── icu_provider v2.0.0 (*)

error[rejected]: failed to satisfy license requirements
  ┌─ registry+https://github.com/rust-lang/crates.io-index#zerovec-derive@0.11.1:4:12
  │
4 │ license = "Unicode-3.0"
  │            ━━━━━━━━━━━
  │            │
  │            license expression retrieved via Cargo.toml `license`
  │            rejected: license is not explicitly allowed
  │
  ├ Unicode-3.0 - Unicode License v3:
  ├   - OSI approved
  ├ zerovec-derive v0.11.1
    └── zerovec v0.11.2
        ├── icu_collections v2.0.0
        │   ├── icu_normalizer v2.0.0
        │   │   └── idna_adapter v1.2.1
        │   │       └── idna v1.0.3
        │   │           └── url v2.5.4
        │   │               ├── redis v0.29.5
        │   │               │   └── deadpool-redis v0.20.0
        │   │               │       └── rustycluster v2.2.7
        │   │               └── redis v0.31.0
        │   │                   └── rustycluster v2.2.7 (*)
        │   └── icu_properties v2.0.1
        │       └── idna_adapter v1.2.1 (*)
        ├── icu_locale_core v2.0.0
        │   ├── icu_properties v2.0.1 (*)
        │   └── icu_provider v2.0.0
        │       ├── icu_normalizer v2.0.0 (*)
        │       └── icu_properties v2.0.1 (*)
        ├── icu_normalizer v2.0.0 (*)
        ├── icu_properties v2.0.1 (*)
        ├── icu_provider v2.0.0 (*)
        ├── potential_utf v0.1.2
        │   ├── icu_collections v2.0.0 (*)
        │   └── icu_properties v2.0.1 (*)
        └── tinystr v0.8.1
            ├── icu_locale_core v2.0.0 (*)
            └── icu_provider v2.0.0 (*)

error[unmaintained]: yaml-rust is unmaintained.
    ┌─ C:\Users\<USER>\Desktop\rust\rustycluster/Cargo.lock:260:1
    │
260 │ yaml-rust 0.4.5 registry+https://github.com/rust-lang/crates.io-index
    │ ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ unmaintained advisory detected
    │
    ├ ID: RUSTSEC-2024-0320
    ├ Advisory: https://rustsec.org/advisories/RUSTSEC-2024-0320
    ├ The maintainer seems [unreachable](https://github.com/chyh1990/yaml-rust/issues/197).
      
      Many issues and pull requests have been submitted over the years
      without any [response](https://github.com/chyh1990/yaml-rust/issues/160).
      
      ## Alternatives
      
      Consider switching to the actively maintained `yaml-rust2` fork of the original project:
      
      - [yaml-rust2](https://github.com/Ethiraric/yaml-rust2)
      - [yaml-rust2 @ crates.io](https://crates.io/crates/yaml-rust2)
    ├ Announcement: https://github.com/rustsec/advisory-db/issues/1921
    ├ Solution: No safe upgrade is available!
    ├ yaml-rust v0.4.5
      └── config v0.13.4
          └── rustycluster v2.2.7

advisories FAILED, bans ok, licenses FAILED, sources ok
