[package]
name = "rustycluster"
version = "2.2.7"
edition = "2024"

[dependencies]
# Logging and tracing
log = "0.4"  # Keep for compatibility with dependencies that use log
tracing = "0.1"  # Core tracing functionality
tracing-subscriber = { version = "0.3", features = ["env-filter", "json", "registry", "chrono"] }
tracing-appender = "0.2"  # For file logging with rotation
tracing-log = "0.1"  # Bridge from log to tracing
console-subscriber = { version = "0.1", optional = true }  # For Tokio Console
lazy_static = "1.4"  # For global state management

# Redis and HTTP
deadpool-redis = "0.20"  # Upgrade from 0.14
redis = { version = "0.31", features = ["aio", "tokio-comp"] }  # Upgrade from 0.24
tokio = { version = "1.38", features = ["full", "tracing"] }  # Added tracing feature

# Configuration and serialization
config = "0.13"
serde = { version = "1.0", features = ["derive"] }
futures = "0.3"
prometheus = "0.14"

# gRPC
tonic = "0.13"
prost = "0.13"
prost-types = "0.13"

# Cryptography
aes-gcm = "0.10"  # AES-GCM encryption
pbkdf2 = "0.12"  # Key derivation
sha2 = "0.10"  # SHA-256 hashing
base64 = "0.21"  # Base64 encoding/decoding
rand = "0.8"  # Random number generation

# Other
num_cpus = "1.16"
parking_lot = "0.12"  # Efficient mutex implementation
uuid = { version = "1.16.0", features = ["v4"] }
dashmap = "6.1.0"

[features]
default = []
console = ["console-subscriber"]

[[bin]]
name = "rustycluster"
path = "src/main.rs"

[[bin]]
name = "encrypt_password"
path = "src/bin/encrypt_password.rs"

[[bin]]
name = "decrypt_password"
path = "src/bin/decrypt_password.rs"

[build-dependencies]
tonic-build = "0.13"
