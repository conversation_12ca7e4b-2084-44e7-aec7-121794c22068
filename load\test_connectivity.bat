@echo off
REM Test basic connectivity to RustyCluster

cd C:\Users\<USER>\Desktop\starters\ghz-windows-x86_64

echo Testing basic connectivity to RustyCluster...
echo.

REM Test 1: Single request
echo Test 1: Single request
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json -n 1 -c 1 --metadata-file=../meta.json 127.0.0.1:50051
echo Single request result: %ERRORLEVEL%
echo.

REM Test 2: Ping test
echo Test 2: Ping test
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Ping --data="{}" -n 5 -c 1 --metadata-file=../meta.json 127.0.0.1:50051
echo Ping test result: %ERRORLEVEL%
echo.

REM Test 3: Authentication test
echo Test 3: Authentication test
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Authenticate --data="{\"username\":\"testuser\",\"password\":\"testpass\"}" -n 1 -c 1 127.0.0.1:50051
echo Authentication test result: %ERRORLEVEL%
echo.

echo Connectivity tests completed!
pause
