use rustycluster::crypto::create_crypto_manager;
use std::env;
use std::io::{self, Write};

/// Utility tool to decrypt passwords from RustyCluster configuration files
/// 
/// Usage:
/// 1. Set RUSTYCLUSTER_MASTER_KEY environment variable (optional)
/// 2. Run: cargo run --bin decrypt_password
/// 3. Enter the encrypted password when prompted
/// 4. View the decrypted result
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("RustyCluster Password Decryption Utility");
    println!("========================================");
    println!();

    // Check if master key is set
    let master_key_set = env::var("RUSTYCLUSTER_MASTER_KEY").is_ok();
    if master_key_set {
        println!("✓ Using RUSTYCLUSTER_MASTER_KEY environment variable");
    } else {
        println!("⚠ RUSTYCLUSTER_MASTER_KEY not set, using default key");
        println!("  Set this environment variable in production for security!");
    }
    println!();

    // Create crypto manager
    let crypto_manager = create_crypto_manager()?;

    loop {
        // Prompt for encrypted password
        print!("Enter encrypted password to decrypt (or 'quit' to exit): ");
        io::stdout().flush()?;

        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        let encrypted_password = input.trim();

        if encrypted_password.is_empty() {
            println!("Empty input entered, skipping...");
            println!();
            continue;
        }

        if encrypted_password.eq_ignore_ascii_case("quit") || encrypted_password.eq_ignore_ascii_case("exit") {
            println!("Goodbye!");
            break;
        }

        // Check if it looks like an encrypted password
        if !encrypted_password.starts_with("ENC:") {
            println!("⚠ Input doesn't start with 'ENC:' - this might be a plain text password");
            println!("  Plain text passwords are returned as-is for backward compatibility");
        }

        // Decrypt the password
        match crypto_manager.decrypt(encrypted_password) {
            Ok(decrypted) => {
                println!();
                println!("✓ Password decrypted successfully!");
                println!("Encrypted: {}", encrypted_password);
                println!("Decrypted: {}", decrypted);
                println!();
            }
            Err(e) => {
                eprintln!("✗ Failed to decrypt password: {}", e);
                println!("  Make sure you're using the same master key that was used for encryption");
                println!();
            }
        }
    }

    Ok(())
}
