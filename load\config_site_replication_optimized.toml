# Optimized configuration for site-based replication feature
# This configuration is tuned for 10000+ RPS performance
# Redis connection URL for the primary node
redis_url = "redis://settlenxt:npci@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to (Rust nodes)
secondary_nodes = ["http://127.0.0.1:50052"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 2048

# Number of secondary nodes to which data should be replicated
replication_factor = 1

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# IMPORTANT: Set to true to enable site replication (async replication required for site replication)
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 50

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 10

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 1024

# OPTIMIZED BATCHING PARAMETERS FOR HIGH PERFORMANCE
# Maximum number of operations in a batch - increased for better throughput
max_batch_size = 50000

# Interval in milliseconds to flush batches - reduced for lower latency
batch_flush_interval_ms = 10

# Authentication configuration
auth_enabled = true
auth_username = "testuser"
auth_password = "testpass"
session_duration_secs = 3600

# Write consistency configuration (not used when async_replication = true)
# These settings are only active when async_replication = false
# List of peer Redis nodes for write consistency (when async_replication = false)
# These are direct Redis connections, not Rust nodes
peer_redis_nodes = ["redis://settlenxt:npci@127.0.0.1:6370", "redis://settlenxt:npci@127.0.0.1:6371"]

# Write consistency level: ALL, QUORUM, or ONE (only used when async_replication = false)
write_consistency = "QUORUM"
quorum_value = 2
write_retry_count = 3
peer_redis_pool_size = 128

# OPTIMIZED SITE-BASED REPLICATION CONFIGURATION
# Enable site-based replication for cross-site data replication
site_replication_enabled = true

# INCREASED CONNECTION POOL SIZE FOR BETTER THROUGHPUT
site_replication_pool_size = 2048

# Primary node for other site (will receive replication with skip_replication=false)
# This is the gRPC endpoint of the primary node in the other site
site_primary_node = "http://127.0.0.1:50053"

# Failover node for other site (used when primary node is unavailable)
# This is the gRPC endpoint of the failover node in the other site
site_failover_node = "http://127.0.0.1:50054"

# REDUCED RETRY COUNT FOR FASTER FAILURE DETECTION
site_replication_retry_count = 2

# REDUCED TIMEOUT FOR FASTER OPERATIONS
site_replication_timeout_ms = 500

# Use physical connections for better performance monitoring
use_physical_connections = true

# OPTIMIZED PERFORMANCE TUNING PARAMETERS
# Reduced TCP keepalive for faster connection recycling
tcp_keepalive_secs = 15

# Enable TCP_NODELAY for lower latency
tcp_nodelay = true

# INCREASED CONCURRENCY LIMITS FOR HIGHER THROUGHPUT
concurrency_limit = 4096
max_concurrent_streams = 16384

# OPTIMIZED CHUNK SIZE FOR BETTER BATCHING
chunk_size = 25000

# INCREASED SHARDING FOR BETTER PARALLELISM
num_shards = 256

# INCREASED WORKER THREADS FOR BETTER CPU UTILIZATION
worker_threads = 64

# ADDITIONAL OPTIMIZATION PARAMETERS
# Connection pool timeout settings
connection_timeout_ms = 1000
request_timeout_ms = 2000

# Batch processing optimization
enable_batch_compression = false
batch_processing_threads = 16

# Memory optimization
preallocate_buffers = true
buffer_pool_size = 1024

# Network optimization
socket_send_buffer_size = 262144
socket_recv_buffer_size = 262144
tcp_window_size = 1048576

# Site replication specific optimizations
site_batch_size = 10000
site_flush_interval_ms = 5
site_connection_pool_min = 512
site_connection_pool_max = 2048
site_health_check_interval_ms = 5000

# Async processing optimization
async_task_queue_size = 100000
async_worker_threads = 32
enable_async_batching = true

# Monitoring and metrics
enable_performance_metrics = true
metrics_collection_interval_ms = 1000
log_slow_operations_threshold_ms = 100

# Error handling optimization
fast_fail_on_errors = true
circuit_breaker_enabled = true
circuit_breaker_failure_threshold = 10
circuit_breaker_timeout_ms = 5000

# Memory management
gc_interval_ms = 30000
memory_pool_enabled = true
connection_recycling_enabled = true

# Load balancing
enable_load_balancing = true
load_balancing_algorithm = "round_robin"
health_check_enabled = true
health_check_interval_ms = 2000

# Compression and serialization
enable_request_compression = false
enable_response_compression = false
serialization_format = "protobuf"

# Cache optimization
enable_connection_caching = true
connection_cache_size = 4096
enable_request_caching = false

# Threading model optimization
use_work_stealing_scheduler = true
thread_pool_size = 64
io_thread_count = 16

# Protocol optimization
http2_initial_window_size = 1048576
http2_max_frame_size = 16777215
http2_enable_push = false

# Security optimization (minimal overhead)
tls_enabled = false
certificate_validation = false

# Debugging and profiling (disabled for production performance)
enable_tracing = false
enable_detailed_logging = false
log_level = "warn"

# Resource limits
max_memory_usage_mb = 8192
max_cpu_usage_percent = 90
max_open_files = 65536

# Failover and recovery
enable_automatic_failover = true
failover_detection_timeout_ms = 1000
recovery_retry_interval_ms = 5000

# Data consistency
enable_checksums = false
enable_data_validation = false
consistency_check_interval_ms = 60000

# Performance monitoring
enable_latency_tracking = true
enable_throughput_tracking = true
performance_log_interval_ms = 10000

# Network tuning
tcp_no_delay = true
tcp_quick_ack = true
socket_reuse_addr = true
socket_reuse_port = true

# Operating system optimization
use_epoll = true
use_io_uring = false
numa_aware = true

# JIT compilation optimization
enable_jit_compilation = true
jit_optimization_level = 3

# Prefetching and caching
enable_prefetching = true
prefetch_size = 1024
cache_line_size = 64

# Vectorization
enable_simd = true
vectorization_level = 2

# Branch prediction optimization
enable_branch_prediction = true
profile_guided_optimization = false

# Memory allocation optimization
use_custom_allocator = true
allocator_type = "jemalloc"
memory_alignment = 64

# Lock-free data structures
use_lockfree_queues = true
use_lockfree_hashmaps = true
use_atomic_counters = true

# CPU affinity and NUMA
enable_cpu_affinity = true
numa_node_binding = true
cpu_isolation = false

# Power management
enable_power_saving = false
cpu_frequency_scaling = "performance"
power_profile = "high_performance"

# Compiler optimizations
optimization_level = "O3"
enable_lto = true
enable_pgo = false
target_cpu = "native"

# Runtime optimization
enable_runtime_optimization = true
adaptive_optimization = true
optimization_threshold = 1000

# Garbage collection (if applicable)
gc_strategy = "concurrent"
gc_threads = 8
gc_heap_size_mb = 4096

# System call optimization
use_batch_syscalls = true
syscall_batching_size = 64
enable_syscall_caching = true

# File system optimization
use_direct_io = false
file_buffer_size = 1048576
enable_file_caching = true

# Network stack optimization
use_kernel_bypass = false
enable_zero_copy = true
network_buffer_size = 2097152

# Interrupt handling
use_interrupt_coalescing = true
interrupt_moderation = true
napi_weight = 64

# Queue management
use_priority_queues = true
queue_depth = 1024
enable_queue_batching = true

# Load shedding
enable_load_shedding = true
load_shedding_threshold = 95
backpressure_enabled = true

# Circuit breaker patterns
circuit_breaker_pattern = "adaptive"
bulkhead_isolation = true
timeout_patterns = "exponential_backoff"

# Observability (minimal overhead)
enable_metrics = true
enable_distributed_tracing = false
sampling_rate = 0.01

# Feature flags
enable_experimental_features = true
enable_beta_optimizations = true
compatibility_mode = false
