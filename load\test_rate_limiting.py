#!/usr/bin/env python3
"""
Rate Limiting Test Script for RustyCluster

This script demonstrates and tests the rate limiting functionality of RustyCluster.
It sends requests at different rates to show how rate limiting works.
"""

import grpc
import time
import sys
import argparse
from concurrent.futures import ThreadPoolExecutor
import threading

# Import the generated protobuf files
sys.path.append('load')
import rustycluster_pb2
import rustycluster_pb2_grpc

class RateLimitTester:
    def __init__(self, host='localhost', port=50051, username='testuser', password='testpass'):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.channel = None
        self.stub = None
        self.session_token = None
        
    def connect(self):
        """Connect to RustyCluster and authenticate"""
        self.channel = grpc.insecure_channel(f'{self.host}:{self.port}')
        self.stub = rustycluster_pb2_grpc.KeyValueServiceStub(self.channel)
        
        # Authenticate
        auth_request = rustycluster_pb2.AuthenticateRequest(
            username=self.username,
            password=self.password
        )
        
        try:
            auth_response = self.stub.Authenticate(auth_request)
            if auth_response.success:
                self.session_token = auth_response.session_token
                print(f"✅ Authentication successful. Session token: {self.session_token[:20]}...")
                return True
            else:
                print(f"❌ Authentication failed: {auth_response.message}")
                return False
        except grpc.RpcError as e:
            print(f"❌ Authentication error: {e.details()}")
            return False
    
    def make_request(self, key_suffix=""):
        """Make a single SET request with authentication"""
        if not self.session_token:
            return False, "Not authenticated"
        
        metadata = [('authorization', f'Bearer {self.session_token}')]
        request = rustycluster_pb2.SetRequest(
            key=f"rate_limit_test_{key_suffix}_{int(time.time() * 1000)}",
            value=f"test_value_{int(time.time() * 1000)}",
            skip_replication=False
        )
        
        try:
            start_time = time.time()
            response = self.stub.Set(request, metadata=metadata, timeout=5.0)
            end_time = time.time()
            
            if response.success:
                return True, f"Success (latency: {(end_time - start_time) * 1000:.1f}ms)"
            else:
                return False, "Request failed"
        except grpc.RpcError as e:
            end_time = time.time()
            latency = (end_time - start_time) * 1000
            if e.code() == grpc.StatusCode.RESOURCE_EXHAUSTED:
                return False, f"RATE LIMITED (latency: {latency:.1f}ms)"
            else:
                return False, f"Error: {e.details()} (latency: {latency:.1f}ms)"
    
    def test_burst_behavior(self, burst_size=10):
        """Test burst behavior - send multiple requests quickly"""
        print(f"\n🚀 Testing burst behavior ({burst_size} requests)")
        print("=" * 60)
        
        success_count = 0
        rate_limited_count = 0
        
        start_time = time.time()
        
        for i in range(burst_size):
            success, message = self.make_request(f"burst_{i}")
            if success:
                success_count += 1
                print(f"Request {i+1:2d}: ✅ {message}")
            else:
                if "RATE LIMITED" in message:
                    rate_limited_count += 1
                    print(f"Request {i+1:2d}: 🚫 {message}")
                else:
                    print(f"Request {i+1:2d}: ❌ {message}")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n📊 Burst Test Results:")
        print(f"   Total requests: {burst_size}")
        print(f"   Successful: {success_count}")
        print(f"   Rate limited: {rate_limited_count}")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Average rate: {burst_size / total_time:.1f} RPS")
    
    def test_sustained_rate(self, target_rps=5, duration_seconds=10):
        """Test sustained rate - send requests at a specific rate"""
        print(f"\n⏱️  Testing sustained rate ({target_rps} RPS for {duration_seconds}s)")
        print("=" * 60)
        
        interval = 1.0 / target_rps
        total_requests = int(target_rps * duration_seconds)
        
        success_count = 0
        rate_limited_count = 0
        error_count = 0
        
        start_time = time.time()
        
        for i in range(total_requests):
            request_start = time.time()
            
            success, message = self.make_request(f"sustained_{i}")
            if success:
                success_count += 1
                print(f"Request {i+1:2d}: ✅ {message}")
            else:
                if "RATE LIMITED" in message:
                    rate_limited_count += 1
                    print(f"Request {i+1:2d}: 🚫 {message}")
                else:
                    error_count += 1
                    print(f"Request {i+1:2d}: ❌ {message}")
            
            # Sleep to maintain target rate
            elapsed = time.time() - request_start
            sleep_time = max(0, interval - elapsed)
            if sleep_time > 0:
                time.sleep(sleep_time)
        
        end_time = time.time()
        total_time = end_time - start_time
        actual_rps = total_requests / total_time
        
        print(f"\n📊 Sustained Rate Test Results:")
        print(f"   Target RPS: {target_rps}")
        print(f"   Actual RPS: {actual_rps:.1f}")
        print(f"   Total requests: {total_requests}")
        print(f"   Successful: {success_count}")
        print(f"   Rate limited: {rate_limited_count}")
        print(f"   Errors: {error_count}")
        print(f"   Success rate: {(success_count / total_requests) * 100:.1f}%")
    
    def test_recovery_behavior(self, wait_seconds=6):
        """Test recovery behavior after rate limiting"""
        print(f"\n🔄 Testing recovery behavior (wait {wait_seconds}s)")
        print("=" * 60)

        # First, trigger rate limiting
        print("Triggering rate limit...")
        for i in range(10):
            success, message = self.make_request(f"recovery_trigger_{i}")
            if not success and "RATE LIMITED" in message:
                print(f"✅ Rate limit triggered on request {i+1}")
                break

        # Wait for recovery
        print(f"Waiting {wait_seconds} seconds for token bucket to refill...")
        time.sleep(wait_seconds)

        # Test recovery
        print("Testing recovery...")
        success, message = self.make_request("recovery_test")
        if success:
            print(f"✅ Recovery successful: {message}")
        else:
            print(f"❌ Recovery failed: {message}")

    def test_global_rate_limiting(self, num_clients=3, requests_per_client=10):
        """Test global rate limiting with multiple simulated clients"""
        print(f"\n🌍 Testing global rate limiting ({num_clients} clients, {requests_per_client} requests each)")
        print("=" * 60)

        def client_worker(client_id, results):
            """Worker function for simulating a client"""
            success_count = 0
            rate_limited_count = 0

            for i in range(requests_per_client):
                success, message = self.make_request(f"global_test_client_{client_id}_{i}")
                if success:
                    success_count += 1
                    print(f"Client {client_id} Request {i+1:2d}: ✅ {message}")
                else:
                    if "RATE LIMITED" in message:
                        rate_limited_count += 1
                        print(f"Client {client_id} Request {i+1:2d}: 🚫 {message}")
                    else:
                        print(f"Client {client_id} Request {i+1:2d}: ❌ {message}")

                # Small delay between requests to simulate realistic client behavior
                time.sleep(0.1)

            results[client_id] = (success_count, rate_limited_count)

        # Run multiple clients concurrently
        results = {}
        threads = []

        start_time = time.time()

        for client_id in range(num_clients):
            thread = threading.Thread(target=client_worker, args=(client_id, results))
            threads.append(thread)
            thread.start()

        # Wait for all clients to complete
        for thread in threads:
            thread.join()

        end_time = time.time()
        total_time = end_time - start_time

        # Calculate totals
        total_requests = num_clients * requests_per_client
        total_success = sum(result[0] for result in results.values())
        total_rate_limited = sum(result[1] for result in results.values())

        print(f"\n📊 Global Rate Limiting Test Results:")
        print(f"   Clients: {num_clients}")
        print(f"   Requests per client: {requests_per_client}")
        print(f"   Total requests: {total_requests}")
        print(f"   Total successful: {total_success}")
        print(f"   Total rate limited: {total_rate_limited}")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Average rate: {total_requests / total_time:.1f} RPS")
        print(f"   Success rate: {(total_success / total_requests) * 100:.1f}%")

        # Per-client breakdown
        print(f"\n📋 Per-Client Results:")
        for client_id, (success, rate_limited) in results.items():
            print(f"   Client {client_id}: {success} success, {rate_limited} rate limited")
    
    def close(self):
        """Close the connection"""
        if self.channel:
            self.channel.close()

def main():
    parser = argparse.ArgumentParser(description='Test RustyCluster Rate Limiting')
    parser.add_argument('--host', default='localhost', help='RustyCluster host')
    parser.add_argument('--port', type=int, default=50051, help='RustyCluster port')
    parser.add_argument('--username', default='testuser', help='Username for authentication')
    parser.add_argument('--password', default='testpass', help='Password for authentication')
    parser.add_argument('--test', choices=['burst', 'sustained', 'recovery', 'global', 'all'],
                       default='all', help='Test type to run')
    
    args = parser.parse_args()
    
    print("🧪 RustyCluster Rate Limiting Test")
    print(f"Target: {args.host}:{args.port}")
    print("=" * 60)
    
    tester = RateLimitTester(args.host, args.port, args.username, args.password)
    
    try:
        if not tester.connect():
            return 1
        
        if args.test in ['burst', 'all']:
            tester.test_burst_behavior(burst_size=15)
        
        if args.test in ['sustained', 'all']:
            tester.test_sustained_rate(target_rps=5, duration_seconds=8)
        
        if args.test in ['recovery', 'all']:
            tester.test_recovery_behavior(wait_seconds=6)

        if args.test in ['global', 'all']:
            tester.test_global_rate_limiting(num_clients=3, requests_per_client=8)

        print("\n✅ Rate limiting tests completed!")
        
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return 1
    finally:
        tester.close()
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
