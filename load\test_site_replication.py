#!/usr/bin/env python3
"""
Test script to verify that all operations are being replicated to other sites.
This script tests all write operations to ensure they are properly replicated.
"""

import grpc
import sys
import os
import time
import redis

# Add the load directory to the path to import the generated protobuf files
sys.path.append(os.path.join(os.path.dirname(__file__), 'load'))

import rustycluster_pb2
import rustycluster_pb2_grpc

def get_auth_token(channel, username, password):
    """Get authentication token from the server."""
    stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
    
    request = rustycluster_pb2.AuthenticateRequest(
        username=username,
        password=password
    )
    
    try:
        response = stub.Authenticate(request)
        if response.success:
            print(f"✅ Authentication successful: {response.message}")
            return response.session_token
        else:
            print(f"❌ Authentication failed: {response.message}")
            return None
    except grpc.RpcError as e:
        print(f"❌ Authentication error: {e}")
        return None

def create_authenticated_metadata(token):
    """Create metadata with authentication token."""
    return [('authorization', f'Bearer {token}')]

def test_operation(stub, metadata, operation_name, operation_func):
    """Test a single operation and return success status."""
    try:
        print(f"Testing {operation_name}...")
        result = operation_func()
        print(f"✅ {operation_name} successful")
        return True
    except grpc.RpcError as e:
        print(f"❌ {operation_name} failed: {e}")
        return False
    except Exception as e:
        print(f"❌ {operation_name} error: {e}")
        return False

def check_redis_key_exists(redis_client, key, expected_value=None):
    """Check if a key exists in Redis and optionally verify its value."""
    try:
        if expected_value is not None:
            value = redis_client.get(key)
            if value is None:
                return False
            return value.decode('utf-8') == expected_value
        else:
            return redis_client.exists(key) > 0
    except Exception as e:
        print(f"Redis check error: {e}")
        return False

def main():
    # Configuration
    primary_site_url = "127.0.0.1:50051"  # Primary site
    target_site_url = "127.0.0.1:50053"   # Target site (where replication should happen)
    username = "testuser"
    password = "testpass"
    
    # Redis connection for target site verification
    target_redis_url = "redis://settlenxt:npci@127.0.0.1:6379"  # Adjust as needed
    
    print("🚀 Starting Site Replication Test")
    print(f"Primary site: {primary_site_url}")
    print(f"Target site: {target_site_url}")
    print()
    
    # Connect to primary site
    try:
        primary_channel = grpc.insecure_channel(primary_site_url)
        primary_stub = rustycluster_pb2_grpc.KeyValueServiceStub(primary_channel)
        print(f"✅ Connected to primary site: {primary_site_url}")
    except Exception as e:
        print(f"❌ Failed to connect to primary site: {e}")
        return False
    
    # Authenticate with primary site
    token = get_auth_token(primary_channel, username, password)
    if not token:
        print("❌ Failed to authenticate with primary site")
        return False
    
    metadata = create_authenticated_metadata(token)
    
    # Connect to Redis on target site for verification
    try:
        target_redis = redis.Redis.from_url(target_redis_url)
        target_redis.ping()
        print(f"✅ Connected to target Redis: {target_redis_url}")
    except Exception as e:
        print(f"❌ Failed to connect to target Redis: {e}")
        return False
    
    print("\n📝 Testing all write operations for site replication...")
    
    test_results = []
    
    # Test 1: SET operation
    def test_set():
        request = rustycluster_pb2.SetRequest(
            key="site_test_set",
            value="test_value_set"
        )
        response = primary_stub.Set(request, metadata=metadata)
        return response.success
    
    test_results.append(test_operation(primary_stub, metadata, "SET", test_set))
    
    # Test 2: DELETE operation
    def test_delete():
        # First set a key to delete
        request = rustycluster_pb2.SetRequest(
            key="site_test_delete",
            value="to_be_deleted"
        )
        primary_stub.Set(request, metadata=metadata)
        
        # Now delete it
        delete_request = rustycluster_pb2.DeleteRequest(key="site_test_delete")
        response = primary_stub.Delete(delete_request, metadata=metadata)
        return response.success
    
    test_results.append(test_operation(primary_stub, metadata, "DELETE", test_delete))
    
    # Test 3: SETEX operation
    def test_setex():
        request = rustycluster_pb2.SetExRequest(
            key="site_test_setex",
            value="test_value_setex",
            ttl=3600
        )
        response = primary_stub.SetEx(request, metadata=metadata)
        return response.success
    
    test_results.append(test_operation(primary_stub, metadata, "SETEX", test_setex))
    
    # Test 4: SETEXPIRY operation
    def test_setexpiry():
        # First set a key
        request = rustycluster_pb2.SetRequest(
            key="site_test_setexpiry",
            value="test_value_setexpiry"
        )
        primary_stub.Set(request, metadata=metadata)
        
        # Now set expiry
        expiry_request = rustycluster_pb2.SetExpiryRequest(
            key="site_test_setexpiry",
            ttl=3600
        )
        response = primary_stub.SetExpiry(expiry_request, metadata=metadata)
        return response.success
    
    test_results.append(test_operation(primary_stub, metadata, "SETEXPIRY", test_setexpiry))
    
    # Test 5: INCRBY operation
    def test_incrby():
        request = rustycluster_pb2.IncrByRequest(
            key="site_test_incrby",
            value=5
        )
        response = primary_stub.IncrBy(request, metadata=metadata)
        return True  # IncrBy returns new_value, not success boolean
    
    test_results.append(test_operation(primary_stub, metadata, "INCRBY", test_incrby))
    
    # Test 6: DECRBY operation
    def test_decrby():
        request = rustycluster_pb2.DecrByRequest(
            key="site_test_decrby",
            value=3
        )
        response = primary_stub.DecrBy(request, metadata=metadata)
        return True  # DecrBy returns new_value, not success boolean
    
    test_results.append(test_operation(primary_stub, metadata, "DECRBY", test_decrby))
    
    # Test 7: INCRBYFLOAT operation
    def test_incrbyfloat():
        request = rustycluster_pb2.IncrByFloatRequest(
            key="site_test_incrbyfloat",
            value=2.5
        )
        response = primary_stub.IncrByFloat(request, metadata=metadata)
        return True  # IncrByFloat returns new_value, not success boolean
    
    test_results.append(test_operation(primary_stub, metadata, "INCRBYFLOAT", test_incrbyfloat))
    
    # Test 8: HSET operation
    def test_hset():
        request = rustycluster_pb2.HSetRequest(
            key="site_test_hset",
            field="field1",
            value="hash_value1"
        )
        response = primary_stub.HSet(request, metadata=metadata)
        return response.success
    
    test_results.append(test_operation(primary_stub, metadata, "HSET", test_hset))
    
    # Test 9: HINCRBY operation
    def test_hincrby():
        request = rustycluster_pb2.HIncrByRequest(
            key="site_test_hincrby",
            field="counter",
            value=10
        )
        response = primary_stub.HIncrBy(request, metadata=metadata)
        return True  # HIncrBy returns new_value, not success boolean
    
    test_results.append(test_operation(primary_stub, metadata, "HINCRBY", test_hincrby))
    
    # Test 10: HDECRBY operation
    def test_hdecrby():
        request = rustycluster_pb2.HDecrByRequest(
            key="site_test_hdecrby",
            field="counter",
            value=5
        )
        response = primary_stub.HDecrBy(request, metadata=metadata)
        return True  # HDecrBy returns new_value, not success boolean
    
    test_results.append(test_operation(primary_stub, metadata, "HDECRBY", test_hdecrby))
    
    # Test 11: HINCRBYFLOAT operation
    def test_hincrbyfloat():
        request = rustycluster_pb2.HIncrByFloatRequest(
            key="site_test_hincrbyfloat",
            field="float_counter",
            value=1.5
        )
        response = primary_stub.HIncrByFloat(request, metadata=metadata)
        return True  # HIncrByFloat returns new_value, not success boolean
    
    test_results.append(test_operation(primary_stub, metadata, "HINCRBYFLOAT", test_hincrbyfloat))
    
    # Test 12: BatchWrite operation
    def test_batch_write():
        operations = [
            rustycluster_pb2.BatchOperation(
                operation_type=rustycluster_pb2.BatchOperation.SET,
                key="site_test_batch_set",
                value="batch_value"
            ),
            rustycluster_pb2.BatchOperation(
                operation_type=rustycluster_pb2.BatchOperation.DELETE,
                key="site_test_batch_delete"
            )
        ]
        
        request = rustycluster_pb2.BatchWriteRequest(operations=operations)
        response = primary_stub.BatchWrite(request, metadata=metadata)
        return response.success
    
    test_results.append(test_operation(primary_stub, metadata, "BATCHWRITE", test_batch_write))
    
    # Wait a moment for replication to complete
    print("\n⏳ Waiting for site replication to complete...")
    time.sleep(2)
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    print(f"Total operations tested: {len(test_results)}")
    print(f"Successful operations: {sum(test_results)}")
    print(f"Failed operations: {len(test_results) - sum(test_results)}")
    
    if all(test_results):
        print("\n🎉 All site replication tests passed!")
        print("✅ All write operations are now being replicated to other sites")
        return True
    else:
        print("\n⚠️  Some site replication tests failed")
        print("❌ Check the server logs for more details")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
