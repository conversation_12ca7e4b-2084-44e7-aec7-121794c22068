#!/usr/bin/env python3
"""
Single request test to debug ResourceExhausted error
"""

import grpc
import sys
import time

# Import the generated protobuf files
sys.path.append('load')
import rustycluster_pb2
import rustycluster_pb2_grpc

def test_single_request():
    print("Testing single request to RustyCluster...")
    
    # Connect to RustyCluster
    channel = grpc.insecure_channel('127.0.0.1:50051')
    stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
    
    try:
        # Test 1: Authentication
        print("\n1. Testing authentication...")
        auth_request = rustycluster_pb2.AuthenticateRequest(
            username="testuser",
            password="testpass"
        )
        
        auth_response = stub.Authenticate(auth_request, timeout=10.0)
        if auth_response.success:
            session_token = auth_response.session_token
            print(f"✅ Authentication successful. Token: {session_token[:20]}...")
        else:
            print(f"❌ Authentication failed: {auth_response.message}")
            return False
        
        # Test 2: Single SET request
        print("\n2. Testing single SET request...")
        metadata = [('authorization', f'Bearer {session_token}')]
        
        set_request = rustycluster_pb2.SetRequest(
            key="test_key_single",
            value="test_value_single",
            skip_replication=False
        )
        
        start_time = time.time()
        set_response = stub.Set(set_request, metadata=metadata, timeout=10.0)
        end_time = time.time()
        
        if set_response.success:
            print(f"✅ SET request successful (latency: {(end_time - start_time) * 1000:.1f}ms)")
        else:
            print(f"❌ SET request failed: {set_response.message}")
            return False
        
        # Test 3: Single GET request
        print("\n3. Testing single GET request...")
        get_request = rustycluster_pb2.GetRequest(key="test_key_single")
        
        start_time = time.time()
        get_response = stub.Get(get_request, metadata=metadata, timeout=10.0)
        end_time = time.time()
        
        if get_response.success:
            print(f"✅ GET request successful (latency: {(end_time - start_time) * 1000:.1f}ms)")
            print(f"   Retrieved value: {get_response.value}")
        else:
            print(f"❌ GET request failed: {get_response.message}")
            return False
        
        # Test 4: Multiple requests to check rate limiting
        print("\n4. Testing multiple requests (rate limiting check)...")
        for i in range(5):
            set_request = rustycluster_pb2.SetRequest(
                key=f"test_key_multi_{i}",
                value=f"test_value_multi_{i}",
                skip_replication=False
            )
            
            try:
                start_time = time.time()
                set_response = stub.Set(set_request, metadata=metadata, timeout=10.0)
                end_time = time.time()
                
                if set_response.success:
                    print(f"   Request {i+1}: ✅ Success (latency: {(end_time - start_time) * 1000:.1f}ms)")
                else:
                    print(f"   Request {i+1}: ❌ Failed: {set_response.message}")
            except grpc.RpcError as e:
                end_time = time.time()
                latency = (end_time - start_time) * 1000
                print(f"   Request {i+1}: ❌ gRPC Error: {e.code()} - {e.details()} (latency: {latency:.1f}ms)")
                if e.code() == grpc.StatusCode.RESOURCE_EXHAUSTED:
                    print("   ⚠️  RESOURCE_EXHAUSTED error detected!")
                    return False
        
        print("\n✅ All tests passed! Rate limiting appears to be disabled.")
        return True
        
    except grpc.RpcError as e:
        print(f"\n❌ gRPC Error: {e.code()} - {e.details()}")
        if e.code() == grpc.StatusCode.RESOURCE_EXHAUSTED:
            print("⚠️  RESOURCE_EXHAUSTED error detected in basic test!")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False
    finally:
        channel.close()

if __name__ == '__main__':
    success = test_single_request()
    if not success:
        print("\n🔍 Debug suggestions:")
        print("1. Check if RustyCluster is running on port 50051")
        print("2. Verify rate_limiting_enabled = false in config")
        print("3. Check RustyCluster startup logs for rate limiting status")
        print("4. Restart RustyCluster with the updated configuration")
        sys.exit(1)
    else:
        print("\n🎉 Single request test completed successfully!")
        sys.exit(0)
