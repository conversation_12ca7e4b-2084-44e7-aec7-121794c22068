# Rusty<PERSON>luster Scripts Documentation

This document contains detailed information about all scripts, batch files, and load testing tools available in the RustyCluster project. All scripts are located in the `load/` directory.

## Authentication Scripts

### Python Authentication Script

**File**: `load/get_session_token.py`

Get a session token for authenticated requests:

```bash
python get_session_token.py --username testuser --password testpass
```

### Windows Batch Authentication Script

**File**: `load/get_token.bat`

Get session token on Windows:

```cmd
get_token.bat --username testuser --password testpass
```

## Load Testing Scripts

### High-Performance Async Load Testing

**File**: `load/loadtest_async.py`

**Expected Performance**: 10K-20K+ RPS

Ultra-high performance load testing using asyncio:

```bash
# Navigate to load directory
cd load

# Basic high-performance test
python loadtest_async.py --username testuser --password testpass --operations 200000 --concurrency 2000 --rps 10000 --pool-size 200

# Scale up for maximum performance
python loadtest_async.py --username testuser --password testpass --operations 300000 --concurrency 3000 --rps 15000 --pool-size 300

# Push to maximum limits
python loadtest_async.py --username testuser --password testpass --operations 500000 --concurrency 5000 --rps 20000 --pool-size 500
```

### Optimized Load Testing

**File**: `load/loadtest_optimized.py`

**Expected Performance**: 5K-10K RPS

High-performance testing with threading and connection pooling:

```bash
python loadtest_optimized.py --username testuser --password testpass --operations 100000 --concurrency 1000 --rps 5000
```

### Basic Authenticated Load Testing

**File**: `load/loadtest_with_auth.py`

**Expected Performance**: 2K-5K RPS

Basic load testing with authentication:

```bash
python loadtest_with_auth.py --username testuser --password testpass --operations 50000 --concurrency 500 --rps 2000
```

### Replication Load Testing

**File**: `load/loadtest_replication.py`

Load testing specifically for replication scenarios:

```bash
python loadtest_replication.py --config config_site_replication_test.toml
```

## GHZ Load Testing Scripts

### Automated GHZ with Authentication

**File**: `load/ghz_with_auth.sh`

Automated script for ghz load testing with authentication:

```bash
# Make script executable
chmod +x ghz_with_auth.sh

# Run with authentication
./ghz_with_auth.sh --username testuser --password testpass --rps 10000 -n 200000 -c 1000

# Run without authentication
./ghz_with_auth.sh --no-auth --rps 10000 -n 200000 -c 1000
```

### Manual GHZ Commands

For load testing with tools like `ghz`, you have several options:

#### Option 1: Disable Authentication (Recommended for Load Testing)
```bash
# Use config_loadtest.toml with auth_enabled = false
cargo run --release load/config_loadtest.toml

# Run your existing ghz command
ghz.exe --insecure --proto=load/proto/rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=load/set-data.json --rps 10000 -n 200000 -c 1000 127.0.0.1:50051
```

#### Option 2: Use ghz with Session Token
```bash
# Get session token
python load/get_session_token.py --username testuser --password testpass

# Use token with ghz (replace YOUR_SESSION_TOKEN)
ghz.exe --insecure --proto=load/proto/rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=load/set-data.json --metadata="authorization:Bearer YOUR_SESSION_TOKEN" --rps 10000 -n 200000 -c 1000 127.0.0.1:50051
```

## Windows Batch Scripts

### Debug Load Test

**File**: `load/debug_load_test.bat`

Debug version of load testing with detailed logging:

```cmd
debug_load_test.bat
```

### gRPC Load Test

**File**: `load/grpc_load_test.bat`

Basic gRPC load testing batch script:

```cmd
grpc_load_test.bat
```

### Gradual Load Test

**File**: `load/load_test_gradual.bat`

Gradually increasing load test:

```cmd
load_test_gradual.bat
```

### Optimized Load Test

**File**: `load/load_test_optimized.bat`

Optimized batch load testing:

```cmd
load_test_optimized.bat
```

### Quick Tests

**File**: `load/quick_test.bat`

Quick connectivity and basic functionality test:

```cmd
quick_test.bat
```

### Rate Limiting Tests

**File**: `load/quick_rate_limit_test.bat`

Quick rate limiting functionality test:

```cmd
quick_rate_limit_test.bat
```

**File**: `load/run_rate_limit_tests.bat`

Comprehensive rate limiting test suite:

```cmd
run_rate_limit_tests.bat
```

### Connectivity Test

**File**: `load/test_connectivity.bat`

Test basic connectivity to RustyCluster:

```cmd
test_connectivity.bat
```

### Start RustyCluster Debug

**File**: `load/start_rustycluster_debug.bat`

Start RustyCluster with debug configuration:

```cmd
start_rustycluster_debug.bat
```

## Specialized Testing Scripts

### Authentication Testing

**File**: `load/test_auth.py`

Test authentication functionality:

```bash
python test_auth.py
```

### Batch Optimization Testing

**File**: `load/test_batch_optimization.py`

Test batch operation optimizations:

```bash
python test_batch_optimization.py
```

### Health Check Testing

**File**: `load/test_health_check.py`

Test health check functionality:

```bash
python test_health_check.py
```

### Performance Testing

**File**: `load/test_optimized_performance.py`

Optimized performance testing:

```bash
python test_optimized_performance.py
```

### Rate Limiting Testing

**File**: `load/test_rate_limiting.py`

Comprehensive rate limiting tests:

```bash
python test_rate_limiting.py
```

**File**: `load/test_rate_limiting_fast.py`

Fast rate limiting validation:

```bash
python test_rate_limiting_fast.py
```

**File**: `load/test_rate_limiting_validation.py`

Rate limiting validation tests:

```bash
python test_rate_limiting_validation.py
```

### Single Request Testing

**File**: `load/test_single_request.py`

Test individual request functionality:

```bash
python test_single_request.py
```

### Site Replication Testing

**File**: `load/test_site_replication.py`

Test site replication functionality:

```bash
python test_site_replication.py
```

**File**: `load/test_site_replication_performance.py`

Site replication performance testing:

```bash
python test_site_replication_performance.py
```

**File**: `load/test_evalsha_site_replication.py`

Test EvalSha operations with site replication:

```bash
python test_evalsha_site_replication.py
```

## Performance Analysis

**File**: `load/analyze_performance.py`

Analyze performance test results:

```bash
python analyze_performance.py
```

## Configuration Files for Testing

All configuration files are located in the `load/` directory:

### Performance Configurations
- `config_performance.toml` - Optimized for maximum throughput
- `config_replication_performance.toml` - Replication performance testing
- `config_site_replication_high_performance.toml` - High-performance site replication
- `config_site_replication_optimized.toml` - Optimized site replication

### Authentication Configurations
- `config_auth_test.toml` - Standard authentication testing
- `config_auth_modes_example.toml` - Different authentication modes

### Rate Limiting Configurations
- `config_rate_limit_test.toml` - Basic rate limiting testing
- `config_rate_limit_1000_rps.toml` - 1000 RPS rate limiting
- `config_global_rate_limit_test.toml` - Global rate limiting
- `config_high_performance_rate_limiting.toml` - High-performance rate limiting
- `config_no_rate_limiting.toml` - Rate limiting disabled

### Specialized Configurations
- `config_loadtest.toml` - Authentication disabled for baseline testing
- `config_write_consistency_test.toml` - Write consistency testing
- `config_site_replication_test.toml` - Site replication testing
- `config_hot_reload_test.toml` - Hot configuration reload testing
- `config_idle_health_check_test.toml` - Idle health check testing

### Node Configurations
- `config_node2.toml` - Secondary node configuration
- `config_node3.toml` - Third node configuration

### Logging Configurations
- `logconfig2.toml` - Logging configuration for node 2
- `logconfig3.toml` - Logging configuration for node 3

## Quick Start Guide

### 1. High-Performance Load Testing
```bash
# Start RustyCluster with performance config
cargo run --release load/config_performance.toml

# Run ultra-high performance test
cd load
python loadtest_async.py --username testuser --password testpass --operations 200000 --concurrency 2000 --rps 10000 --pool-size 200
```

### 2. Authentication Testing
```bash
# Start with authentication enabled
cargo run --release load/config_auth_test.toml

# Get session token
python load/get_session_token.py --username testuser --password testpass

# Run authenticated load test
python load/loadtest_with_auth.py --username testuser --password testpass
```

### 3. Rate Limiting Testing
```bash
# Start with rate limiting enabled
cargo run --release load/config_rate_limit_test.toml

# Run rate limiting tests
python load/test_rate_limiting.py
```

### 4. Site Replication Testing
```bash
# Start with site replication config
cargo run --release load/config_site_replication_test.toml

# Test site replication
python load/test_site_replication.py
```

## Performance Targets

| Script Type | Expected RPS | Technology | Best Use Case |
|-------------|--------------|------------|---------------|
| `loadtest_async.py` | 10K-20K+ | Asyncio | Maximum performance |
| `loadtest_optimized.py` | 5K-10K | Threading + pooling | High performance |
| `loadtest_with_auth.py` | 2K-5K | Basic threading | Basic testing |
| GHZ with auth disabled | 10K+ | Native gRPC | Baseline performance |
| GHZ with authentication | 3K-8K | Native gRPC | Authenticated performance |

## Java Client Example

For Java applications connecting to RustyCluster with authentication:

```java
// Java client authentication example
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.Metadata;
import io.grpc.stub.MetadataUtils;

// Create channel
ManagedChannel channel = ManagedChannelBuilder.forAddress("localhost", 50051)
    .usePlaintext()
    .build();

KeyValueServiceGrpc.KeyValueServiceBlockingStub stub =
    KeyValueServiceGrpc.newBlockingStub(channel);

// Authenticate
AuthenticateRequest authRequest = AuthenticateRequest.newBuilder()
    .setUsername("testuser")
    .setPassword("testpass")
    .build();

AuthenticateResponse authResponse = stub.authenticate(authRequest);

if (authResponse.getSuccess()) {
    String sessionToken = authResponse.getSessionToken();

    // Create metadata with session token
    Metadata metadata = new Metadata();
    Metadata.Key<String> authKey = Metadata.Key.of("authorization",
        Metadata.ASCII_STRING_MARSHALLER);
    metadata.put(authKey, "Bearer " + sessionToken);

    // Use authenticated stub for operations
    KeyValueServiceGrpc.KeyValueServiceBlockingStub authenticatedStub =
        MetadataUtils.attachHeaders(stub, metadata);

    // Now you can use authenticatedStub for all operations
    PingResponse pingResponse = authenticatedStub.ping(PingRequest.newBuilder().build());
}
```

## Script Dependencies

### Python Dependencies

Install required Python packages:

```bash
pip install grpcio grpcio-tools asyncio aiogrpc
```

### Protocol Buffer Files

The following proto files are available in `load/proto/`:
- `rustycluster.proto` - Main protocol buffer definition

Generate Python gRPC files:
```bash
cd load
python -m grpc_tools.protoc --proto_path=proto --python_out=. --grpc_python_out=. proto/rustycluster.proto
```

## Troubleshooting Scripts

### Common Issues

1. **Connection Refused**
   - Ensure RustyCluster is running on the correct port
   - Check firewall settings
   - Verify configuration file paths

2. **Authentication Failures**
   - Verify username/password in configuration
   - Check session token expiry settings
   - Ensure auth_enabled=true in config

3. **Performance Issues**
   - Check system resources (CPU, memory, network)
   - Verify Redis server performance
   - Adjust concurrency and pool sizes

4. **Rate Limiting Errors**
   - Check rate limiting configuration
   - Verify client identification (IP vs session token)
   - Adjust rate limits for testing

### Debug Commands

Enable debug logging:
```bash
RUST_LOG=debug cargo run --release load/config_auth_test.toml
```

Test single operations:
```bash
python load/test_single_request.py
```

Validate connectivity:
```bash
load/test_connectivity.bat
```

## Best Practices

### Load Testing
1. **Start Small**: Begin with low RPS and gradually increase
2. **Monitor Resources**: Watch CPU, memory, and network usage
3. **Use Appropriate Configs**: Match configuration to test scenario
4. **Baseline First**: Test without authentication/rate limiting first
5. **Realistic Data**: Use representative key/value sizes

### Authentication Testing
1. **Token Management**: Handle token expiry gracefully
2. **Connection Pooling**: Reuse authenticated connections
3. **Error Handling**: Implement proper retry logic
4. **Security**: Never log passwords or tokens

### Performance Optimization
1. **Connection Pools**: Size pools appropriately for workload
2. **Batch Operations**: Use batch APIs for bulk operations
3. **Async Processing**: Leverage async capabilities
4. **Resource Monitoring**: Monitor all system components

## Performance Monitoring and Analysis

### Key Metrics to Watch
1. **Throughput**: Target ≥10000 RPS
2. **Latency**: P95 <100ms, P99 <200ms
3. **Error Rate**: <1%
4. **Connection Utilization**: Monitor actual vs configured pool sizes

### Success Criteria
- ✅ **Excellent**: ≥10000 RPS (≥100% of target)
- ✅ **Good**: ≥9500 RPS (≥95% of target)
- ⚠️ **Acceptable**: ≥8000 RPS (≥80% of target)
- ❌ **Needs Work**: <8000 RPS (<80% of target)

### Expected Results

#### With Authentication Enabled
- **Target**: 10,000 RPS
- **Achievable**: 10,000-20,000 RPS
- **Latency**: 1-5ms average

#### Without Authentication
- **Target**: 15,000+ RPS
- **Achievable**: 15,000-25,000 RPS
- **Latency**: 0.5-3ms average

### System Requirements

#### Client Machine
- **CPU**: 4+ cores recommended
- **RAM**: 4GB+ available
- **Network**: Gigabit connection preferred
- **Python**: 3.7+ with grpcio

#### Server Machine (RustyCluster)
- **CPU**: 8+ cores for 10K+ RPS
- **RAM**: 8GB+ recommended
- **Redis**: Properly configured and tuned
- **Network**: Low latency to client

## Site Replication Performance Optimization

### Performance Analysis

**Current Performance**: 6000 TPS with site replication enabled
**Target**: 10000+ RPS
**Gap**: ~67% performance improvement needed

### Root Cause Analysis

1. **Sequential Operation Processing**: Operations sent one-by-one sequentially
2. **Mutex Contention**: Site replication manager serialization
3. **Inefficient Batching**: Small batches sent frequently
4. **Suboptimal Connection Pool Usage**: Sequential processing doesn't utilize pools effectively

### Optimization Strategy

**Phase 1: Configuration Optimizations**
```toml
# Optimized batch flush interval
batch_flush_interval_ms = 100

# Increased site replication pool
site_replication_pool_size = 1024

# Reduced retry count for faster failure detection
site_replication_retry_count = 2

# Reduced timeout for faster operations
site_replication_timeout_ms = 500

# Enable physical connections for better load balancing
use_physical_connections = true
```

**Phase 2: Code Optimizations**
- **BatchWrite Implementation**: Use single network round trip for entire batch instead of N round trips
- **Parallel Operation Processing**: Send operations in parallel instead of sequentially
- **Lock-Free Site Replication Manager**: Remove mutex contention
- **Connection Pool Optimization**: Utilize multiple clients in parallel

### Expected Performance Improvements

| Optimization Phase | Expected RPS Gain | Cumulative RPS |
|-------------------|------------------|----------------|
| **Baseline**      | -                | 6000 RPS       |
| **Phase 1: Config** | +1000-2000     | 7000-8000 RPS |
| **Phase 2: Code**   | +2000-4000     | 9000-12000 RPS |
| **🎯 TARGET**     | **+3000-6000** | **9000-12000 RPS** |

## Troubleshooting Guide

### Performance Issues

#### Issue: Low RPS (< 5K)
**Solutions:**
1. Use `loadtest_async.py` for maximum performance
2. Increase concurrency: `--concurrency 2000`
3. Increase pool size: `--pool-size 200`
4. Verify RustyCluster is using performance config
5. Check system resources (CPU, memory, network)

#### Issue: High Latency
**Check:**
- Network latency: `ping 127.0.0.1`
- Redis performance: `redis-cli --latency-history`
- CPU usage: `htop`
- Connection pool utilization

#### Issue: Connection Errors
**Solutions:**
- Reduce concurrency, increase pool size
- Increase timeouts in client configuration
- Check network connectivity and firewall settings
- Monitor connection pool statistics

### Authentication Issues

#### Issue: Authentication Failures
**Check:**
1. `auth_mode` setting matches client expectations
2. `auth_token_expiry_enabled` setting
3. Session creation/validation logs
4. Username/password correctness

#### Issue: Session Token Expiry
**Solutions:**
- Increase `session_duration_secs`
- Set `auth_token_expiry_enabled = false` for testing
- Implement token refresh in client applications

### Replication Issues

#### Issue: Site Replication Not Working
**Check:**
1. `async_replication = true` (required for site replication)
2. `site_replication_enabled = true`
3. Site nodes are accessible and running
4. Network connectivity between sites
5. Authentication bypass for site replication

#### Issue: Write Consistency Failures
**Check:**
1. `async_replication = false` (required for write consistency)
2. Peer Redis nodes are accessible
3. Redis authentication credentials
4. Network connectivity to peer nodes
5. Consistency level configuration (ALL/QUORUM/ONE)

### Health Check Issues

#### Issue: Health Check Failures
**Normal Behavior:**
- Authentication failures are expected when auth is enabled
- Check for connection success logs, not auth failures

**Solutions:**
- Verify all configured nodes are reachable
- Adjust health check intervals based on network stability
- Enable health checks for all connection types
- Check firewall and network connectivity

### Configuration Issues

#### Issue: Configuration Not Loading
**Check:**
1. Configuration file path is correct
2. TOML syntax is valid
3. All required parameters are present
4. File permissions allow reading

#### Issue: Redis Connection Failures
**Check:**
1. Redis server is running
2. Redis authentication credentials
3. Network connectivity to Redis
4. Redis configuration (bind address, port)
5. Connection pool settings

### Monitoring and Debugging

#### Enable Debug Logging
```toml
# Add to config.toml
level = "debug"
console_enabled = true
```

#### Monitor Connection Pools
```bash
# Monitor actual connections
netstat -an | grep :50051 | wc -l
netstat -an | grep :6379 | wc -l
```

#### Check Resource Usage
```bash
# CPU and memory usage
top -p $(pgrep rustycluster)

# Network usage
iftop
```

#### Verify Site Nodes
```bash
# Check if site nodes are accessible
curl -v http://127.0.0.1:50053/health
curl -v http://127.0.0.1:50054/health
```

## Notes

- All Python scripts require the gRPC dependencies to be installed
- Windows batch scripts are designed for PowerShell environments
- Configuration files should be used with the appropriate scripts
- Performance results may vary based on system specifications and network conditions
- For production load testing, consider using dedicated test environments
- Always test authentication and rate limiting in development before production deployment
