# Rate Limiting Test Configuration - 1000 RPS Global Limit
# This configuration is specifically for testing rate limiting functionality

# Redis connection URL for the primary node
redis_url = "redis://settlenxt:npci@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to (Rust nodes)
secondary_nodes = []

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 512

# Number of secondary nodes to which data should be replicated
replication_factor = 0

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# Disable async replication for simpler testing
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 100

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 256

# Maximum number of operations in a batch
max_batch_size = 1000

# Interval in milliseconds to flush batches
batch_flush_interval_ms = 100

# Authentication configuration
auth_enabled = true
auth_username = "testuser"
auth_password = "testpass"
session_duration_secs = 3600
auth_mode = "connection_only"
auth_token_expiry_enabled = false

# Write consistency configuration (disabled for testing)
peer_redis_nodes = []
write_consistency = "QUORUM"
quorum_value = 2
write_retry_count = 3
peer_redis_pool_size = 32

# Site-based replication configuration (disabled for testing)
site_replication_enabled = false
site_replication_pool_size = 256
site_primary_node = ""
site_failover_node = ""
site_replication_retry_count = 3
site_replication_timeout_ms = 1000

use_physical_connections = true

# Performance tuning parameters
tcp_keepalive_secs = 30
tcp_nodelay = true
concurrency_limit = 512
max_concurrent_streams = 2048
chunk_size = 1000
num_shards = 16
worker_threads = 8

# Health check configuration (disabled for testing)
redis_keepalive_enabled = false
redis_keepalive_interval_secs = 30
secondary_nodes_keepalive_enabled = false
secondary_nodes_keepalive_interval_secs = 30
site_nodes_keepalive_enabled = false
site_nodes_keepalive_interval_secs = 30
peer_redis_keepalive_enabled = false
peer_redis_keepalive_interval_secs = 30

# RATE LIMITING CONFIGURATION FOR TESTING
# Enable rate limiting with specific limits for validation

# Enable rate limiting
rate_limiting_enabled = true

# Use global rate limiting for testing
rate_limit_type = "global"

# Per-client settings (not used in global mode but required)
rate_limit_requests_per_second = 1000
rate_limit_burst_size = 100

# GLOBAL RATE LIMITING - SET TO 1000 RPS FOR TESTING
# This is the limit we want to test against
rate_limit_global_requests_per_second = 1000

# Moderate burst size to allow some initial burst but not too much
# This should allow ~200 requests to burst initially
rate_limit_global_burst_size = 200

# Cleanup interval
rate_limit_cleanup_interval_secs = 300
