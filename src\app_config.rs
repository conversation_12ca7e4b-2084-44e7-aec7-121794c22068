use serde::Deserialize;
use log::info;
use crate::crypto::{CryptoManager, create_crypto_manager};

#[derive(Deserialize, Debug, Clone)]
pub struct Config {
    pub redis_url: String,
    pub secondary_nodes: Vec<String>,
    pub port: u16,
    pub replication_factor: usize, // to how many secondary nodes to replicate
    pub read_consistency: usize, // if the read consistency is 2, it means that the data should be replicated to at least 2 secondary nodes before being considered valid
    #[serde(default = "default_async_replication")]
    pub async_replication: bool, // if true, the data will be replicated to the secondary nodes asynchronously
    #[serde(default = "default_max_retries")]
    pub max_retries: u32, // maximum number of retries for connection attempts
    #[serde(default = "default_retry_delay_ms")]
    pub retry_delay_ms: u64, // delay between retries in milliseconds
    #[serde(default = "default_replication_batch_max_age_secs")]
    pub replication_batch_max_age_secs: u64, // maximum age of a replication batch in seconds
    #[serde(default = "default_redis_pool_size")]
    pub redis_pool_size: usize,
    #[serde(default = "default_secondary_pool_size")]
    pub secondary_pool_size: usize,
    #[serde(default = "default_max_batch_size")]
    pub max_batch_size: usize, // maximum number of operations in a batch
    #[serde(default = "default_batch_flush_interval_ms")]
    pub batch_flush_interval_ms: u64, // interval in milliseconds to flush batches
    #[serde(default = "default_tcp_keepalive_secs")]
    pub tcp_keepalive_secs: u64,
    #[serde(default = "default_tcp_nodelay")]
    pub tcp_nodelay: bool,
    #[serde(default = "default_concurrency_limit")]
    pub concurrency_limit: usize,
    #[serde(default = "default_max_concurrent_streams")]
    pub max_concurrent_streams: u32,
    #[serde(default = "default_chunk_size")]
    pub chunk_size: usize,
    #[serde(default = "default_num_shards")]
    pub num_shards: usize,
    #[serde(default = "default_worker_threads")]
    pub worker_threads: usize,
    // Authentication configuration
    #[serde(default = "default_auth_enabled")]
    pub auth_enabled: bool,
    #[serde(default = "default_auth_username")]
    pub auth_username: String,
    #[serde(default = "default_auth_password")]
    pub auth_password: String,
    #[serde(default = "default_session_duration_secs")]
    pub session_duration_secs: u64,
    #[serde(default = "default_auth_mode")]
    pub auth_mode: String, // "connection_only" or "per_request"
    #[serde(default = "default_auth_token_expiry_enabled")]
    pub auth_token_expiry_enabled: bool,
    // Write consistency configuration
    #[serde(default = "default_peer_redis_nodes")]
    pub peer_redis_nodes: Vec<String>, // list of other Redis nodes for write consistency
    #[serde(default = "default_write_consistency")]
    pub write_consistency: String, // ALL, QUORUM, or ONE
    #[serde(default = "default_quorum_value")]
    pub quorum_value: usize, // number of nodes expected for quorum
    #[serde(default = "default_write_retry_count")]
    pub write_retry_count: u32, // retry attempts for write consistency
    #[serde(default = "default_peer_redis_pool_size")]
    pub peer_redis_pool_size: usize, // connection pool size for each peer Redis node
    // Site-based replication configuration
    #[serde(default = "default_site_replication_enabled")]
    pub site_replication_enabled: bool, // enable site-based replication
    #[serde(default = "default_site_primary_node")]
    pub site_primary_node: String, // primary node for other site
    #[serde(default = "default_site_failover_node")]
    pub site_failover_node: String, // failover node for other site
    #[serde(default = "default_site_replication_retry_count")]
    pub site_replication_retry_count: u32, // retry attempts for site replication
    #[serde(default = "default_site_replication_timeout_ms")]
    pub site_replication_timeout_ms: u64,
    // Health check and keep-alive configuration
    #[serde(default = "default_redis_keepalive_enabled")]
    pub redis_keepalive_enabled: bool,
    #[serde(default = "default_redis_keepalive_interval_secs")]
    pub redis_keepalive_interval_secs: u64,
    #[serde(default = "default_redis_idle_threshold_secs")]
    pub redis_idle_threshold_secs: u64,
    #[serde(default = "default_secondary_nodes_keepalive_enabled")]
    pub secondary_nodes_keepalive_enabled: bool,
    #[serde(default = "default_secondary_nodes_keepalive_interval_secs")]
    pub secondary_nodes_keepalive_interval_secs: u64,
    #[serde(default = "default_secondary_nodes_idle_threshold_secs")]
    pub secondary_nodes_idle_threshold_secs: u64,
    #[serde(default = "default_site_nodes_keepalive_enabled")]
    pub site_nodes_keepalive_enabled: bool,
    #[serde(default = "default_site_nodes_keepalive_interval_secs")]
    pub site_nodes_keepalive_interval_secs: u64,
    #[serde(default = "default_site_nodes_idle_threshold_secs")]
    pub site_nodes_idle_threshold_secs: u64,
    #[serde(default = "default_peer_redis_keepalive_enabled")]
    pub peer_redis_keepalive_enabled: bool,
    #[serde(default = "default_peer_redis_keepalive_interval_secs")]
    pub peer_redis_keepalive_interval_secs: u64, // timeout for site replication operations
    #[serde(default = "default_peer_redis_idle_threshold_secs")]
    pub peer_redis_idle_threshold_secs: u64,
    #[serde(default = "default_site_replication_pool_size")]
    pub site_replication_pool_size: usize, // connection pool size for each site node
    #[serde(default = "default_use_physical_connections")]
    pub use_physical_connections: bool, // if true, create actual TCP connections; if false, use HTTP/2 multiplexing
    // Rate limiting configuration
    #[serde(default = "default_rate_limiting_enabled")]
    pub rate_limiting_enabled: bool, // enable/disable rate limiting
    #[serde(default = "default_rate_limit_type")]
    pub rate_limit_type: String, // "global", "per_client", or "both"
    #[serde(default = "default_rate_limit_requests_per_second")]
    pub rate_limit_requests_per_second: u32, // requests per second per client
    #[serde(default = "default_rate_limit_burst_size")]
    pub rate_limit_burst_size: u32, // maximum burst size (token bucket capacity)
    #[serde(default = "default_rate_limit_global_requests_per_second")]
    pub rate_limit_global_requests_per_second: u32, // global requests per second across all clients
    #[serde(default = "default_rate_limit_global_burst_size")]
    pub rate_limit_global_burst_size: u32, // global maximum burst size
    #[serde(default = "default_rate_limit_cleanup_interval_secs")]
    pub rate_limit_cleanup_interval_secs: u64, // cleanup interval for expired clients

    // Hot configuration reload settings
    #[serde(default = "default_config_watch_interval_secs")]
    pub config_watch_interval_secs: u64, // interval in seconds to check for configuration file changes
}

// Default configuration values - minimal baseline that can be increased through configuration
fn default_async_replication() -> bool { true }
fn default_max_retries() -> u32 { 3 }
fn default_retry_delay_ms() -> u64 { 50 }
fn default_replication_batch_max_age_secs() -> u64 { 30 }
fn default_redis_pool_size() -> usize { 32 }
fn default_secondary_pool_size() -> usize { 16 }
fn default_max_batch_size() -> usize { 1000 }
fn default_batch_flush_interval_ms() -> u64 { 50 }
fn default_tcp_keepalive_secs() -> u64 { 30 }
fn default_tcp_nodelay() -> bool { true }
fn default_concurrency_limit() -> usize { 64 }
fn default_max_concurrent_streams() -> u32 { 256 }
fn default_chunk_size() -> usize { 1000 }
fn default_num_shards() -> usize { 16 }
fn default_worker_threads() -> usize {
    // Use number of logical cores by default, with a minimum of 4 and maximum of 64
    let cores = match num_cpus::get() {
        0 => 4, // Fallback if num_cpus returns 0 for some reason
        cores => cores,
    };
    cores.clamp(4, 64)
}
fn default_auth_enabled() -> bool { false }
fn default_auth_username() -> String { String::new() }
fn default_auth_password() -> String { String::new() }
fn default_session_duration_secs() -> u64 { 3600 } // 1 hour
fn default_auth_mode() -> String { "per_request".to_string() } // "connection_only" or "per_request"
fn default_auth_token_expiry_enabled() -> bool { true }
fn default_peer_redis_nodes() -> Vec<String> { Vec::new() }
fn default_write_consistency() -> String { "QUORUM".to_string() }
fn default_quorum_value() -> usize { 2 }
fn default_write_retry_count() -> u32 { 3 }
fn default_peer_redis_pool_size() -> usize { 64 }
fn default_site_replication_enabled() -> bool { false }
fn default_site_primary_node() -> String { String::new() }
fn default_site_failover_node() -> String { String::new() }
fn default_site_replication_retry_count() -> u32 { 3 }
fn default_site_replication_timeout_ms() -> u64 { 5000 }
fn default_site_replication_pool_size() -> usize { 256 }
fn default_use_physical_connections() -> bool { true }
fn default_rate_limiting_enabled() -> bool { false }
fn default_rate_limit_type() -> String { "per_client".to_string() } // "global", "per_client", or "both"
fn default_rate_limit_requests_per_second() -> u32 { 1000 } // 1000 RPS per client by default
fn default_rate_limit_burst_size() -> u32 { 100 } // Allow bursts up to 100 requests
fn default_rate_limit_global_requests_per_second() -> u32 { 10000 } // 10000 RPS globally by default
fn default_rate_limit_global_burst_size() -> u32 { 1000 } // Allow global bursts up to 1000 requests
fn default_rate_limit_cleanup_interval_secs() -> u64 { 300 } // Cleanup every 5 minutes
fn default_config_watch_interval_secs() -> u64 { 5 } // Check for config changes every 5 seconds
// Health check and keep-alive defaults
fn default_redis_keepalive_enabled() -> bool { true }
fn default_redis_keepalive_interval_secs() -> u64 { 30 }
fn default_redis_idle_threshold_secs() -> u64 { 60 }
fn default_secondary_nodes_keepalive_enabled() -> bool { true }
fn default_secondary_nodes_keepalive_interval_secs() -> u64 { 30 }
fn default_secondary_nodes_idle_threshold_secs() -> u64 { 60 }
fn default_site_nodes_keepalive_enabled() -> bool { true }
fn default_site_nodes_keepalive_interval_secs() -> u64 { 30 }
fn default_site_nodes_idle_threshold_secs() -> u64 { 60 }
fn default_peer_redis_keepalive_enabled() -> bool { true }
fn default_peer_redis_keepalive_interval_secs() -> u64 { 30 }
fn default_peer_redis_idle_threshold_secs() -> u64 { 60 }

impl Config {
    pub fn from_file(file_path: Option<&str>) -> Self {
        let config_file = file_path.unwrap_or("config.toml"); // Use the provided file path or default to "config.toml"
        let settings = config::Config::builder()
            .add_source(config::File::with_name(config_file).required(true))
            .build()
            .expect("Failed to load configuration file");
        let config: Config = settings
            .try_deserialize::<Config>() // Explicitly deserialize into the Config struct
            .expect("Failed to parse configuration");

        // Validate configuration
        if config.redis_url.is_empty() {
            panic!("Redis URL cannot be empty");
        } else {
            // For security, don't log the full URL if it contains authentication
            let has_auth = config.redis_url.contains('@');

            if has_auth {
                // Log Redis URL without exposing credentials
                let parts: Vec<&str> = config.redis_url.split('@').collect();
                if parts.len() > 1 {
                    let auth_part = parts[0];
                    let host_part = parts[1];

                    // Check authentication format
                    if auth_part.contains(':') {
                        let colon_count = auth_part.chars().filter(|&c| c == ':').count();

                        if colon_count > 1 {
                            // Format: redis://username:password@host:port
                            info!("Redis URL: redis://username:******@{} (username+password auth)", host_part);
                        } else if auth_part.starts_with("redis://:") {
                            // Format: redis://:password@host:port (password only)
                            info!("Redis URL: redis://:******@{} (password-only auth)", host_part);
                        } else {
                            // Format: redis://username:password@host:port
                            info!("Redis URL: redis://******:******@{} (username+password auth)", host_part);
                        }
                    } else {
                        // Format: redis://username@host:port (username only)
                        info!("Redis URL: redis://******@{} (username-only auth)", host_part);
                    }
                } else {
                    // Malformed URL but has @ symbol
                    info!("Redis URL: {} (malformed auth URL)", config.redis_url);
                }
            } else {
                // No authentication
                info!("Redis URL: {} (no authentication)", config.redis_url);
            }
        }
        if config.secondary_nodes.is_empty() {
            panic!("Secondary nodes cannot be empty");
        }else{
            info!("Secondary nodes: {:?}", config.secondary_nodes);
        }
        if config.redis_pool_size == 0 {
            panic!("Pool size must be greater than 0");
        } else{
            info!("Redis pool size: {}", config.redis_pool_size);
        }
        if config.replication_factor > config.secondary_nodes.len() {
            panic!("Replication factor cannot exceed the number of secondary nodes");
        } else{
            info!("Replication factor: {}", config.replication_factor);
        }
        if config.read_consistency > config.secondary_nodes.len() {
            panic!("Read consistency cannot exceed the number of secondary nodes");
        } else{
            info!("Read consistency: {}", config.read_consistency);
        }
        if config.max_retries == 0 {
            panic!("Max retries must be greater than 0");
        } else{
            info!("Max retries: {}", config.max_retries);
        }
        if config.retry_delay_ms == 0 {
            panic!("Retry delay must be greater than 0");
        } else {
            info!("Retry delay: {} ms", config.retry_delay_ms);
        }
        if config.replication_batch_max_age_secs == 0 {
            panic!("Replication batch max age must be greater than 0");
        } else{
            info!("Replication batch max age: {} seconds", config.replication_batch_max_age_secs);
        }
        if config.max_batch_size == 0 {
            panic!("Max batch size must be greater than 0");
        } else{
            info!("Max batch size: {}", config.max_batch_size);
        }
        if config.batch_flush_interval_ms == 0 {
            panic!("Batch flush interval must be greater than 0");
        } else{
            info!("Batch flush interval: {} ms", config.batch_flush_interval_ms);
        }
        if config.tcp_keepalive_secs == 0 {
            panic!("TCP keepalive seconds must be greater than 0");
        } else{
            info!("TCP keepalive seconds: {}", config.tcp_keepalive_secs);
        }
        if config.concurrency_limit == 0 {
            panic!("Concurrency limit must be greater than 0");
        } else{
            info!("Concurrency limit: {}", config.concurrency_limit);
        }
        if config.max_concurrent_streams == 0 {
            panic!("Max concurrent streams must be greater than 0");
        } else{
            info!("Max concurrent streams: {}", config.max_concurrent_streams);
        }
        if config.chunk_size == 0 {
            panic!("Chunk size must be greater than 0");
        } else{
            info!("Chunk size: {}", config.chunk_size);
        }
        if config.num_shards == 0 {
            panic!("Number of shards must be greater than 0");
        } else{
            info!("Number of shards: {}", config.num_shards);
        }
        if config.worker_threads == 0 {
            panic!("Worker threads must be greater than 0");
        } else{
            info!("Worker threads: {}", config.worker_threads);
        }
        if config.secondary_pool_size == 0 {
            panic!("Secondary pool size must be greater than 0");
        } else{
            info!("Secondary pool size: {}", config.secondary_pool_size);
        }
        info!("TCP nodelay: {}", config.tcp_nodelay);
        info!("Async replication: {}", config.async_replication);

        // Validate write consistency configuration
        if !config.write_consistency.is_empty() {
            match config.write_consistency.to_uppercase().as_str() {
                "ALL" | "QUORUM" | "ONE" => {
                    info!("Write consistency: {}", config.write_consistency);
                },
                _ => {
                    panic!("Invalid write_consistency value. Must be ALL, QUORUM, or ONE");
                }
            }
        }

        if config.quorum_value == 0 {
            panic!("Quorum value must be greater than 0");
        } else {
            info!("Quorum value: {}", config.quorum_value);
        }

        if config.write_retry_count == 0 {
            panic!("Write retry count must be greater than 0");
        } else {
            info!("Write retry count: {}", config.write_retry_count);
        }

        if config.peer_redis_pool_size == 0 {
            panic!("Peer Redis pool size must be greater than 0");
        } else if config.peer_redis_pool_size > 2048 {
            panic!("Peer Redis pool size cannot exceed 2048 connections per node");
        } else {
            info!("Peer Redis pool size: {}", config.peer_redis_pool_size);
        }

        if !config.peer_redis_nodes.is_empty() {
            info!("Peer Redis nodes: {:?}", config.peer_redis_nodes);

            // Validate that quorum_value doesn't exceed the number of peer nodes
            if config.write_consistency.to_uppercase() == "QUORUM" &&
               config.quorum_value > config.peer_redis_nodes.len() {
                panic!("Quorum value cannot exceed the number of peer Redis nodes");
            }
        } else {
            info!("No peer Redis nodes configured - write consistency disabled");
        }

        // Validate site replication configuration
        if config.site_replication_enabled {
            if config.site_primary_node.is_empty() && config.site_failover_node.is_empty() {
                panic!("Site replication enabled but no primary or failover nodes configured");
            }

            if config.site_replication_pool_size == 0 {
                panic!("Site replication pool size must be greater than 0");
            } else if config.site_replication_pool_size > 2048 {
                panic!("Site replication pool size cannot exceed 2048 connections per node");
            } else {
                info!("Site replication pool size: {}", config.site_replication_pool_size);
            }

            if config.site_replication_retry_count == 0 {
                panic!("Site replication retry count must be greater than 0");
            } else {
                info!("Site replication retry count: {}", config.site_replication_retry_count);
            }

            if config.site_replication_timeout_ms == 0 {
                panic!("Site replication timeout must be greater than 0");
            } else {
                info!("Site replication timeout: {} ms", config.site_replication_timeout_ms);
            }

            info!("Site replication enabled with primary: {} and failover: {}",
                  config.site_primary_node, config.site_failover_node);
        } else {
            info!("Site replication disabled");
        }

        // Validate rate limiting configuration
        if config.rate_limiting_enabled {
            // Validate rate limit type
            let valid_types = ["global", "per_client", "both"];
            if !valid_types.contains(&config.rate_limit_type.as_str()) {
                panic!("Rate limit type must be one of: global, per_client, both");
            } else {
                info!("Rate limiting enabled with type: {}", config.rate_limit_type);
            }

            // Validate per-client settings (used for per_client and both modes)
            if config.rate_limit_type == "per_client" || config.rate_limit_type == "both" {
                if config.rate_limit_requests_per_second == 0 {
                    panic!("Rate limit requests per second must be greater than 0 when per-client rate limiting is enabled");
                } else if config.rate_limit_requests_per_second > 100000 {
                    panic!("Rate limit requests per second cannot exceed 100,000 RPS per client");
                } else {
                    info!("Per-client rate limiting: {} RPS per client", config.rate_limit_requests_per_second);
                }

                if config.rate_limit_burst_size == 0 {
                    panic!("Rate limit burst size must be greater than 0 when per-client rate limiting is enabled");
                } else if config.rate_limit_burst_size > 10000 {
                    panic!("Rate limit burst size cannot exceed 10,000 requests per client");
                } else {
                    info!("Per-client rate limiting burst size: {} requests", config.rate_limit_burst_size);
                }
            }

            // Validate global settings (used for global and both modes)
            if config.rate_limit_type == "global" || config.rate_limit_type == "both" {
                if config.rate_limit_global_requests_per_second == 0 {
                    panic!("Global rate limit requests per second must be greater than 0 when global rate limiting is enabled");
                } else if config.rate_limit_global_requests_per_second > 1000000 {
                    panic!("Global rate limit requests per second cannot exceed 1,000,000 RPS");
                } else {
                    info!("Global rate limiting: {} RPS across all clients", config.rate_limit_global_requests_per_second);
                }

                if config.rate_limit_global_burst_size == 0 {
                    panic!("Global rate limit burst size must be greater than 0 when global rate limiting is enabled");
                } else if config.rate_limit_global_burst_size > 100000 {
                    panic!("Global rate limit burst size cannot exceed 100,000 requests");
                } else {
                    info!("Global rate limiting burst size: {} requests", config.rate_limit_global_burst_size);
                }
            }

            if config.rate_limit_cleanup_interval_secs < 60 {
                panic!("Rate limit cleanup interval must be at least 60 seconds");
            } else if config.rate_limit_cleanup_interval_secs > 3600 {
                panic!("Rate limit cleanup interval cannot exceed 3600 seconds (1 hour)");
            } else {
                info!("Rate limiting cleanup interval: {} seconds", config.rate_limit_cleanup_interval_secs);
            }
        } else {
            info!("Rate limiting disabled");
        }

        // Validate hot configuration reload settings
        if config.config_watch_interval_secs < 1 {
            panic!("Configuration watch interval must be at least 1 second");
        } else if config.config_watch_interval_secs > 300 {
            panic!("Configuration watch interval cannot exceed 300 seconds (5 minutes)");
        } else {
            info!("Configuration file watch interval: {} seconds", config.config_watch_interval_secs);
        }

        config
    }

    /// Decrypt sensitive configuration values using the crypto manager
    ///
    /// This method creates a new Config instance with decrypted passwords
    /// while keeping all other configuration values unchanged.
    pub fn decrypt_passwords(&self) -> Result<Self, Box<dyn std::error::Error>> {
        let crypto_manager = create_crypto_manager()?;

        // Decrypt authentication password
        let decrypted_auth_password = crypto_manager.decrypt(&self.auth_password)?;

        // Decrypt Redis URL
        let decrypted_redis_url = crypto_manager.decrypt_redis_url(&self.redis_url)?;

        // Decrypt peer Redis node URLs
        let mut decrypted_peer_redis_nodes = Vec::new();
        for node_url in &self.peer_redis_nodes {
            let decrypted_url = crypto_manager.decrypt_redis_url(node_url)?;
            decrypted_peer_redis_nodes.push(decrypted_url);
        }

        // Create a new config with decrypted values
        let mut decrypted_config = self.clone();
        decrypted_config.auth_password = decrypted_auth_password;
        decrypted_config.redis_url = decrypted_redis_url;
        decrypted_config.peer_redis_nodes = decrypted_peer_redis_nodes;

        info!("Configuration passwords decrypted successfully");
        Ok(decrypted_config)
    }

    /// Check if any passwords in the configuration are encrypted
    pub fn has_encrypted_passwords(&self) -> bool {
        CryptoManager::is_encrypted(&self.auth_password) ||
        CryptoManager::is_encrypted(&self.redis_url) ||
        self.peer_redis_nodes.iter().any(|url| CryptoManager::is_encrypted(url))
    }
}
