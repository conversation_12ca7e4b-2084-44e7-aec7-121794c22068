#!/usr/bin/env python3
"""
Rate Limiting Validation Script for RustyCluster

This script tests whether rate limiting is working correctly by:
1. Sending requests at different rates
2. Verifying that requests are allowed below the limit
3. Verifying that requests are rejected above the limit
4. Measuring actual throughput vs configured limits

Configurable parameters:
- Target RPS to test
- Expected rate limit
- Test duration
- Number of concurrent clients
"""

import grpc
import time
import sys
import argparse
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from typing import List, Tuple
import statistics

# Import the generated protobuf files
sys.path.append('load')
import rustycluster_pb2
import rustycluster_pb2_grpc

@dataclass
class TestResult:
    timestamp: float
    success: bool
    latency_ms: float
    error_code: str = ""
    error_message: str = ""

@dataclass
class TestSummary:
    total_requests: int
    successful_requests: int
    rate_limited_requests: int
    error_requests: int
    actual_rps: float
    avg_latency_ms: float
    test_duration_seconds: float
    rate_limit_triggered: bool

class RateLimitTester:
    def __init__(self, host='localhost', port=50051, username='testuser', password='testpass'):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.session_token = None
        
    def authenticate(self):
        """Authenticate and get session token"""
        channel = grpc.insecure_channel(f'{self.host}:{self.port}')
        stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
        
        try:
            auth_request = rustycluster_pb2.AuthenticateRequest(
                username=self.username,
                password=self.password
            )
            
            auth_response = stub.Authenticate(auth_request, timeout=10.0)
            if auth_response.success:
                self.session_token = auth_response.session_token
                print(f"✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {auth_response.message}")
                return False
        except grpc.RpcError as e:
            print(f"❌ Authentication error: {e.details()}")
            return False
        finally:
            channel.close()
    
    def make_request(self, request_id: int) -> TestResult:
        """Make a single SET request and return result"""
        channel = grpc.insecure_channel(f'{self.host}:{self.port}')
        stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
        
        start_time = time.time()
        
        try:
            metadata = [('authorization', f'Bearer {self.session_token}')]
            request = rustycluster_pb2.SetRequest(
                key=f"rate_test_{request_id}_{int(time.time() * 1000)}",
                value=f"test_value_{request_id}",
                skip_replication=False
            )
            
            response = stub.Set(request, metadata=metadata, timeout=5.0)
            end_time = time.time()
            
            latency_ms = (end_time - start_time) * 1000
            
            if response.success:
                return TestResult(
                    timestamp=start_time,
                    success=True,
                    latency_ms=latency_ms
                )
            else:
                return TestResult(
                    timestamp=start_time,
                    success=False,
                    latency_ms=latency_ms,
                    error_code="REQUEST_FAILED",
                    error_message=response.message
                )
                
        except grpc.RpcError as e:
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            
            if e.code() == grpc.StatusCode.RESOURCE_EXHAUSTED:
                return TestResult(
                    timestamp=start_time,
                    success=False,
                    latency_ms=latency_ms,
                    error_code="RATE_LIMITED",
                    error_message=e.details()
                )
            else:
                return TestResult(
                    timestamp=start_time,
                    success=False,
                    latency_ms=latency_ms,
                    error_code=str(e.code()),
                    error_message=e.details()
                )
        finally:
            channel.close()
    
    def test_rate_limiting(self, target_rps: int, expected_limit_rps: int, 
                          test_duration_seconds: int, num_clients: int = 1) -> TestSummary:
        """
        Test rate limiting by sending requests at target_rps and checking if 
        rate limiting kicks in when exceeding expected_limit_rps
        """
        print(f"\n🧪 Testing Rate Limiting")
        print(f"Target RPS: {target_rps}")
        print(f"Expected Rate Limit: {expected_limit_rps} RPS")
        print(f"Test Duration: {test_duration_seconds} seconds")
        print(f"Concurrent Clients: {num_clients}")
        print("=" * 60)
        
        # Calculate request interval
        request_interval = 1.0 / target_rps if target_rps > 0 else 0.1
        total_requests = target_rps * test_duration_seconds
        
        results = []
        start_time = time.time()
        
        def worker_thread(client_id: int, requests_per_client: int):
            """Worker thread for sending requests"""
            thread_results = []
            
            for i in range(requests_per_client):
                request_start = time.time()
                request_id = client_id * 10000 + i
                
                result = self.make_request(request_id)
                thread_results.append(result)
                
                # Print progress for first client
                if client_id == 0 and (i + 1) % max(1, requests_per_client // 10) == 0:
                    progress = ((i + 1) / requests_per_client) * 100
                    print(f"Progress: {progress:.1f}% - Last result: {'✅' if result.success else '🚫'} "
                          f"({result.error_code if not result.success else 'OK'})")
                
                # Sleep to maintain target rate (per client)
                elapsed = time.time() - request_start
                sleep_time = max(0, request_interval * num_clients - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)
            
            return thread_results
        
        # Distribute requests across clients
        requests_per_client = total_requests // num_clients
        remaining_requests = total_requests % num_clients
        
        # Use ThreadPoolExecutor for concurrent clients
        with ThreadPoolExecutor(max_workers=num_clients) as executor:
            futures = []
            
            for client_id in range(num_clients):
                client_requests = requests_per_client
                if client_id < remaining_requests:
                    client_requests += 1
                
                future = executor.submit(worker_thread, client_id, client_requests)
                futures.append(future)
            
            # Collect results from all threads
            for future in as_completed(futures):
                thread_results = future.result()
                results.extend(thread_results)
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        # Analyze results
        successful_requests = sum(1 for r in results if r.success)
        rate_limited_requests = sum(1 for r in results if r.error_code == "RATE_LIMITED")
        error_requests = sum(1 for r in results if not r.success and r.error_code != "RATE_LIMITED")
        
        actual_rps = len(results) / actual_duration if actual_duration > 0 else 0
        avg_latency = statistics.mean(r.latency_ms for r in results) if results else 0
        
        # Determine if rate limiting was triggered appropriately
        rate_limit_triggered = rate_limited_requests > 0
        
        return TestSummary(
            total_requests=len(results),
            successful_requests=successful_requests,
            rate_limited_requests=rate_limited_requests,
            error_requests=error_requests,
            actual_rps=actual_rps,
            avg_latency_ms=avg_latency,
            test_duration_seconds=actual_duration,
            rate_limit_triggered=rate_limit_triggered
        )
    
    def print_summary(self, summary: TestSummary, target_rps: int, expected_limit_rps: int):
        """Print test summary and analysis"""
        print(f"\n📊 Test Results Summary")
        print("=" * 60)
        print(f"Total Requests Sent: {summary.total_requests}")
        print(f"Successful Requests: {summary.successful_requests}")
        print(f"Rate Limited Requests: {summary.rate_limited_requests}")
        print(f"Other Errors: {summary.error_requests}")
        print(f"Actual RPS Achieved: {summary.actual_rps:.1f}")
        print(f"Average Latency: {summary.avg_latency_ms:.1f}ms")
        print(f"Test Duration: {summary.test_duration_seconds:.1f}s")
        
        print(f"\n🔍 Rate Limiting Analysis")
        print("=" * 60)
        
        success_rate = (summary.successful_requests / summary.total_requests) * 100 if summary.total_requests > 0 else 0
        rate_limit_rate = (summary.rate_limited_requests / summary.total_requests) * 100 if summary.total_requests > 0 else 0
        
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Rate Limit Rate: {rate_limit_rate:.1f}%")
        
        # Analysis
        if target_rps <= expected_limit_rps:
            # Should NOT be rate limited
            if summary.rate_limit_triggered:
                print(f"⚠️  UNEXPECTED: Rate limiting triggered when target RPS ({target_rps}) <= expected limit ({expected_limit_rps})")
                print(f"   This suggests the rate limit is set lower than expected or there's an issue")
            else:
                print(f"✅ EXPECTED: No rate limiting when target RPS ({target_rps}) <= expected limit ({expected_limit_rps})")
        else:
            # SHOULD be rate limited
            if summary.rate_limit_triggered:
                print(f"✅ EXPECTED: Rate limiting triggered when target RPS ({target_rps}) > expected limit ({expected_limit_rps})")
                effective_rps = summary.successful_requests / summary.test_duration_seconds
                print(f"   Effective allowed RPS: {effective_rps:.1f}")
            else:
                print(f"⚠️  UNEXPECTED: No rate limiting when target RPS ({target_rps}) > expected limit ({expected_limit_rps})")
                print(f"   This suggests rate limiting is disabled or the limit is higher than expected")

def main():
    parser = argparse.ArgumentParser(description='Test RustyCluster Rate Limiting')
    parser.add_argument('--host', default='localhost', help='RustyCluster host')
    parser.add_argument('--port', type=int, default=50051, help='RustyCluster port')
    parser.add_argument('--username', default='testuser', help='Username for authentication')
    parser.add_argument('--password', default='testpass', help='Password for authentication')
    
    # Test configuration
    parser.add_argument('--target-rps', type=int, default=1200, 
                       help='Target RPS to send (default: 1200)')
    parser.add_argument('--expected-limit', type=int, default=1000, 
                       help='Expected rate limit in RPS (default: 1000)')
    parser.add_argument('--duration', type=int, default=10, 
                       help='Test duration in seconds (default: 10)')
    parser.add_argument('--clients', type=int, default=5, 
                       help='Number of concurrent clients (default: 5)')
    
    args = parser.parse_args()
    
    print("🧪 RustyCluster Rate Limiting Validation Test")
    print(f"Target: {args.host}:{args.port}")
    print("=" * 60)
    
    tester = RateLimitTester(args.host, args.port, args.username, args.password)
    
    # Authenticate
    if not tester.authenticate():
        print("❌ Authentication failed. Cannot proceed with rate limiting test.")
        return 1
    
    try:
        # Run the rate limiting test
        summary = tester.test_rate_limiting(
            target_rps=args.target_rps,
            expected_limit_rps=args.expected_limit,
            test_duration_seconds=args.duration,
            num_clients=args.clients
        )
        
        # Print results and analysis
        tester.print_summary(summary, args.target_rps, args.expected_limit)
        
        # Determine overall test result
        if args.target_rps <= args.expected_limit:
            # Should not be rate limited
            test_passed = not summary.rate_limit_triggered
        else:
            # Should be rate limited
            test_passed = summary.rate_limit_triggered
        
        if test_passed:
            print(f"\n✅ Rate Limiting Test PASSED")
            return 0
        else:
            print(f"\n❌ Rate Limiting Test FAILED")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
