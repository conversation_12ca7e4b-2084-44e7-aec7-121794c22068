#!/usr/bin/env python3
"""
Fast Rate Limiting Test for RustyCluster
Optimized for quick testing with shorter durations and burst testing
"""

import grpc
import time
import sys
import argparse
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
import statistics

# Import the generated protobuf files
sys.path.append('load')
import rustycluster_pb2
import rustycluster_pb2_grpc

@dataclass
class TestResult:
    timestamp: float
    success: bool
    latency_ms: float
    error_code: str = ""

class FastRateLimitTester:
    def __init__(self, host='localhost', port=50051, username='testuser', password='testpass'):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.session_token = None
        
    def authenticate(self):
        """Authenticate and get session token"""
        channel = grpc.insecure_channel(f'{self.host}:{self.port}')
        stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
        
        try:
            auth_request = rustycluster_pb2.AuthenticateRequest(
                username=self.username,
                password=self.password
            )
            
            auth_response = stub.Authenticate(auth_request, timeout=10.0)
            if auth_response.success:
                self.session_token = auth_response.session_token
                print(f"✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {auth_response.message}")
                return False
        except grpc.RpcError as e:
            print(f"❌ Authentication error: {e.details()}")
            return False
        finally:
            channel.close()
    
    def make_request(self, request_id: int) -> TestResult:
        """Make a single SET request"""
        channel = grpc.insecure_channel(f'{self.host}:{self.port}')
        stub = rustycluster_pb2_grpc.KeyValueServiceStub(channel)
        
        start_time = time.time()
        
        try:
            metadata = [('authorization', f'Bearer {self.session_token}')]
            request = rustycluster_pb2.SetRequest(
                key=f"fast_test_{request_id}_{int(time.time() * 1000)}",
                value=f"test_value_{request_id}",
                skip_replication=False
            )
            
            response = stub.Set(request, metadata=metadata, timeout=3.0)
            end_time = time.time()
            
            latency_ms = (end_time - start_time) * 1000
            
            if response.success:
                return TestResult(start_time, True, latency_ms)
            else:
                return TestResult(start_time, False, latency_ms, "REQUEST_FAILED")
                
        except grpc.RpcError as e:
            end_time = time.time()
            latency_ms = (end_time - start_time) * 1000
            
            if e.code() == grpc.StatusCode.RESOURCE_EXHAUSTED:
                return TestResult(start_time, False, latency_ms, "RATE_LIMITED")
            else:
                return TestResult(start_time, False, latency_ms, str(e.code()))
        finally:
            channel.close()
    
    def burst_test(self, num_requests: int, num_clients: int) -> tuple:
        """Send a burst of requests as fast as possible"""
        print(f"🚀 Sending {num_requests} requests with {num_clients} clients...")
        
        results = []
        start_time = time.time()
        
        def worker(start_id: int, count: int):
            worker_results = []
            for i in range(count):
                result = self.make_request(start_id + i)
                worker_results.append(result)
                
                # Quick progress indicator
                if i % max(1, count // 5) == 0:
                    status = "✅" if result.success else "🚫"
                    print(f"  Client {start_id//1000}: {status} ({result.error_code if not result.success else 'OK'})")
            
            return worker_results
        
        # Distribute requests across clients
        requests_per_client = num_requests // num_clients
        remaining = num_requests % num_clients
        
        with ThreadPoolExecutor(max_workers=num_clients) as executor:
            futures = []
            current_id = 0
            
            for i in range(num_clients):
                count = requests_per_client + (1 if i < remaining else 0)
                future = executor.submit(worker, current_id, count)
                futures.append(future)
                current_id += 1000  # Space out IDs
            
            for future in as_completed(futures):
                results.extend(future.result())
        
        end_time = time.time()
        duration = end_time - start_time
        
        return results, duration
    
    def sustained_test(self, target_rps: int, duration_seconds: int) -> tuple:
        """Send requests at a sustained rate"""
        print(f"⏱️  Sending {target_rps} RPS for {duration_seconds} seconds...")
        
        results = []
        start_time = time.time()
        end_time = start_time + duration_seconds
        request_interval = 1.0 / target_rps
        
        request_id = 0
        while time.time() < end_time:
            loop_start = time.time()
            
            result = self.make_request(request_id)
            results.append(result)
            request_id += 1
            
            # Progress indicator
            if request_id % max(1, target_rps // 4) == 0:
                elapsed = time.time() - start_time
                progress = (elapsed / duration_seconds) * 100
                status = "✅" if result.success else "🚫"
                print(f"  Progress: {progress:.1f}% - {status} ({result.error_code if not result.success else 'OK'})")
            
            # Sleep to maintain rate
            elapsed = time.time() - loop_start
            sleep_time = max(0, request_interval - elapsed)
            if sleep_time > 0:
                time.sleep(sleep_time)
        
        actual_duration = time.time() - start_time
        return results, actual_duration
    
    def analyze_results(self, results: list, duration: float, test_name: str, expected_limit: int):
        """Analyze and print test results"""
        if not results:
            print("❌ No results to analyze")
            return False
        
        successful = sum(1 for r in results if r.success)
        rate_limited = sum(1 for r in results if r.error_code == "RATE_LIMITED")
        errors = len(results) - successful - rate_limited
        
        actual_rps = len(results) / duration
        success_rate = (successful / len(results)) * 100
        rate_limit_rate = (rate_limited / len(results)) * 100
        avg_latency = statistics.mean(r.latency_ms for r in results)
        
        print(f"\n📊 {test_name} Results")
        print("=" * 50)
        print(f"Total Requests: {len(results)}")
        print(f"Successful: {successful} ({success_rate:.1f}%)")
        print(f"Rate Limited: {rate_limited} ({rate_limit_rate:.1f}%)")
        print(f"Other Errors: {errors}")
        print(f"Actual RPS: {actual_rps:.1f}")
        print(f"Average Latency: {avg_latency:.1f}ms")
        print(f"Duration: {duration:.1f}s")
        
        # Determine if rate limiting is working as expected
        effective_rps = successful / duration
        
        if actual_rps <= expected_limit * 1.1:  # 10% tolerance
            if rate_limited == 0:
                print(f"✅ PASS: No rate limiting below/at limit ({actual_rps:.1f} <= {expected_limit})")
                return True
            else:
                print(f"⚠️  UNEXPECTED: Rate limiting triggered below limit ({actual_rps:.1f} <= {expected_limit})")
                return False
        else:
            if rate_limited > 0:
                print(f"✅ PASS: Rate limiting triggered above limit (effective: {effective_rps:.1f} RPS)")
                return True
            else:
                print(f"⚠️  FAIL: No rate limiting above limit ({actual_rps:.1f} > {expected_limit})")
                return False

def main():
    parser = argparse.ArgumentParser(description='Fast Rate Limiting Test for RustyCluster')
    parser.add_argument('--host', default='localhost', help='RustyCluster host')
    parser.add_argument('--port', type=int, default=50051, help='RustyCluster port')
    parser.add_argument('--username', default='testuser', help='Username')
    parser.add_argument('--password', default='testpass', help='Password')
    parser.add_argument('--expected-limit', type=int, default=1000, help='Expected rate limit (default: 1000)')
    parser.add_argument('--test', choices=['burst', 'sustained', 'both'], default='both', help='Test type')
    
    args = parser.parse_args()
    
    print("⚡ Fast Rate Limiting Test for RustyCluster")
    print(f"Target: {args.host}:{args.port}")
    print(f"Expected Rate Limit: {args.expected_limit} RPS")
    print("=" * 60)
    
    tester = FastRateLimitTester(args.host, args.port, args.username, args.password)
    
    if not tester.authenticate():
        return 1
    
    try:
        all_passed = True
        
        if args.test in ['burst', 'both']:
            print(f"\n🚀 BURST TEST - Testing burst capacity")
            print("-" * 40)
            
            # Test 1: Small burst (should succeed)
            results, duration = tester.burst_test(num_requests=100, num_clients=5)
            passed = tester.analyze_results(results, duration, "Small Burst (100 requests)", args.expected_limit)
            all_passed = all_passed and passed
            
            time.sleep(2)  # Brief pause
            
            # Test 2: Large burst (should trigger rate limiting)
            results, duration = tester.burst_test(num_requests=500, num_clients=10)
            passed = tester.analyze_results(results, duration, "Large Burst (500 requests)", args.expected_limit)
            # For burst test, we expect rate limiting on large bursts
            
        if args.test in ['sustained', 'both']:
            print(f"\n⏱️  SUSTAINED TEST - Testing sustained rates")
            print("-" * 40)
            
            # Test 3: Below limit (should succeed)
            below_limit = int(args.expected_limit * 0.8)  # 80% of limit
            results, duration = tester.sustained_test(target_rps=below_limit, duration_seconds=5)
            passed = tester.analyze_results(results, duration, f"Below Limit ({below_limit} RPS)", args.expected_limit)
            all_passed = all_passed and passed
            
            time.sleep(2)  # Brief pause
            
            # Test 4: Above limit (should trigger rate limiting)
            above_limit = int(args.expected_limit * 1.2)  # 120% of limit
            results, duration = tester.sustained_test(target_rps=above_limit, duration_seconds=5)
            passed = tester.analyze_results(results, duration, f"Above Limit ({above_limit} RPS)", args.expected_limit)
            # For above limit, we expect rate limiting
        
        print(f"\n{'✅ All tests completed!' if all_passed else '⚠️  Some tests had unexpected results'}")
        return 0 if all_passed else 1
        
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted")
        return 1
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
