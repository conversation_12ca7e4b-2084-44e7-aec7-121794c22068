# Hot reload test configuration for RustyCluster
# This configuration is designed to test hot configuration reload functionality

# Redis connection URL for the primary node
redis_url = "redis://settlenxt:npci@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to
secondary_nodes = ["http://127.0.0.1:50052"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 512

# Number of secondary nodes to which data should be replicated
replication_factor = 1

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# If true, replication to secondary nodes happens asynchronously
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 100

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 256

# Maximum number of operations in a batch
max_batch_size = 1000

# Interval in milliseconds to flush batches
batch_flush_interval_ms = 100

# TCP keepalive settings
tcp_keepalive_secs = 30
tcp_nodelay = true

# Concurrency settings
concurrency_limit = 256
max_concurrent_streams = 1000

# Chunk and shard settings
chunk_size = 1000
num_shards = 16

# Worker threads (auto-detected based on CPU cores)
worker_threads = 8

# Authentication configuration (disabled for testing)
auth_enabled = false
auth_username = ""
auth_password = ""
session_duration_secs = 3600
auth_mode = "per_request"
auth_token_expiry_enabled = true

# Write consistency configuration (disabled for testing)
peer_redis_nodes = []
write_consistency = "QUORUM"
quorum_value = 2
write_retry_count = 3
peer_redis_pool_size = 64

# Site replication configuration (disabled for testing)
site_replication_enabled = false
site_primary_node = ""
site_failover_node = ""
site_replication_retry_count = 3
site_replication_timeout_ms = 5000
site_replication_pool_size = 256

# Health check configuration
redis_keepalive_enabled = true
redis_keepalive_interval_secs = 30
redis_idle_threshold_secs = 60
secondary_nodes_keepalive_enabled = true
secondary_nodes_keepalive_interval_secs = 30
secondary_nodes_idle_threshold_secs = 60
site_nodes_keepalive_enabled = false
site_nodes_keepalive_interval_secs = 30
site_nodes_idle_threshold_secs = 60
peer_redis_keepalive_enabled = false
peer_redis_keepalive_interval_secs = 30
peer_redis_idle_threshold_secs = 60

# Performance settings
use_physical_connections = true

# RATE LIMITING CONFIGURATION - HOT RELOADABLE
# These settings can be changed at runtime without restarting the server

# Enable rate limiting (can be toggled at runtime)
rate_limiting_enabled = true

# Rate limiting type: "global", "per_client", or "both" (can be changed at runtime)
rate_limit_type = "per_client"

# Per-client rate limiting settings (hot reloadable)
rate_limit_requests_per_second = 100  # Start with conservative limit
rate_limit_burst_size = 20            # Allow small bursts

# Global rate limiting settings (hot reloadable)
rate_limit_global_requests_per_second = 1000  # Global limit
rate_limit_global_burst_size = 200            # Global burst size

# Cleanup interval for expired clients (hot reloadable)
rate_limit_cleanup_interval_secs = 300  # 5 minutes

# HOT CONFIGURATION RELOAD SETTINGS
# How often to check for configuration file changes (hot reloadable)
config_watch_interval_secs = 5  # Check every 5 seconds (can be 1-300 seconds)
