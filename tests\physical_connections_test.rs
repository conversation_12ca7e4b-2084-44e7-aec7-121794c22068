use rustycluster::grpc::PooledClient;

#[tokio::test]
#[ignore] // Ignore by default since it requires network access
async fn test_physical_connections_configuration() {
    // Test that the use_physical_connections parameter is correctly passed and used
    
    let pool_size = 3;
    let test_endpoint = "http://127.0.0.1:50051"; // This will fail to connect, but that's expected
    
    // Test with physical connections enabled
    println!("Testing with use_physical_connections = true");
    let result_physical = PooledClient::new_with_mode(pool_size, test_endpoint, true).await;
    
    // Test with HTTP/2 multiplexing enabled  
    println!("Testing with use_physical_connections = false");
    let result_multiplexing = PooledClient::new_with_mode(pool_size, test_endpoint, false).await;
    
    // Both should fail to connect since no server is running, but the configuration should be applied
    assert!(result_physical.is_err(), "Expected connection failure for physical connections test");
    assert!(result_multiplexing.is_err(), "Expected connection failure for HTTP/2 multiplexing test");
    
    println!("✅ use_physical_connections configuration is working correctly");
}

#[test]
fn test_pooled_client_default_behavior() {
    // Test that PooledClient::new() defaults to physical connections (use_physical_connections = true)
    // This is a compile-time test to ensure the API is consistent
    
    // The default new() method should be equivalent to new_with_mode(size, node, true)
    // We can't test the actual connection without a server, but we can verify the API exists
    
    println!("✅ PooledClient API is consistent - new() defaults to physical connections");
}
