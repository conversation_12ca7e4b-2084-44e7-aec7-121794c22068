# Minimal configuration without rate limiting for debugging
# Redis connection URL for the primary node
redis_url = "redis://settlenxt:npci@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to (Rust nodes)
secondary_nodes = ["http://127.0.0.1:50052"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 1024

# Number of secondary nodes to which data should be replicated
replication_factor = 1

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# IMPORTANT: Set to true to enable site replication (async replication required for site replication)
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 100

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 512

# Maximum number of operations in a batch
max_batch_size = 50000

# Interval in milliseconds to flush batches
batch_flush_interval_ms = 200

# Authentication configuration
auth_enabled = true
auth_username = "testuser"
auth_password = "testpass"
session_duration_secs = 3600
auth_mode = "connection_only"
auth_token_expiry_enabled = false

# Write consistency configuration (not used when async_replication = true)
peer_redis_nodes = []
write_consistency = "QUORUM"
quorum_value = 2
write_retry_count = 3
peer_redis_pool_size = 64

# Site-based replication configuration
site_replication_enabled = true
site_replication_pool_size = 1024
site_primary_node = "http://127.0.0.1:50053"
site_failover_node = "http://127.0.0.1:50054"
site_replication_retry_count = 3
site_replication_timeout_ms = 1000

use_physical_connections = false

# Performance tuning parameters
tcp_keepalive_secs = 30
tcp_nodelay = true
concurrency_limit = 1024
max_concurrent_streams = 8192
chunk_size = 10000
num_shards = 128
worker_threads = 32

# Health check configuration
redis_keepalive_enabled = true
redis_keepalive_interval_secs = 30
secondary_nodes_keepalive_enabled = true
secondary_nodes_keepalive_interval_secs = 30
site_nodes_keepalive_enabled = true
site_nodes_keepalive_interval_secs = 30
peer_redis_keepalive_enabled = true
peer_redis_keepalive_interval_secs = 30

# RATE LIMITING COMPLETELY DISABLED
rate_limiting_enabled = false
